package config

import (
	"log"
	"pebble/pkg/mandrill"
	"pebble/pkg/paystream"
	"pebble/pkg/uconfig"
	"pebble/pkg/udb"
	"pebble/pkg/ujwt"
	"pebble/pkg/ulog"
	"	"pebble/pkg/utwilio"
	"pebble/pkg/uwebsocket""
)

// Config represents the application configuration
type Config struct {
	App       AppConfig          `mapstructure:"app"`
	Database  udb.DatabaseConfig `mapstructure:"database"`
	Log       ulog.LogConfig     `mapstructure:"log"`
	JWT       ujwt.JWTConfig     `mapstructure:"jwt"`
	Twilio    utwilio.Config     `mapstructure:"twilio"`
	Mandrill  mandrill.Config    `mapstructure:"mandrill"`
	Paystream paystream.Config   `mapstructure:"paystream"`
}

// AppConfig represents the application configuration
type AppConfig struct {
	Name              string `mapstructure:"name"`
	Env               string `mapstructure:"env"`
	Mode              string `mapstructure:"mode"`
	Port              int    `mapstructure:"port"`
	Domain            string `mapstructure:"domain"`
	InternalApiSecret string `mapstructure:"internal_api_secret"`
}

// Global config instance
var C Config

// Init initializes the configuration
func Init() *Config {
	// Parse command-line flags to get config path and environment.
	configPath, env := uconfig.ParseFlags()

	// Create a new configuration loader.
	// The env prefix allows overriding config values with environment variables
	// like WEBAPP_APP_PORT=8082
	loader := uconfig.New(
		uconfig.WithEnvPrefix("WEBAPP"),
		uconfig.WithLogger(log.Default()), // Use standard logger for messages
	)

	// Load the configuration.
	// The loader will handle finding the file, merging env-specific versions,
	// and validating the final struct.
	if err := loader.Load(configPath, env, &C); err != nil {
		// Using panic here because the application cannot run without a valid config.
		panic(err)
	}

	// If env was provided via flag but not set in config, update it.
	// This is useful for ensuring the app environment context is always set if specified.
	if env != "" && C.App.Env == "" {
		C.App.Env = env
	}

	return &C
}

// GetConfig returns the global configuration instance
func GetConfig() *Config {
	return &C
}

// Implement ConfigProvider interfaces
func (c *Config) GetAppName() string {
	return c.App.Name
}

func (c *Config) GetAppEnv() string {
	return c.App.Env
}

func (c *Config) GetAppMode() string {
	return c.App.Mode
}

func (c *Config) GetServerPort() int {
	return c.App.Port
}

func (c *Config) GetServerDomain() string {
	return c.App.Domain
}

func (c *Config) GetDatabaseConfig() interface{} {
	return &c.Database
}

func (c *Config) GetLogConfig() interface{} {
	return &c.Log
}
