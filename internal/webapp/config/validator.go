package config

import (
	"fmt"
	"strings"
)

// Validate validates the configuration
func (c *Config) Validate() error {
	var errors []string

	// Validate App configuration
	if c.App.Name == "" {
		errors = append(errors, "app name is required")
	}
	if c.App.Port <= 0 || c.App.Port > 65535 {
		errors = append(errors, "invalid app port")
	}
	if c.App.Mode != "debug" && c.App.Mode != "release" && c.App.Mode != "test" {
		errors = append(errors, "invalid app mode (must be debug, release, or test)")
	}

	// Validate Database configuration
	if c.Database.Host == "" {
		errors = append(errors, "database host is required")
	}
	if c.Database.Port <= 0 || c.Database.Port > 65535 {
		errors = append(errors, "invalid database port")
	}
	if c.Database.Username == "" {
		errors = append(errors, "database username is required")
	}
	if c.Database.Password == "" {
		errors = append(errors, "database password is required")
	}
	if c.Database.Name == "" {
		errors = append(errors, "database name is required")
	}
	if c.Database.MaxConn <= 0 {
		errors = append(errors, "invalid database max connections")
	}
	if c.Database.MaxIdleConn <= 0 {
		errors = append(errors, "invalid database max idle connections")
	}

	// Validate Log configuration
	if c.Log.Path == "" {
		errors = append(errors, "log path is required")
	}
	if c.Log.MaxSize <= 0 {
		errors = append(errors, "invalid log max size")
	}
	if c.Log.MaxBackups < 0 {
		errors = append(errors, "invalid log max backups")
	}
	if c.Log.MaxAge < 0 {
		errors = append(errors, "invalid log max age")
	}

	// Validate JWT configuration
	if c.JWT.Secret == "" {
		errors = append(errors, "jwt secret is required")
	}
	if c.JWT.Issuer == "" {
		errors = append(errors, "jwt issuer is required")
	}
	if c.JWT.AccessTokenExpire <= 0 {
		errors = append(errors, "invalid jwt access token expire")
	}
	if c.JWT.RefreshTokenExpire <= 0 {
		errors = append(errors, "invalid jwt refresh token expire")
	}
	if c.JWT.RefreshTokenExpire <= c.JWT.AccessTokenExpire {
		errors = append(errors, "refresh token expire must be greater than access token expire")
	}

	if len(errors) > 0 {
		return fmt.Errorf("configuration validation failed:\n- %s", strings.Join(errors, "\n- "))
	}

	return nil
}
