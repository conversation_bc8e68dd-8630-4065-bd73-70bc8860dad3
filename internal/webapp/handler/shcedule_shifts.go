package handler

import (
	"pebble/internal/webapp/services"
	"pebble/internal/webapp/types"
	"pebble/pkg/uerror"
	"pebble/pkg/ugin/writer"

	"github.com/gin-gonic/gin"
)

func QueryAvailableScheduleShifts(c *gin.Context) {
	var params types.QueryAvailableScheduleShiftsParams
	if err := c.Bind<PERSON>uery(&params); err != nil {
		writer.ResponseErr(c, uerror.Wrap(uerror.ErrInvalidParam, uerror.ErrCodeInvalidParam, "%v", err), nil)
		return
	}

	resp, err := services.ScheduleShiftsSvc().QueryAvailableScheduleShifts(c.Request.Context(), params)
	if err != nil {
		writer.ResponseErr(c, err, nil)
		return
	}

	writer.ResponseOk(c, writer.List(resp))
}

func QueryStaffScheduleShifts(c *gin.Context) {
	var params types.QueryStaffScheduleShiftsParams
	if err := c.BindQ<PERSON>y(&params); err != nil {
		writer.ResponseErr(c, uerror.Wrap(uerror.ErrInvalidParam, uerror.ErrCodeInvalidParam, "%v", err), nil)
		return
	}

	resp, err := services.ScheduleShiftsSvc().QueryStaffScheduleShifts(c.Request.Context(), params)
	if err != nil {
		writer.ResponseErr(c, err, nil)
		return
	}

	writer.ResponseOk(c, writer.List(resp))
}
