package handler

import (
	"pebble/internal/webapp/services"
	"pebble/internal/webapp/types"
	"pebble/pkg/uerror"
	"pebble/pkg/ugin/writer"

	"github.com/gin-gonic/gin"
)

// StaffSchedulesHours godoc
// @Summary      Get staff schedule hours
// @Description  Retrieve the working hours schedule for all staff members including their weekly working time configuration
// @Tags         working-schedules
// @Accept       json
// @Produce      json
// @Success      200  {object}  writer.Resp{data=object{list=[]types.StaffScheduleHours}}
// @Failure      400  {object}  writer.ErrResp
// @Failure      401  {object}  writer.ErrResp
// @Failure      500  {object}  writer.ErrResp
// @Router       /working-schedules [get]
// @Security     ApiKeyAuth
// @Example      Success Response:
//
//	{
//	  "success": true,
//	  "data": {
//	    "list": [
//	      {
//	        "account_id": "acc_123456",
//	        "first_name": "<PERSON>",
//	        "last_name": "<PERSON><PERSON>",
//	        "is_owner": true,
//	        "schedule": [
//	          {
//	            "weekday": 0,
//	            "working_times": [{"start_time": 32400, "end_time": 64800}],
//	            "is_day_off": false
//	          }
//	        ]
//	      }
//	    ]
//	  },
//	  "message": "",
//	  "code": 0
//	}
//
// @Example      Error Response:
//
//	{
//	  "success": false,
//	  "data": {},
//	  "message": "Authentication required",
//	  "code": 40100
//	}
func StaffSchedulesHours(c *gin.Context) {
	resp, err := services.StaffScheduleSvc().ScheduleHours(c.Request.Context())
	if err != nil {
		writer.ResponseErr(c, err, nil)
		return
	}

	writer.ResponseOk(c, writer.List(resp))
}

// BatchUpdateStaffSchedule godoc
// @Summary      Batch update staff schedules
// @Description  Update multiple staff schedules for specific weekdays. Weekday values: 0=Sunday, 1=Monday, 2=Tuesday, 3=Wednesday, 4=Thursday, 5=Friday, 6=Saturday. Working times are in seconds from midnight (e.g., 32400=9:00 AM, 64800=6:00 PM).
// @Tags         working-schedules
// @Accept       json
// @Produce      json
// @Param        request  body      types.BatchUpdateStaffScheduleReq  true  "Staff schedule update information"
// @Success      200      {object}  writer.Resp{data=object{}}
// @Failure      400      {object}  writer.ErrResp
// @Failure      401      {object}  writer.ErrResp
// @Failure      500      {object}  writer.ErrResp
// @Router       /working-schedules [put]
// @Security     ApiKeyAuth
// @Example      Success Response:
//
//	{
//	  "success": true,
//	  "data": {},
//	  "message": "",
//	  "code": 0
//	}
//
// @Example      Error Response:
//
//	{
//	  "success": false,
//	  "data": {},
//	  "message": "Invalid weekday value",
//	  "code": 40000
//	}
func BatchUpdateStaffSchedule(c *gin.Context) {
	var req types.BatchUpdateStaffScheduleReq
	if err := c.ShouldBindJSON(&req); err != nil {
		writer.ResponseErr(c, err, nil)
		return
	}

	err := services.StaffScheduleSvc().BatchUpdate(c.Request.Context(), req)
	if err != nil {
		writer.ResponseErr(c, err, nil)
	}

	writer.ResponseOk(c, writer.Empty())
}

// ResetStaffSchedule godoc
// @Summary      Reset staff schedules
// @Description  Reset staff schedules to default working hours (Monday-Friday 9:00 AM to 6:00 PM, weekends off). This will override any existing custom schedules for the specified staff members.
// @Tags         working-schedules
// @Accept       json
// @Produce      json
// @Param        request  body      types.ResetStaffScheduleReq  true  "Staff IDs to reset schedules for"
// @Success      200      {object}  writer.Resp{data=object{}}
// @Failure      400      {object}  writer.ErrResp
// @Failure      401      {object}  writer.ErrResp
// @Failure      500      {object}  writer.ErrResp
// @Router       /working-schedules/reset [post]
// @Security     ApiKeyAuth
// @Example      Success Response:
//
//	{
//	  "success": true,
//	  "data": {},
//	  "message": "",
//	  "code": 0
//	}
//
// @Example      Error Response:
//
//	{
//	  "success": false,
//	  "data": {},
//	  "message": "Staff account not found",
//	  "code": 40400
//	}
func ResetStaffSchedule(c *gin.Context) {
	var req types.ResetStaffScheduleReq
	if err := c.ShouldBindJSON(&req); err != nil {
		writer.ResponseErr(c, err, nil)
		return
	}

	err := services.StaffScheduleSvc().Reset(c.Request.Context(), req)
	if err != nil {
		writer.ResponseErr(c, err, nil)
	}

	writer.ResponseOk(c, writer.Empty())
}

// QueryStaffSchedulesDate godoc
// @Summary      Query staff schedules by date
// @Description  Get staff schedules for a specific date range and staff member, including any schedule overrides for specific dates. Returns a map where keys are dates (YYYY-MM-DD) and values contain the schedule details for that date.
// @Tags         working-schedules
// @Accept       json
// @Produce      json
// @Param        start_date  query     string  true   "Start date (YYYY-MM-DD format)"
// @Param        end_date    query     string  true   "End date (YYYY-MM-DD format)"
// @Param        account_id  query     string  true   "Staff account ID"
// @Success      200         {object}  writer.Resp{data=object{obj=map[string]types.QueryStaffSchedulesDateResp}}
// @Failure      400         {object}  writer.ErrResp
// @Failure      401         {object}  writer.ErrResp
// @Failure      500         {object}  writer.ErrResp
// @Router       /working-schedules/dates [get]
// @Security     ApiKeyAuth
// @Example      Success Response:
//
//	{
//	  "success": true,
//	  "data": {
//	    "obj": {
//	      "2023-07-01": {
//	        "account_id": "acc_123456",
//	        "account_first_name": "John",
//	        "account_last_name": "Doe",
//	        "date": "2023-07-01",
//	        "reason": "holiday",
//	        "working_times": [{"start_time": 32400, "end_time": 64800}],
//	        "is_day_off": false
//	      }
//	    }
//	  },
//	  "message": "",
//	  "code": 0
//	}
//
// @Example      Error Response:
//
//	{
//	  "success": false,
//	  "data": {},
//	  "message": "Invalid date format",
//	  "code": 40000
//	}
func QueryStaffSchedulesDate(c *gin.Context) {
	var params types.QueryStaffSchedulesDateParams
	if err := c.ShouldBindQuery(&params); err != nil {
		writer.ResponseErr(c, err, nil)
		return
	}

	resp, err := services.StaffScheduleSvc().QueryStaffSchedulesDate(c.Request.Context(), params)
	if err != nil {
		writer.ResponseErr(c, err, nil)
		return
	}

	writer.ResponseOk(c, writer.List(resp))
}

// BatchUpdateStaffScheduleDate godoc
// @Summary      Batch update staff schedules for specific dates
// @Description  Update multiple staff schedules for specific dates (overrides). This creates schedule overrides that take precedence over regular weekly schedules for the specified dates. Working times are in seconds from midnight (e.g., 32400=9:00 AM, 64800=6:00 PM).
// @Tags         working-schedules
// @Accept       json
// @Produce      json
// @Param        request  body      types.BatchUpdateStaffScheduleOverrideReq  true  "Staff schedule date override information"
// @Success      200      {object}  writer.Resp{data=object{}}
// @Failure      400      {object}  writer.ErrResp
// @Failure      401      {object}  writer.ErrResp
// @Failure      500      {object}  writer.ErrResp
// @Router       /working-schedules/dates [put]
// @Security     ApiKeyAuth
// @Example      Success Response:
//
//	{
//	  "success": true,
//	  "data": {},
//	  "message": "",
//	  "code": 0
//	}
//
// @Example      Error Response:
//
//	{
//	  "success": false,
//	  "data": {},
//	  "message": "Invalid date format",
//	  "code": 40000
//	}
func BatchUpdateStaffScheduleDate(c *gin.Context) {
	var req types.BatchUpdateStaffScheduleOverrideReq
	if err := c.ShouldBindJSON(&req); err != nil {
		writer.ResponseErr(c, err, nil)
		return
	}

	err := services.StaffScheduleSvc().BatchUpdateStaffScheduleOverride(c.Request.Context(), req)
	if err != nil {
		writer.ResponseErr(c, err, nil)
	}

	writer.ResponseOk(c, writer.Empty())
}

func QueryStaffSchedulesSettings(c *gin.Context) {
	settings, err := services.StaffScheduleSvc().QueryStaffSchedulesSettings(c.Request.Context())
	if err != nil {
		writer.ResponseErr(c, err, nil)
		return
	}

	writer.ResponseOk(c, writer.Obj(settings))

}

func UpdateStaffSchedulesSettings(c *gin.Context) {
	var req types.UpdateStaffScheduleSettingsReq
	err := c.ShouldBindJSON(&req)
	if err != nil {
		writer.ResponseErr(c, uerror.Wrap(uerror.ErrInvalidParam, uerror.ErrCodeInvalidParam, "%v", err), nil)
		return
	}

	_, err = services.StaffScheduleSvc().UpdateStaffScheduleSettings(c.Request.Context(), req)
	if err != nil {
		writer.ResponseErr(c, err, nil)
		return
	}

	writer.ResponseOk(c, writer.Empty())
}
