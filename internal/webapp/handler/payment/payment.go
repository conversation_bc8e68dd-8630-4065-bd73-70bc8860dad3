package paymenthandler

import (
	paymentservice "pebble/internal/webapp/services/paymentservice"
	paymenttypes "pebble/internal/webapp/types/payment"
	"pebble/pkg/ugin/writer"

	"github.com/gin-gonic/gin"
)

// Payments godoc
// @Summary      Process payment
// @Description  Process payment for a specific source (appointment or sale)
// @Tags         payments
// @Accept       json
// @Produce      json
// @Param        request  body      types.PaymentsReq  true  "Payment information"
// @Success      200      {object}  writer.Resp{data=object{}}
// @Failure      400      {object}  writer.ErrResp
// @Failure      401      {object}  writer.ErrResp
// @Failure      500      {object}  writer.ErrResp
// @Router       /payments [post]
// @Security     ApiKeyAuth
func Payments(c *gin.Context) {
	var req paymenttypes.PaymentsReq
	if err := c.ShouldBindJSON(&req); err != nil {
		writer.ResponseErr(c, err, nil)
		return
	}

	err := paymentservice.PaymentSvc().Payments(c.Request.Context(), req)
	if err != nil {
		writer.ResponseErr(c, err, nil)
		return
	}

	writer.ResponseOk(c, writer.Empty())
}

func QueryPayments(c *gin.Context) {
	var params paymenttypes.QueryPaymentsParams
	if err := c.ShouldBindQuery(&params); err != nil {
		writer.ResponseErr(c, err, nil)
		return
	}

	resp, total, err := paymentservice.PaymentSvc().QueryPayments(c.Request.Context(), params)
	if err != nil {
		writer.ResponseErr(c, err, nil)
		return
	}

	writer.ResponseOk(c, writer.ListAndTotal(resp, total))
}
