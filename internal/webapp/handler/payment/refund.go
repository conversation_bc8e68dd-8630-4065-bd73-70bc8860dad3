package paymenthandler

import (
	paymentservice "pebble/internal/webapp/services/paymentservice"
	paymenttypes "pebble/internal/webapp/types/payment"
	"pebble/pkg/ugin/writer"

	"github.com/gin-gonic/gin"
)

func Refund(c *gin.Context) {
	var req paymenttypes.CreateRefundReq
	if err := c.ShouldBindJSON(&req); err != nil {
		writer.ResponseErr(c, err, nil)
		return
	}

	err := paymentservice.RefundSvc().Refund(c.Request.Context(), req)
	if err != nil {
		writer.ResponseErr(c, err, nil)
		return
	}

	writer.ResponseOk(c, writer.Empty())
}
