package handler

import (
	"pebble/internal/webapp/services"
	"pebble/internal/webapp/types"
	"pebble/pkg/uerror"
	"pebble/pkg/ugin/writer"

	"github.com/gin-gonic/gin"
)

func QueryPermissions(c *gin.Context) {
	var req types.QueryPermissionsParams
	if err := c.BindQuery(&req); err != nil {
		writer.ResponseErr(c, uerror.Wrap(uerror.ErrInvalidParam, uerror.ErrCodeInvalidParam, "%v", err), nil)
		return
	}

	ctx := c.Request.Context()
	permissions, err := services.PermissionSvc().QueryPermissions(ctx, req)
	if err != nil {
		writer.ResponseErr(c, err, nil)
		return
	}

	writer.ResponseOk(c, writer.List(permissions))
}

func CreatePermission(c *gin.Context) {
	var req types.CreatePermissionReq
	if err := c.ShouldBindJSON(&req); err != nil {
		writer.ResponseErr(c, uerror.Wrap(uerror.ErrInvalidParam, uerror.ErrCodeInvalidParam, "%v", err), nil)
		return
	}

	permission, err := services.PermissionSvc().CreatePermission(c.Request.Context(), req)
	if err != nil {
		writer.ResponseErr(c, uerror.ErrCommon, nil)
		return
	}

	writer.ResponseOk(c, writer.Obj(permission))
}

func UpdatePermission(c *gin.Context) {
	var uri struct {
		PermissionId string `uri:"permission_id" validate:"required" example:"perm_123456"`
	}
	if err := c.ShouldBindUri(&uri); err != nil {
		writer.ResponseErr(c, err, nil)
		return
	}

	var req types.UpdatePermissionReq
	if err := c.ShouldBindJSON(&req); err != nil {
		writer.ResponseErr(c, err, nil)
		return
	}

	err := services.PermissionSvc().UpdatePermission(c.Request.Context(), uri.PermissionId, req)
	if err != nil {
		writer.ResponseErr(c, err, nil)
		return
	}

	writer.ResponseOk(c, writer.Empty())
}

func DeletePermission(c *gin.Context) {
	uri := struct {
		PermissionId string `uri:"permission_id" binding:"required"`
	}{}
	if err := c.ShouldBindUri(&uri); err != nil {
		writer.ResponseErr(c, uerror.Wrap(uerror.ErrInvalidParam, uerror.ErrCodeInvalidParam, "%v", err), nil)
		return
	}

	err := services.PermissionSvc().DeletePermission(c.Request.Context(), uri.PermissionId)
	if err != nil {
		writer.ResponseErr(c, err, nil)
		return
	}

	writer.ResponseOk(c, writer.Empty())

}

func QueryPermission(c *gin.Context) {
	var uri struct {
		PermissionId string `uri:"permission_id" validate:"required" example:"perm_123456"`
	}
	if err := c.ShouldBindUri(&uri); err != nil {
		writer.ResponseErr(c, err, nil)
		return
	}

	permission, err := services.PermissionSvc().QueryPermission(c.Request.Context(), uri.PermissionId)
	if err != nil {
		writer.ResponseErr(c, err, nil)
		return
	}

	writer.ResponseOk(c, permission)
}
