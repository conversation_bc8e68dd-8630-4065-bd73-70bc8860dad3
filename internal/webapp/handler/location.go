package handler

import (
	"pebble/internal/webapp/services"
	"pebble/internal/webapp/services/sms"
	"pebble/internal/webapp/types"
	"pebble/pkg/uerror"
	"pebble/pkg/ugin/writer"
	"pebble/pkg/ulog"

	"github.com/gin-gonic/gin"
)

// QueryLocation godoc
// @Summary      Get location details
// @Description  Get detailed information about the current location
// @Tags         locations
// @Accept       json
// @Produce      json
// @Success      200      {object}  writer.Resp{data=object{obj=types.Location}}
// @Failure      401      {object}  writer.ErrResp
// @Failure      500      {object}  writer.ErrResp
// @Router       /locations [get]
// @Security     ApiKeyAuth
func QueryLocation(c *gin.Context) {
	ctx := c.Request.Context()
	resp, err := services.LocationSvc().QueryLocation(ctx)
	if err != nil {
		writer.ResponseErr(c, uerror.<PERSON><PERSON><PERSON><PERSON><PERSON>, nil)
		return
	}

	writer.ResponseOk(c, writer.Obj(resp))
}

// UpdateLocation godoc
// @Summary      Update location information
// @Description  Update the current location's information
// @Tags         locations
// @Accept       json
// @Produce      json
// @Param        request  body      types.UpdateLocationReq  true  "Updated location information"
// @Success      200      {object}  writer.Resp{data=object{}}
// @Failure      400      {object}  writer.ErrResp
// @Failure      401      {object}  writer.ErrResp
// @Failure      500      {object}  writer.ErrResp
// @Router       /locations [put]
// @Security     ApiKeyAuth
func UpdateLocation(c *gin.Context) {
	var req types.UpdateLocationReq
	if err := c.ShouldBindJSON(&req); err != nil {
		writer.ResponseErr(c, uerror.Wrap(uerror.ErrInvalidParam, uerror.ErrCodeInvalidParam, "%v", err), nil)
		return
	}

	ctx := c.Request.Context()

	err := services.LocationSvc().UpdateLocation(ctx, req)
	if err != nil {
		ulog.Errorln(ctx, "update location error:", err)
		writer.ResponseErr(c, uerror.ErrCommon, nil)
		return
	}

	writer.ResponseOk(c, writer.Empty())
}

// BindPhoneToLocation godoc
// @Summary      Bind phone number to location
// @Description  Purchase and bind a phone number to the current location
// @Tags         locations
// @Accept       json
// @Produce      json
// @Success      200      {object}  writer.Resp{data=object{obj=map[string]interface{}}}
// @Failure      401      {object}  writer.ErrResp
// @Failure      500      {object}  writer.ErrResp
// @Router       /locations/bind-phone [post]
// @Security     ApiKeyAuth
func BindPhoneToLocation(c *gin.Context) {

	ctx := c.Request.Context()

	phone, err := services.MsgSvc().PurchaseNumber(ctx, sms.PurchaseNumberReq{})
	if err != nil {
		ulog.Errorln(ctx, "update location error:", err)
		writer.ResponseErr(c, uerror.ErrCommon, nil)
		return
	}

	writer.ResponseOk(c, writer.Obj(phone))
}

func GetBingPhoneNumber(c *gin.Context) {
	ctx := c.Request.Context()
	resp, err := services.LocationSvc().GetBingPhoneNumber(ctx)
	if err != nil {
		ulog.Errorln(ctx, "get location  bing-phone error:", err)
		writer.ResponseErr(c, uerror.ErrCommon, nil)
		return
	}

	writer.ResponseOk(c, writer.Obj(resp))
}
