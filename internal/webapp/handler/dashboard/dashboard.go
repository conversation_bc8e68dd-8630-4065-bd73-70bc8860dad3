package dashboardhandler

import (
	dashboardservice "pebble/internal/webapp/services/dashboard"
	"pebble/pkg/ugin/writer"

	"github.com/gin-gonic/gin"
)

// GetAppointmentsDashboard godoc
//
//	@Summary		Get appointments for the dashboard
//	@Description	Get a list of appointments for the main dashboard, categorized by status
//	@Tags			dashboard
//	@Accept			json
//	@Produce		json
//	@Param			date	query		string	false	"Filter by date (YYYY-MM-DD)"
//	@Success		200		{object}	writer.Resp{data=object{obj=dashboardtypes.DashboardAppointmentsResponse}} "OK"
//	@Failure		400		{object}	writer.ErrResp "Bad Request"
//	@Failure		401		{object}	writer.ErrResp "Unauthorized"
//	@Failure		500		{object}	writer.ErrResp "Internal Server Error"
//	@Router			/dashboard/appointments [get]
//	@Security		ApiKeyAuth
func GetAppointmentsDashboard(c *gin.Context) {
	var params struct {
		Date string `form:"date" binding:"required,date"`
	}
	if err := c.Should<PERSON>y(&params); err != nil {
		writer.ResponseErr(c, err, nil)
		return
	}

	resp, err := dashboardservice.DashboardSvc().GetAppointmentsDashboard(c.Request.Context(), params.Date)
	if err != nil {
		writer.ResponseErr(c, err, nil)
		return
	}

	writer.ResponseOk(c, writer.Obj(resp))
}
