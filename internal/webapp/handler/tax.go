package handler

import (
	"pebble/internal/webapp/services"
	"pebble/internal/webapp/types"
	"pebble/pkg/uerror"
	"pebble/pkg/ugin/writer"

	"github.com/gin-gonic/gin"
)

// CreateTax godoc
// @Summary      Create a new tax
// @Description  Create a new tax with the provided information
// @Tags         taxes
// @Accept       json
// @Produce      json
// @Param        request  body      types.CreateTaxReq  true  "Tax information"
// @Success      200      {object}  writer.Resp{data=object{obj=types.Tax}}
// @Failure      400      {object}  writer.ErrResp
// @Failure      401      {object}  writer.ErrResp
// @Failure      500      {object}  writer.ErrResp
// @Router       /taxes [post]
// @Security     ApiKeyAuth
func CreateTax(c *gin.Context) {
	var req types.CreateTaxReq
	if err := c.ShouldBindJSON(&req); err != nil {
		writer.ResponseErr(c, uerror.Wrap(uerror.ErrInvalidParam, uerror.ErrCodeInvalidParam, "%v", err), nil)
		return
	}

	resp, err := services.TaxSvc().Create(c.Request.Context(), req)
	if err != nil {
		writer.ResponseErr(c, err, nil)
		return
	}

	writer.ResponseOk(c, writer.Obj(resp))
}

// UpdateTax godoc
// @Summary      Update tax information
// @Description  Update an existing tax's information
// @Tags         taxes
// @Accept       json
// @Produce      json
// @Param        tax_id   path      string           true  "Tax ID"
// @Param        request  body      types.UpdateTaxReq  true  "Updated tax information"
// @Success      200      {object}  writer.Resp{data=object{}}
// @Failure      400      {object}  writer.ErrResp
// @Failure      401      {object}  writer.ErrResp
// @Failure      404      {object}  writer.ErrResp
// @Failure      500      {object}  writer.ErrResp
// @Router       /taxes/{tax_id} [put]
// @Security     ApiKeyAuth
func UpdateTax(c *gin.Context) {
	var uri struct {
		TaxId string `uri:"tax_id"`
	}
	if err := c.ShouldBindUri(&uri); err != nil {
		writer.ResponseErr(c, uerror.Wrap(uerror.ErrInvalidParam, uerror.ErrCodeInvalidParam, "%v", err), nil)
		return
	}

	var req types.UpdateTaxReq
	if err := c.ShouldBindJSON(&req); err != nil {
		writer.ResponseErr(c, uerror.Wrap(uerror.ErrInvalidParam, uerror.ErrCodeInvalidParam, "%v", err), nil)
		return
	}

	err := services.TaxSvc().Update(c.Request.Context(), uri.TaxId, req)
	if err != nil {
		writer.ResponseErr(c, err, nil)
		return
	}

	writer.ResponseOk(c, writer.Empty())
}

// DeleteTax godoc
// @Summary      Delete a tax
// @Description  Delete a tax by ID
// @Tags         taxes
// @Accept       json
// @Produce      json
// @Param        tax_id   path      string  true  "Tax ID"
// @Success      200      {object}  writer.Resp{data=object{}}
// @Failure      400      {object}  writer.ErrResp
// @Failure      401      {object}  writer.ErrResp
// @Failure      404      {object}  writer.ErrResp
// @Failure      500      {object}  writer.ErrResp
// @Router       /taxes/{tax_id} [delete]
// @Security     ApiKeyAuth
func DeleteTax(c *gin.Context) {
	var uri struct {
		TaxId string `uri:"tax_id"`
	}
	if err := c.ShouldBindUri(&uri); err != nil {
		writer.ResponseErr(c, uerror.Wrap(uerror.ErrInvalidParam, uerror.ErrCodeInvalidParam, "%v", err), nil)
		return
	}

	err := services.TaxSvc().Delete(c.Request.Context(), uri.TaxId)
	if err != nil {
		writer.ResponseErr(c, err, nil)
		return
	}

	writer.ResponseOk(c, writer.Empty())
}

// QueryTaxList godoc
// @Summary      List all taxes
// @Description  Get a list of all taxes
// @Tags         taxes
// @Accept       json
// @Produce      json
// @Success      200      {object}  writer.Resp{data=object{list=[]types.Tax}}
// @Failure      401      {object}  writer.ErrResp
// @Failure      500      {object}  writer.ErrResp
// @Router       /taxes [get]
// @Security     ApiKeyAuth
func QueryTaxList(c *gin.Context) {
	resp, err := services.TaxSvc().List(c.Request.Context())
	if err != nil {
		writer.ResponseErr(c, err, nil)
		return
	}

	writer.ResponseOk(c, writer.List(resp))
}

// QueryTaxById godoc
// @Summary      Get tax details
// @Description  Get detailed information about a specific tax
// @Tags         taxes
// @Accept       json
// @Produce      json
// @Param        tax_id   path     string  true   "Tax ID"
// @Success      200      {object} writer.Resp{data=object{obj=types.Tax}}
// @Failure      400      {object} writer.ErrResp
// @Failure      401      {object} writer.ErrResp
// @Failure      404      {object} writer.Resp
// @Failure      500      {object} writer.Resp
// @Router       /taxes/{tax_id} [get]
// @Security     ApiKeyAuth
func QueryTaxById(c *gin.Context) {
	var uri struct {
		TaxId string `uri:"tax_id"`
	}
	if err := c.ShouldBindUri(&uri); err != nil {
		writer.ResponseErr(c, uerror.Wrap(uerror.ErrInvalidParam, uerror.ErrCodeInvalidParam, "%v", err), nil)
		return
	}

	resp, err := services.TaxSvc().GetTaxById(c.Request.Context(), uri.TaxId)
	if err != nil {
		writer.ResponseErr(c, err, nil)
		return
	}

	writer.ResponseOk(c, writer.Obj(resp))
}
