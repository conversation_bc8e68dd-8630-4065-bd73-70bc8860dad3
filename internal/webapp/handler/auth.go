package handler

import (
	"pebble/internal/webapp/config"
	"pebble/internal/webapp/services"
	"pebble/internal/webapp/types"
	"pebble/pkg/uerror"
	"pebble/pkg/ugin/writer"
	"pebble/pkg/ujwt"
	"pebble/pkg/ulog"
	"time"

	"github.com/gin-gonic/gin"
)

// Register godoc
//
//	@Summary		Register a new user
//	@Description	Register a new user with the provided information
//	@Tags			auth
//	@Accept			json
//	@Produce		json
//	@Param			request	body		types.RegisterReq	true	"Registration information" example({"location_name":"My Salon","account_name":"<PERSON>","phone_number":"+**********","email":"<EMAIL>","source":"website","password":"securepassword","timezone":"America/New_York","country":"US","currency":"USD"})
//	@Success		200		{object}	writer.Resp{data=object{obj=types.RegisterResp}} "OK"
//	@Failure		400		{object}	writer.ErrResp "Bad Request"
//	@Failure		500		{object}	writer.ErrResp "Internal Server Error"
//	@Router			/auth/register [post]
func Register(c *gin.Context) {
	var req types.RegisterReq
	if err := c.ShouldBindJSON(&req); err != nil {
		writer.ResponseErr(c, uerror.Wrap(uerror.ErrInvalidParam, uerror.ErrCodeInvalidParam, "%v", err), nil)
		return
	}
	ctx := c.Request.Context()
	ulog.Infoln(c.Request.Context(), "params:", req.Hide())

	resp, err := services.AuthSvc().Register(ctx, req)
	if err != nil {
		writer.ResponseErr(c, err, nil)
		return
	}

	writer.ResponseOk(c, writer.Obj(resp))
}

// Login godoc
//
//	@Summary		User login
//	@Description	Authenticate a user and return access token
//	@Tags			auth
//	@Accept			json
//	@Produce		json
//	@Param			request	body		types.LoginReq	true	"Login credentials" example({"email":"<EMAIL>","password":"securepassword"})
//	@Success		200		{object}	writer.Resp{data=object{obj=types.LoginResp}} "OK"
//	@Failure		400		{object}	writer.ErrResp "Bad Request"
//	@Failure		401		{object}	writer.ErrResp "Unauthorized"
//	@Failure		500		{object}	writer.ErrResp "Internal Server Error"
//	@Router			/auth/login [post]
func Login(c *gin.Context) {
	var req types.LoginReq
	if err := c.ShouldBindJSON(&req); err != nil {
		writer.ResponseErr(c, uerror.Wrap(uerror.ErrInvalidParam, uerror.ErrCodeInvalidParam, "%v", err), nil)
		return
	}

	ctx := c.Request.Context()
	ulog.Infoln(c.Request.Context(), "params:", req.Hide())

	resp, err := services.AuthSvc().Login(ctx, req)
	if err != nil {
		writer.ResponseErr(c, err, nil)
		return
	}

	// c.SetCookie("refresh_token", resp.AccessToken, int(resp.Expires), "/", config.C.App.Domain, true, true)
	writer.ResponseOk(c, writer.Obj(resp))
}

// Refresh godoc
//
//	@Summary		Refresh access token
//	@Description	Refresh an expired access token using a refresh token
//	@Tags			auth
//	@Accept			json
//	@Produce		json
//	@Param			request	body		types.RefreshReq	true	"Refresh token" example({"refresh_token":"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."})
//	@Success		200		{object}	writer.Resp{data=object{obj=types.RefreshResp}} "OK"
//	@Failure		400		{object}	writer.ErrResp "Bad Request"
//	@Failure		401		{object}	writer.ErrResp "Unauthorized"
//	@Failure		500		{object}	writer.ErrResp "Internal Server Error"
//	@Router			/auth/refresh [post]
func Refresh(c *gin.Context) {
	var req types.RefreshReq
	if err := c.ShouldBindJSON(&req); err != nil {
		writer.ResponseErr(c, uerror.Wrap(uerror.ErrInvalidParam, uerror.ErrCodeInvalidParam, "%v", err), nil)
		return
	}

	claims, err := ujwt.ParseToken(req.RefreshToken)
	if err != nil {
		writer.ResponseErr(c, err, nil)
		return
	}
	if claims.TokenType != ujwt.RefreshToken {
		writer.ResponseErr(c, uerror.ErrInvalidToken, nil)
		return
	}

	newAccessToken, _ := ujwt.GenerateAccessToken(claims.Ext)
	newRefreshToken, _ := ujwt.GenerateRefreshToken(claims.Ext)
	// c.SetCookie("refresh_token", newRefreshToken, int(config.C.JWT.AccessTokenExpire), "/", config.C.App.Domain, true, true)

	writer.ResponseOk(c, writer.Obj(types.RefreshResp{
		TenantId:     claims.TenantId,
		LocationId:   claims.LocationId,
		AccessToken:  newAccessToken,
		RefreshToken: newRefreshToken,
		Expires:      config.C.JWT.RefreshTokenExpire,
		ExpireTime:   time.Now().Add(time.Duration(config.C.JWT.RefreshTokenExpire)).Unix(),
	}))
}

// Validate godoc
//
//	@Summary		Validate token
//	@Description	Validate if the current token is valid
//	@Tags			auth
//	@Accept			json
//	@Produce		json
//	@Success		200	{object}	writer.Resp{data=object{obj=map[string]string}} "OK"
//	@Failure		401	{object}	writer.ErrResp "Unauthorized"
//	@Router			/auth/validate [post]
//	@Security		ApiKeyAuth
func Validate(ctx *gin.Context) {
	writer.ResponseOk(ctx, writer.Obj(map[string]any{"status": "ok"}))
}
