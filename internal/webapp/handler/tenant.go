package handler

import (
	"pebble/internal/webapp/services"
	"pebble/internal/webapp/types"
	"pebble/pkg/uerror"
	"pebble/pkg/ugin/writer"

	"github.com/gin-gonic/gin"
)

// QueryTenant godoc
// @Summary      Get tenant details
// @Description  Get detailed information about the current tenant
// @Tags         tenants
// @Accept       json
// @Produce      json
// @Success      200      {object}  writer.Resp{data=object{obj=types.Tenant}}
// @Failure      401      {object}  writer.ErrResp
// @Failure      500      {object}  writer.ErrResp
// @Router       /tenants [get]
// @Security     ApiKeyAuth
func QueryTenant(c *gin.Context) {
	ctx := c.Request.Context()

	resp, err := services.TenantSvc().QueryTenant(ctx)
	if err != nil {
		writer.ResponseErr(c, uerror.ErrCommon, nil)
		return
	}

	writer.ResponseOk(c, writer.Obj(resp))
}

// UpdateTenant godoc
// @Summary      Update tenant information
// @Description  Update the current tenant's information
// @Tags         tenants
// @Accept       json
// @Produce      json
// @Param        request  body      types.UpdateTenantReq  true  "Updated tenant information"
// @Success      200      {object}  writer.Resp{data=object{}}
// @Failure      400      {object}  writer.ErrResp
// @Failure      401      {object}  writer.ErrResp
// @Failure      500      {object}  writer.ErrResp
// @Router       /tenants [put]
// @Security     ApiKeyAuth
func UpdateTenant(c *gin.Context) {
	var req types.UpdateTenantReq
	if err := c.ShouldBindJSON(&req); err != nil {
		writer.ResponseErr(c, uerror.Wrap(uerror.ErrInvalidParam, uerror.ErrCodeInvalidParam, "%v", err), nil)
		return
	}

	ctx := c.Request.Context()

	err := services.TenantSvc().UpdateTenant(ctx, req)
	if err != nil {
		writer.ResponseErr(c, uerror.ErrCommon, nil)
		return
	}

	writer.ResponseOk(c, writer.Empty())
}
