package handler

import (
	twilioservice "pebble/internal/webapp/services/sms/twilio-service"
	"pebble/internal/webapp/types"
	"pebble/pkg/auth"
	"pebble/pkg/uerror"
	"pebble/pkg/ugin/writer"
	"strings"

	"github.com/gin-gonic/gin"
)

// TwilioSendSmsStatusCallBack godoc
// @Summary      Twilio SMS status callback
// @Description  Webhook for Twilio to report SMS delivery status
// @Tags         twilio-callback
// @Accept       json
// @Produce      json
// @Param        tenant_id    path      string                     true  "Tenant ID"
// @Param        location_id  path      string                     true  "Location ID"
// @Param        request      body      types.TwilioSendStatusCallback  true  "Status callback information"
// @Success      200          {object}  writer.Resp{data=object{}}
// @Failure      400          {object}  writer.ErrResp
// @Failure      500          {object}  writer.ErrResp
// @Router       /twilio-callback/messageing/tenant/{tenant_id}/location/{location_id}/status [post]
func TwilioSendSmsStatusCallBack(c *gin.Context) {
	var uri struct {
		TenantID   string `uri:"tenant_id"`
		LocationID string `uri:"location_id"`
	}
	if err := c.ShouldBindUri(&uri); err != nil {
		writer.ResponseErr(c, uerror.Wrap(uerror.ErrInvalidParam, uerror.ErrCodeInvalidParam, "%v", err), nil)
		return
	}

	var req types.TwilioSendStatusCallback
	contentType := c.GetHeader("Content-Type")
	if strings.Contains(contentType, "application/json") {
		if err := c.ShouldBindJSON(&req); err != nil {
			writer.ResponseErr(c, uerror.Wrap(uerror.ErrInvalidParam, uerror.ErrCodeInvalidParam, "%v", err), nil)
			return
		}
	} else {
		if err := c.ShouldBind(&req); err != nil {
			writer.ResponseErr(c, uerror.Wrap(uerror.ErrInvalidParam, uerror.ErrCodeInvalidParam, "%v", err), nil)
			return
		}
	}

	ctx := auth.ContextWithExt(c.Request.Context(), auth.Ext{
		TenantId:   uri.TenantID,
		LocationId: uri.LocationID,
	})

	err := twilioservice.NewTwilioProvider().StatusCallBack(ctx, req)
	if err != nil {
		writer.ResponseErr(c, err, nil)
		return
	}

	writer.ResponseOk(c, writer.Empty())
}

// TwilioSmsInboundWebhook godoc
// @Summary      Twilio SMS inbound webhook
// @Description  Webhook for Twilio to report incoming SMS messages
// @Tags         twilio-callback
// @Accept       json
// @Produce      json
// @Param        tenant_id    path      string                     true  "Tenant ID"
// @Param        location_id  path      string                     true  "Location ID"
// @Param        request      body      types.TwilioSMSInboundWebhook  true  "Inbound SMS information"
// @Success      200          {object}  writer.Resp{data=object{}}
// @Failure      400          {object}  writer.ErrResp
// @Failure      500          {object}  writer.ErrResp
// @Router       /twilio-callback/messageing/tenant/{tenant_id}/location/{location_id}/inbound [post]
func TwilioSmsInboundWebhook(c *gin.Context) {
	var uri struct {
		TenantID   string `uri:"tenant_id"`
		LocationID string `uri:"location_id"`
	}
	if err := c.ShouldBindUri(&uri); err != nil {
		writer.ResponseErr(c, uerror.Wrap(uerror.ErrInvalidParam, uerror.ErrCodeInvalidParam, "%v", err), nil)
		return
	}

	var req types.TwilioSMSInboundWebhook
	contentType := c.GetHeader("Content-Type")
	if strings.Contains(contentType, "application/json") {
		if err := c.ShouldBindJSON(&req); err != nil {
			writer.ResponseErr(c, uerror.Wrap(uerror.ErrInvalidParam, uerror.ErrCodeInvalidParam, "%v", err), nil)
			return
		}
	} else {
		if err := c.ShouldBind(&req); err != nil {
			writer.ResponseErr(c, uerror.Wrap(uerror.ErrInvalidParam, uerror.ErrCodeInvalidParam, "%v", err), nil)
			return
		}
	}

	ctx := auth.ContextWithExt(c.Request.Context(), auth.Ext{
		TenantId:   uri.TenantID,
		LocationId: uri.LocationID,
	})
	err := twilioservice.NewTwilioProvider().InboundWebhook(ctx, req)
	if err != nil {
		writer.ResponseErr(c, err, nil)
		return
	}

	writer.ResponseOk(c, writer.Empty())
}
