package appointmenthandler

import (
	appointmentservice "pebble/internal/webapp/services/appointment"
	appointmenttypes "pebble/internal/webapp/types/appointment"
	"pebble/pkg/uerror"
	"pebble/pkg/ugin/writer"

	"github.com/gin-gonic/gin"
)

func QueryClientAppointments(c *gin.Context) {
	var uri struct {
		ClientId string `uri:"client_id" binding:"required"`
	}
	if err := c.ShouldBindUri(&uri); err != nil {
		writer.ResponseErr(c, uerror.Wrap(uerror.ErrInvalidParam, uerror.ErrCodeInvalidParam, "%v", err), nil)
		return
	}

	var params appointmenttypes.QueryClientAppointmentParams
	if err := c.ShouldBind<PERSON>uery(&params); err != nil {
		writer.ResponseErr(c, err, nil)
		return
	}

	resp, total, err := appointmentservice.ClientAppointmentSvc().List(c.Request.Context(), uri.ClientId, params)
	if err != nil {
		writer.ResponseErr(c, err, nil)
		return
	}

	writer.ResponseOk(c, writer.ListAndTotal(resp, total))
}
