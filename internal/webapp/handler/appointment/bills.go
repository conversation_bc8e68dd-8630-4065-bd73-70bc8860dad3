package appointmenthandler

import (
	appointmentservice "pebble/internal/webapp/services/appointment"
	"pebble/pkg/uerror"
	"pebble/pkg/ugin/writer"

	"github.com/gin-gonic/gin"
)

func CreateAppointmentBills(c *gin.Context) {
	var req struct {
		AppointmentId string `uri:"appointment_id" binding:"required"`
	}
	if err := c.Should<PERSON>ri(&req); err != nil {
		writer.ResponseErr(c, uerror.Wrap(uerror.ErrInvalidParam, uerror.ErrCodeInvalidParam, "%v", err), nil)
		return
	}

	var params struct {
		PaymentMethodName string `form:"payment_method_name" binding:"required"`
	}
	if err := c.ShouldBind<PERSON>uery(&params); err != nil {
		writer.ResponseErr(c, err, nil)
		return
	}

	resp, err := appointmentservice.AppointmentSvc().CreateBills(c.Request.Context(), req.AppointmentId, params.PaymentMethodName)
	if err != nil {
		writer.ResponseErr(c, err, nil)
		return
	}

	writer.ResponseOk(c, writer.Obj(resp))
}

func CreateAppointmentAABills(c *gin.Context) {
	var req struct {
		AppointmentId string `uri:"appointment_id" binding:"required"`
	}
	if err := c.ShouldBindUri(&req); err != nil {
		writer.ResponseErr(c, uerror.Wrap(uerror.ErrInvalidParam, uerror.ErrCodeInvalidParam, "%v", err), nil)
		return
	}

	var params struct {
		PaymentMethodName string `form:"payment_method_name" binding:"required"`
	}
	if err := c.ShouldBindQuery(&params); err != nil {
		writer.ResponseErr(c, err, nil)
		return
	}

	resp, err := appointmentservice.AppointmentSvc().CreateAABills(c.Request.Context(), req.AppointmentId, params.PaymentMethodName)
	if err != nil {
		writer.ResponseErr(c, err, nil)
		return
	}

	writer.ResponseOk(c, writer.Obj(resp))
}
