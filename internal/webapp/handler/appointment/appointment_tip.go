package appointmenthandler

import (
	appointmentservice "pebble/internal/webapp/services/appointment"
	appointmenttypes "pebble/internal/webapp/types/appointment"
	"pebble/pkg/uerror"
	"pebble/pkg/ugin/writer"

	"github.com/gin-gonic/gin"
)

func UpdateAppointmentTips(c *gin.Context) {
	var uri struct {
		AppointmentId string `uri:"appointment_id" binding:"required"`
	}
	if err := c.ShouldBindUri(&uri); err != nil {
		writer.ResponseErr(c, uerror.Wrap(uerror.ErrInvalidParam, uerror.ErrCodeInvalidParam, "%v", err), nil)
		return
	}
	var req appointmenttypes.BatchUpdateTipsReq
	if err := c.ShouldBindJSON(&req); err != nil {
		writer.ResponseErr(c, err, nil)
		return
	}

	err := appointmentservice.AppointmentTipSvc().UpdateAppointmentTips(c.Request.Context(), uri.AppointmentId, req)
	if err != nil {
		writer.ResponseErr(c, err, nil)
		return
	}

	writer.ResponseOk(c, writer.Empty())
}

func AddAppointmentTips(c *gin.Context) {
	var uri struct {
		AppointmentId string `uri:"appointment_id" binding:"required"`
	}
	if err := c.ShouldBindUri(&uri); err != nil {
		writer.ResponseErr(c, uerror.Wrap(uerror.ErrInvalidParam, uerror.ErrCodeInvalidParam, "%v", err), nil)
		return
	}
	var req appointmenttypes.AddAppointmentTipsReq
	if err := c.ShouldBindJSON(&req); err != nil {
		writer.ResponseErr(c, err, nil)
		return
	}

	tip, err := appointmentservice.AppointmentTipSvc().AddAppointmentTips(c.Request.Context(), uri.AppointmentId, req)
	if err != nil {
		writer.ResponseErr(c, err, nil)
		return
	}

	writer.ResponseOk(c, writer.Obj(tip))
}

func DeleteAppointmentTip(c *gin.Context) {
	var uri struct {
		AppointmentId string `uri:"appointment_id" binding:"required"`
		ApptTipId     string `uri:"appt_tip_id" binding:"required"`
	}
	if err := c.ShouldBindUri(&uri); err != nil {
		writer.ResponseErr(c, uerror.Wrap(uerror.ErrInvalidParam, uerror.ErrCodeInvalidParam, "%v", err), nil)
		return
	}

	err := appointmentservice.AppointmentTipSvc().DeleteAppointmentTip(c.Request.Context(), uri.AppointmentId, uri.ApptTipId)
	if err != nil {
		writer.ResponseErr(c, err, nil)
		return
	}
	writer.ResponseOk(c, writer.Empty())
}
