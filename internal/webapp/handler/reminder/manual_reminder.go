package reminder

import (
	"pebble/internal/webapp/services/reminder"
	reminderTypes "pebble/internal/webapp/types/reminder"
	"pebble/pkg/uerror"
	"pebble/pkg/ugin/writer"
	"pebble/pkg/ulog"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

// ManualReminderHandler handles manual reminder operations
type ManualReminderHandler struct {
	service reminder.IManualReminderService
}

// NewManualReminderHandler creates a new manual reminder handler
func NewManualReminderHandler(service reminder.IManualReminderService) *ManualReminderHandler {
	return &ManualReminderHandler{
		service: service,
	}
}

// NewManualReminderHandlerWithDefaults creates a new manual reminder handler with default dependencies
func NewManualReminderHandlerWithDefaults() *ManualReminderHandler {
	service := reminder.NewManualReminderServiceWithDefaults()
	return NewManualReminderHandler(service)
}

// PreviewReminder handles POST /api/v1/reminders/preview
func (h *ManualReminderHandler) PreviewReminder(c *gin.Context) {
	var req reminderTypes.PreviewRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		ulog.Error(c.Request.Context(), "invalid request body", zap.Error(err))
		writer.ResponseErr(c, uerror.Wrap(uerror.ErrInvalidParam, uerror.ErrCodeInvalidParam, "%v", err), nil)
		return
	}

	preview, err := h.service.PreviewReminder(c.Request.Context(), req)
	if err != nil {
		ulog.Error(c.Request.Context(), "failed to preview reminder",
			zap.String("template_type", string(req.TemplateType)),
			zap.String("appointment_id", req.AppointmentId),
			zap.String("client_id", req.ClientId),
			zap.Error(err))
		writer.ResponseErr(c, err, nil)
		return
	}

	writer.ResponseOk(c, writer.Obj(preview))
}

// SendReminder handles POST /api/v1/reminders/send
func (h *ManualReminderHandler) SendReminder(c *gin.Context) {
	var req reminderTypes.SendRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		ulog.Error(c.Request.Context(), "invalid request body", zap.Error(err))
		writer.ResponseErr(c, uerror.Wrap(uerror.ErrInvalidParam, uerror.ErrCodeInvalidParam, "%v", err), nil)
		return
	}

	result, err := h.service.SendReminder(c.Request.Context(), req)
	if err != nil {
		ulog.Error(c.Request.Context(), "failed to send reminder",
			zap.String("template_type", string(req.TemplateType)),
			zap.String("phone", req.Phone),
			zap.String("appointment_id", req.AppointmentId),
			zap.String("client_id", req.ClientId),
			zap.Error(err))
		writer.ResponseErr(c, err, nil)
		return
	}

	writer.ResponseOk(c, writer.Obj(result))
}

// GetSupportedTypes handles GET /api/v1/reminders/types
func (h *ManualReminderHandler) GetSupportedTypes(c *gin.Context) {
	types, err := h.service.GetSupportedTypes(c.Request.Context())
	if err != nil {
		ulog.Error(c.Request.Context(), "failed to get supported types", zap.Error(err))
		writer.ResponseErr(c, err, nil)
		return
	}

	writer.ResponseOk(c, writer.List(types))
}
