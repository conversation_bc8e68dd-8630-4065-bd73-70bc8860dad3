package handler

import (
	"pebble/pkg/localization"
	"pebble/pkg/ugin/writer"

	"github.com/gin-gonic/gin"
)

// LocalizationData represents the complete localization data response
type LocalizationData struct {
	Currencies []localization.Currency `json:"currencies"`
	Countries  []localization.Country  `json:"countries"`
	Languages  []localization.Language `json:"languages"`
}

// GetLocalizationData godoc
// @Summary      Get localization data
// @Description  Get all available currencies, countries, and languages for internationalization. This endpoint does not require authentication and provides comprehensive data for frontend localization.
// @Tags         localization
// @Accept       json
// @Produce      json
// @Success      200  {object}  writer.Resp{data=object{obj=LocalizationData}}
// @Failure      500  {object}  writer.ErrResp
// @Router       /localization [get]
// @Example      Success Response:
//
//	{
//	  "success": true,
//	  "data": {
//	    "obj": {
//	      "currencies": [
//	        {
//	          "code": "USD",
//	          "symbol": "$",
//	          "name": "US Dollar"
//	        },
//	        {
//	          "code": "EUR",
//	          "symbol": "€",
//	          "name": "Euro"
//	        }
//	      ],
//	      "countries": [
//	        {
//	          "code": "US",
//	          "name": "United States",
//	          "currency_code": "USD",
//	          "currency": {
//	            "code": "USD",
//	            "symbol": "$",
//	            "name": "US Dollar"
//	          }
//	        }
//	      ],
//	      "languages": [
//	        {
//	          "code": "en",
//	          "name": "English"
//	        },
//	        {
//	          "code": "zh",
//	          "name": "Chinese"
//	        }
//	      ]
//	    }
//	  },
//	  "message": "",
//	  "code": 0
//	}
//
// @Example      Error Response:
//
//	{
//	  "success": false,
//	  "data": {},
//	  "message": "Internal server error",
//	  "code": 50000
//	}
func GetLocalizationData(c *gin.Context) {
	data := LocalizationData{
		Currencies: localization.GetAllCurrencies(),
		Countries:  localization.GetAllCountries(),
		Languages:  localization.GetAllLanguages(),
	}

	writer.ResponseOk(c, writer.Obj(data))
}

// GetMajorLocalizationData godoc
// @Summary      Get major localization data
// @Description  Get commonly used currencies, countries, and languages for internationalization. This endpoint returns a subset of the most frequently used localization data and does not require authentication.
// @Tags         localization
// @Accept       json
// @Produce      json
// @Success      200  {object}  writer.Resp{data=object{obj=LocalizationData}}
// @Failure      500  {object}  writer.ErrResp
// @Router       /localization/major [get]
// @Example      Success Response:
//
//	{
//	  "success": true,
//	  "data": {
//	    "obj": {
//	      "currencies": [
//	        {
//	          "code": "USD",
//	          "symbol": "$",
//	          "name": "US Dollar"
//	        },
//	        {
//	          "code": "EUR",
//	          "symbol": "€",
//	          "name": "Euro"
//	        }
//	      ],
//	      "countries": [
//	        {
//	          "code": "US",
//	          "name": "United States",
//	          "currency_code": "USD",
//	          "currency": {
//	            "code": "USD",
//	            "symbol": "$",
//	            "name": "US Dollar"
//	          }
//	        }
//	      ],
//	      "languages": [
//	        {
//	          "code": "en",
//	          "name": "English"
//	        },
//	        {
//	          "code": "zh",
//	          "name": "Chinese"
//	        }
//	      ]
//	    }
//	  },
//	  "message": "",
//	  "code": 0
//	}
func GetMajorLocalizationData(c *gin.Context) {
	// Get major currencies
	majorCurrencies := localization.GetMajorCurrencies()

	// Get countries that use major currencies
	majorCountries := []localization.Country{}
	majorCurrencyCodes := make(map[string]bool)
	for _, currency := range majorCurrencies {
		majorCurrencyCodes[currency.Code] = true
	}

	for _, country := range localization.GetAllCountries() {
		if majorCurrencyCodes[country.CurrencyCode] {
			majorCountries = append(majorCountries, country)
		}
	}

	data := LocalizationData{
		Currencies: majorCurrencies,
		Countries:  majorCountries,
		Languages:  localization.GetMajorLanguages(),
	}

	writer.ResponseOk(c, writer.Obj(data))
}

// GetCurrencies godoc
// @Summary      Get all currencies
// @Description  Get all available currencies with their codes, symbols, and names. This endpoint does not require authentication.
// @Tags         localization
// @Accept       json
// @Produce      json
// @Success      200  {object}  writer.Resp{data=object{list=[]currencies.Currency}}
// @Failure      500  {object}  writer.ErrResp
// @Router       /currencies [get]
func GetCurrencies(c *gin.Context) {
	currencies := localization.GetAllCurrencies()
	writer.ResponseOk(c, writer.List(currencies))
}

// GetCountries godoc
// @Summary      Get all countries
// @Description  Get all available countries with their codes, names, and associated currencies. This endpoint does not require authentication.
// @Tags         localization
// @Accept       json
// @Produce      json
// @Success      200  {object}  writer.Resp{data=object{list=[]currencies.Country}}
// @Failure      500  {object}  writer.ErrResp
// @Router       /countries [get]
func GetCountries(c *gin.Context) {
	countries := localization.GetAllCountries()
	writer.ResponseOk(c, writer.List(countries))
}

// GetLanguages godoc
// @Summary      Get all languages
// @Description  Get all available languages with their codes and names. This endpoint does not require authentication.
// @Tags         localization
// @Accept       json
// @Produce      json
// @Success      200  {object}  writer.Resp{data=object{list=[]currencies.Language}}
// @Failure      500  {object}  writer.ErrResp
// @Router       /languages [get]
func GetLanguages(c *gin.Context) {
	languages := localization.GetAllLanguages()
	writer.ResponseOk(c, writer.List(languages))
}

// GetTimezones godoc
// @Summary      Get all timezones
// @Description  Get a list of all IANA timezones. The list is cached for 24 hours. This endpoint does not require authentication.
// @Tags         localization
// @Accept       json
// @Produce      json
// @Success      200  {object}  writer.Resp{data=object{list=[]localization.TimezoneInfo}}
// @Failure      500  {object}  writer.ErrResp
// @Router       /timezones [get]
func GetTimezones(c *gin.Context) {
	timezones, err := localization.GetTimezones()
	if err != nil {
		writer.ResponseErr(c, err, nil)
		return
	}
	writer.ResponseOk(c, writer.List(timezones))
}
