package stripe

import (
	"pebble/internal/webapp/services/stripe"
	stripetypes "pebble/internal/webapp/types/stripe"
	"pebble/pkg/ugin/writer"

	"github.com/gin-gonic/gin"
)

func QuerySettings(c *gin.Context) {
	obj, err := stripe.SettingSvc().Get(c.Request.Context())
	if err != nil {
		writer.ResponseErr(c, err, nil)
		return
	}
	writer.ResponseOk(c, writer.Obj(obj))
}

func UpdateSettings(c *gin.Context) {
	var req stripetypes.UpdateStripeSettingReq
	if err := c.ShouldBindJSON(&req); err != nil {
		writer.ResponseErr(c, err, nil)
		return
	}

	err := stripe.SettingSvc().Update(c.Request.Context(), req)
	if err != nil {
		writer.ResponseErr(c, err, nil)
		return
	}

	writer.ResponseOk(c, writer.Empty())
}

func GetConnectLink(c *gin.Context) {
	link, err := stripe.SettingSvc().GetConnectLink(c.Request.Context())
	if err != nil {
		writer.ResponseErr(c, err, nil)
		return
	}
	writer.ResponseOk(c, writer.Obj(link))
}
