package stripe

import (
	"pebble/internal/webapp/services/stripe"
	stripetypes "pebble/internal/webapp/types/stripe"

	"pebble/pkg/ugin/writer"

	"github.com/gin-gonic/gin"
)

func GetStripeBalance(c *gin.Context) {
	balance, err := stripe.BalanceSvc().GetBalance(c.Request.Context())
	if err != nil {
		writer.ResponseErr(c, err, nil)
		return
	}

	writer.ResponseOk(c, balance)
}

func GetStripeBalancePayoutLogs(c *gin.Context) {
	var req stripetypes.GetStripeBalancePayoutLogsReq
	if err := c.ShouldBindQuery(&req); err != nil {
		writer.ResponseErr(c, err, nil)
		return
	}

	logs, total, err := stripe.BalanceSvc().GetBalancePayoutLogs(c.Request.Context(), req)
	if err != nil {
		writer.ResponseErr(c, err, nil)
		return
	}

	writer.ResponseOk(c, writer.ListAndTotal(logs, total))
}

func GetStripeBalancePaymentLogs(c *gin.Context) {
	var req stripetypes.GetStripeBalancePaymentLogsReq
	if err := c.ShouldBindQuery(&req); err != nil {
		writer.ResponseErr(c, err, nil)
		return
	}

	logs, total, err := stripe.BalanceSvc().GetBalancePaymentLogs(c.Request.Context(), req)
	if err != nil {
		writer.ResponseErr(c, err, nil)
		return
	}

	writer.ResponseOk(c, writer.ListAndTotal(logs, total))
}

func StripeBalancePayout(c *gin.Context) {
	var req stripetypes.StripeBalancePayoutReq
	if err := c.ShouldBindJSON(&req); err != nil {
		writer.ResponseErr(c, err, nil)
		return
	}

	err := stripe.BalanceSvc().StripeBalancePayout(c.Request.Context(), req)
	if err != nil {
		writer.ResponseErr(c, err, nil)
		return
	}

	writer.ResponseOk(c, writer.Empty())
}

func UpdateStripePayoutSetting(c *gin.Context) {
	var req stripetypes.UpdateStripePayoutSettingReq
	if err := c.ShouldBindJSON(&req); err != nil {
		writer.ResponseErr(c, err, nil)
		return
	}

	err := stripe.BalanceSvc().UpdateStripePayoutSetting(c.Request.Context(), req)
	if err != nil {
		writer.ResponseErr(c, err, nil)
		return
	}

	writer.ResponseOk(c, writer.Empty())
}

func QueryStripePayoutSetting(c *gin.Context) {
	setting, err := stripe.BalanceSvc().QueryStripePayoutSetting(c.Request.Context())
	if err != nil {
		writer.ResponseErr(c, err, nil)
		return
	}

	writer.ResponseOk(c, setting)
}
