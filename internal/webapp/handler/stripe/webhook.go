package stripe

import (
	"pebble/internal/webapp/services/stripe"
	"pebble/pkg/ugin/writer"

	"github.com/gin-gonic/gin"
)

func Webhook(c *gin.Context) {

	var event map[string]interface{}
	if err := c.ShouldBindJSON(&event); err != nil {
		writer.ResponseErr(c, err, nil)
		return
	}

	err := stripe.WebhookSvc().HandleWebhook(c.Request.Context(), event)
	if err != nil {
		writer.ResponseErr(c, err, nil)
		return
	}
	writer.ResponseOk(c, writer.Obj(nil))
}
