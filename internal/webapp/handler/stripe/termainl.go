package stripe

import (
	"pebble/internal/webapp/services/stripe"
	stripetypes "pebble/internal/webapp/types/stripe"
	"pebble/pkg/ugin/writer"

	"github.com/gin-gonic/gin"
)

func RegisterStripeReader(c *gin.Context) {
	var req stripetypes.RegisterStripeReaderReq
	if err := c.ShouldBindJSON(&req); err != nil {
		writer.ResponseErr(c, err, nil)
		return
	}

	err := stripe.TerminalSvc().RegisterStripeReader(c.Request.Context(), req)
	if err != nil {
		writer.ResponseErr(c, err, nil)
		return
	}
	writer.ResponseOk(c, writer.Empty())
}

func QueryStripeReaders(c *gin.Context) {
	var params stripetypes.QueryStripeReadersParams
	if err := c.Should<PERSON>ind<PERSON>y(&params); err != nil {
		writer.ResponseErr(c, err, nil)
		return
	}

	readers, err := stripe.TerminalSvc().QueryStripeReaders(c.Request.Context(), params)
	if err != nil {
		writer.ResponseErr(c, err, nil)
		return
	}
	writer.ResponseOk(c, writer.Obj(readers))
}

func StripeReaderIntent(c *gin.Context) {
	var req stripetypes.StripeReaderIntentReq
	if err := c.ShouldBindJSON(&req); err != nil {
		writer.ResponseErr(c, err, nil)
		return
	}

	err := stripe.TerminalSvc().StripeReaderIntent(c.Request.Context(), req)
	if err != nil {
		writer.ResponseErr(c, err, nil)
		return
	}

	writer.ResponseOk(c, writer.Empty())
}

func CancelStripeReaderAction(c *gin.Context) {
	var req stripetypes.CancelStripeReaderActionReq
	if err := c.ShouldBindJSON(&req); err != nil {
		writer.ResponseErr(c, err, nil)
		return
	}

	err := stripe.TerminalSvc().CancelStripeReaderAction(c.Request.Context(), req)
	if err != nil {
		writer.ResponseErr(c, err, nil)
		return
	}
}
