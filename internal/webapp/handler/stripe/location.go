package stripe

import (
	"pebble/internal/webapp/services/stripe"
	stripetypes "pebble/internal/webapp/types/stripe"
	"pebble/pkg/ugin/writer"

	"github.com/gin-gonic/gin"
)

func QueryLocations(c *gin.Context) {
	list, err := stripe.LocationSvc().List(c.Request.Context())
	if err != nil {
		writer.ResponseErr(c, err, nil)
		return
	}
	writer.ResponseOk(c, writer.List(list))
}

func CreateLocation(c *gin.Context) {

	var req stripetypes.StripeLocationsEntity
	if err := c.ShouldBindJSON(&req); err != nil {
		writer.ResponseErr(c, err, nil)
		return
	}

	obj, err := stripe.LocationSvc().Create(c.Request.Context(), req)
	if err != nil {
		writer.ResponseErr(c, err, nil)
		return
	}
	writer.ResponseOk(c, writer.Obj(obj))
}
