package handler

import (
	"pebble/internal/webapp/services"
	"pebble/internal/webapp/types"
	"pebble/pkg/uerror"
	"pebble/pkg/ugin/writer"

	"github.com/gin-gonic/gin"
)

// QueryServiceCategoryList godoc
//
//	@Summary		List all service categories
//	@Description	Get a list of all service categories
//	@Tags			service-categories
//	@Accept			json
//	@Produce		json
//	@Success		200	{object}	writer.Resp{data=object{list=[]types.ServiceCategory}} "OK"
//	@Failure		401	{object}	writer.ErrResp "Unauthorized"
//	@Failure		500	{object}	writer.ErrResp "Internal Server Error"
//	@Router			/service-categories [get]
//	@Security		ApiKeyAuth
func QueryServiceCategoryList(c *gin.Context) {
	resp, err := services.ServiceCategorySvc().List(c.Request.Context())
	if err != nil {
		writer.ResponseErr(c, err, nil)
		return
	}

	writer.ResponseOk(c, writer.List(resp))
}

// QueryServiceCategory godoc
//
//	@Summary		Get service category details
//	@Description	Get detailed information about a specific service category
//	@Tags			service-categories
//	@Accept			json
//	@Produce		json
//	@Param			category_id	path		string	true	"Category ID"
//	@Success		200			{object}	writer.Resp{data=object{obj=types.ServiceCategory}} "OK"
//	@Failure		400			{object}	writer.ErrResp "Bad Request"
//	@Failure		401			{object}	writer.ErrResp "Unauthorized"
//	@Failure		404			{object}	writer.ErrResp "Not Found"
//	@Failure		500			{object}	writer.ErrResp "Internal Server Error"
//	@Router			/service-categories/{category_id} [get]
//	@Security		ApiKeyAuth
func QueryServiceCategory(c *gin.Context) {
	var req struct {
		CategoryId string `uri:"category_id"`
	}
	if err := c.ShouldBindUri(&req); err != nil {
		writer.ResponseErr(c, uerror.Wrap(uerror.ErrInvalidParam, uerror.ErrCodeInvalidParam, "%v", err), nil)
		return
	}
	resp, err := services.ServiceCategorySvc().Get(c.Request.Context(), req.CategoryId)
	if err != nil {
		writer.ResponseErr(c, err, nil)
		return
	}

	writer.ResponseOk(c, writer.Obj(resp))
}

// CreateServiceCategory godoc
//
//	@Summary		Create a new service category
//	@Description	Create a new service category with the provided information
//	@Tags			service-categories
//	@Accept			json
//	@Produce		json
//	@Param			request	body		types.CreateServiceCategoryReq	true	"Service category information" example({"name":"Haircut","description":"All haircut services","color":"#FF5733","icon":"scissors"})
//	@Success		200		{object}	writer.Resp{data=object{obj=types.ServiceCategory}} "OK"
//	@Failure		400		{object}	writer.ErrResp "Bad Request"
//	@Failure		401		{object}	writer.ErrResp "Unauthorized"
//	@Failure		500		{object}	writer.ErrResp "Internal Server Error"
//	@Router			/service-categories [post]
//	@Security		ApiKeyAuth
func CreateServiceCategory(c *gin.Context) {
	var req types.CreateServiceCategoryReq
	if err := c.ShouldBindJSON(&req); err != nil {
		writer.ResponseErr(c, err, nil)
		return
	}

	resp, err := services.ServiceCategorySvc().Create(c.Request.Context(), req)
	if err != nil {
		writer.ResponseErr(c, err, nil)
	}

	writer.ResponseOk(c, writer.Obj(resp))
}

// UpdateServiceCategory godoc
//
//	@Summary		Update service category
//	@Description	Update an existing service category's information
//	@Tags			service-categories
//	@Accept			json
//	@Produce		json
//	@Param			category_id	path		string							true	"Category ID"
//	@Param			request		body		types.UpdateServiceCategoryReq	true	"Updated service category information" example({"name":"Haircut Premium","description":"Premium haircut services","color":"#3366FF","icon":"premium-scissors"})
//	@Success		200			{object}	writer.Resp{data=object{}} "OK"
//	@Failure		400			{object}	writer.ErrResp "Bad Request"
//	@Failure		401			{object}	writer.ErrResp "Unauthorized"
//	@Failure		404			{object}	writer.ErrResp "Not Found"
//	@Failure		500			{object}	writer.ErrResp "Internal Server Error"
//	@Router			/service-categories/{category_id} [put]
//	@Security		ApiKeyAuth
func UpdateServiceCategory(c *gin.Context) {
	var uri struct {
		CategoryId string `uri:"category_id"`
	}
	if err := c.ShouldBindUri(&uri); err != nil {
		writer.ResponseErr(c, uerror.Wrap(uerror.ErrInvalidParam, uerror.ErrCodeInvalidParam, "%v", err), nil)
		return
	}

	var req types.UpdateServiceCategoryReq
	if err := c.ShouldBindJSON(&req); err != nil {
		writer.ResponseErr(c, err, nil)
		return
	}

	err := services.ServiceCategorySvc().Update(c.Request.Context(), uri.CategoryId, req)
	if err != nil {
		writer.ResponseErr(c, err, nil)
		return
	}

	writer.ResponseOk(c, writer.Empty())
}

// DeleteServiceCategory godoc
//
//	@Summary		Delete a service category
//	@Description	Delete a service category by ID
//	@Tags			service-categories
//	@Accept			json
//	@Produce		json
//	@Param			category_id	path		string	true	"Category ID"
//	@Success		200			{object}	writer.Resp{data=object{}} "OK"
//	@Failure		400			{object}	writer.ErrResp "Bad Request"
//	@Failure		401			{object}	writer.ErrResp "Unauthorized"
//	@Failure		404			{object}	writer.ErrResp "Not Found"
//	@Failure		500			{object}	writer.ErrResp "Internal Server Error"
//	@Router			/service-categories/{category_id} [delete]
//	@Security		ApiKeyAuth
func DeleteServiceCategory(c *gin.Context) {
	var uri struct {
		CategoryId string `uri:"category_id"`
	}
	if err := c.ShouldBindUri(&uri); err != nil {
		writer.ResponseErr(c, uerror.Wrap(uerror.ErrInvalidParam, uerror.ErrCodeInvalidParam, "%v", err), nil)
		return
	}

	err := services.ServiceCategorySvc().Delete(c.Request.Context(), uri.CategoryId)
	if err != nil {
		writer.ResponseErr(c, err, nil)
		return
	}
	writer.ResponseOk(c, writer.Empty())
}
