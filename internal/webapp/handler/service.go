package handler

import (
	"pebble/internal/webapp/services"
	"pebble/internal/webapp/types"
	"pebble/pkg/uerror"
	"pebble/pkg/ugin/writer"

	"github.com/gin-gonic/gin"
)

// QueryServiceList godoc
// @Summary      List all services
// @Description  Get a list of all services
// @Tags         services
// @Accept       json
// @Produce      json
// @Success      200      {object}  writer.Resp{data=object{list=[]types.Service}} "OK"
// @Failure      401      {object}  writer.ErrResp "Unauthorized"
// @Failure      500      {object}  writer.ErrResp "Internal Server Error"
// @Router       /services [get]
// @Security     ApiKeyAuth
func QueryServiceList(c *gin.Context) {
	resp, err := services.ServiceSvc().List(c.Request.Context())
	if err != nil {
		writer.ResponseErr(c, err, nil)
		return
	}

	writer.ResponseOk(c, writer.List(resp))
}

// QueryService godoc
// @Summary      Get service details
// @Description  Get detailed information about a specific service
// @Tags         services
// @Accept       json
// @Produce      json
// @Param        service_id  path     string  true   "Service ID"
// @Success      200         {object} writer.Resp{data=object{obj=types.Service}} "OK"
// @Failure      400         {object} writer.ErrResp "Bad Request"
// @Failure      401         {object} writer.ErrResp "Unauthorized"
// @Failure      404         {object} writer.ErrResp "Not Found"
// @Failure      500         {object} writer.ErrResp "Internal Server Error"
// @Router       /services/{service_id} [get]
// @Security     ApiKeyAuth
func QueryService(c *gin.Context) {
	var req struct {
		ServiceId string `uri:"service_id"`
	}
	if err := c.ShouldBindUri(&req); err != nil {
		writer.ResponseErr(c, uerror.Wrap(uerror.ErrInvalidParam, uerror.ErrCodeInvalidParam, "%v", err), nil)
		return
	}
	resp, err := services.ServiceSvc().Get(c.Request.Context(), req.ServiceId)
	if err != nil {
		writer.ResponseErr(c, err, nil)
		return
	}

	writer.ResponseOk(c, writer.Obj(resp))
}

// CreateService godoc
// @Summary      Create a new service
// @Description  Create a new service with the provided information
// @Tags         services
// @Accept       json
// @Produce      json
// @Param        request  body      types.CreateServiceReq  true  "Service information"
// @Success      200      {object}  writer.Resp{data=object{obj=types.Service}} "OK"
// @Failure      400      {object}  writer.ErrResp "Bad Request"
// @Failure      401      {object}  writer.ErrResp "Unauthorized"
// @Failure      500      {object}  writer.ErrResp "Internal Server Error"
// @Router       /services [post]
// @Security     ApiKeyAuth
func CreateService(c *gin.Context) {
	var req types.CreateServiceReq
	if err := c.ShouldBindJSON(&req); err != nil {
		writer.ResponseErr(c, err, nil)
		return
	}

	resp, err := services.ServiceSvc().Create(c.Request.Context(), req)
	if err != nil {
		writer.ResponseErr(c, err, nil)
	}

	writer.ResponseOk(c, writer.Obj(resp))
}

// UpdateService godoc
// @Summary      Update service
// @Description  Update an existing service's information
// @Tags         services
// @Accept       json
// @Produce      json
// @Param        service_id  path      string                true  "Service ID"
// @Param        request     body      types.UpdateServiceReq  true  "Updated service information"
// @Success      200         {object}  writer.Resp{data=object{}} "OK"
// @Failure      400         {object}  writer.Resp "Bad Request"
// @Failure      401         {object}  writer.Resp "Unauthorized"
// @Failure      404         {object}  writer.Resp "Not Found"
// @Failure      500         {object}  writer.Resp "Internal Server Error"
// @Router       /services/{service_id} [put]
// @Security     ApiKeyAuth
func UpdateService(c *gin.Context) {
	var uri struct {
		ServiceId string `uri:"service_id"`
	}
	if err := c.ShouldBindUri(&uri); err != nil {
		writer.ResponseErr(c, uerror.Wrap(uerror.ErrInvalidParam, uerror.ErrCodeInvalidParam, "%v", err), nil)
		return
	}

	var req types.UpdateServiceReq
	if err := c.ShouldBindJSON(&req); err != nil {
		writer.ResponseErr(c, err, nil)
		return
	}

	err := services.ServiceSvc().Update(c.Request.Context(), uri.ServiceId, req)
	if err != nil {
		writer.ResponseErr(c, err, nil)
		return
	}

	writer.ResponseOk(c, writer.Empty())
}

// DeleteService godoc
// @Summary      Delete a service
// @Description  Delete a service by ID
// @Tags         services
// @Accept       json
// @Produce      json
// @Param        service_id  path      string  true  "Service ID"
// @Success      200         {object}  writer.Resp{data=object{}} "OK"
// @Failure      400         {object}  writer.Resp "Bad Request"
// @Failure      401         {object}  writer.Resp "Unauthorized"
// @Failure      404         {object}  writer.Resp "Not Found"
// @Failure      500         {object}  writer.Resp "Internal Server Error"
// @Router       /services/{service_id} [delete]
// @Security     ApiKeyAuth
func DeleteService(c *gin.Context) {
	var uri struct {
		ServiceId string `uri:"service_id"`
	}
	if err := c.ShouldBindUri(&uri); err != nil {
		writer.ResponseErr(c, uerror.Wrap(uerror.ErrInvalidParam, uerror.ErrCodeInvalidParam, "%v", err), nil)
		return
	}

	err := services.ServiceSvc().Delete(c.Request.Context(), uri.ServiceId)
	if err != nil {
		writer.ResponseErr(c, err, nil)
		return
	}
	writer.ResponseOk(c, writer.Empty())
}

// UpdateServiceStaffs godoc
// @Summary      Update service staff assignments
// @Description  Update the staff members assigned to a service
// @Tags         services
// @Accept       json
// @Produce      json
// @Param        service_id  path      string                   true  "Service ID"
// @Param        request     body      types.UpdateServiceStaffReq  true  "Staff assignment information"
// @Success      200         {object}  writer.Resp{data=object{}} "OK"
// @Failure      400         {object}  writer.ErrResp "Bad Request"
// @Failure      401         {object}  writer.ErrResp "Unauthorized"
// @Failure      404         {object}  writer.ErrResp "Not Found"
// @Failure      500         {object}  writer.ErrResp "Internal Server Error"
// @Router       /services/{service_id}/staffs [put]
// @Security     ApiKeyAuth
func UpdateServiceStaffs(c *gin.Context) {
	var uri struct {
		ServiceId string `uri:"service_id"`
	}
	if err := c.ShouldBindUri(&uri); err != nil {
		writer.ResponseErr(c, uerror.Wrap(uerror.ErrInvalidParam, uerror.ErrCodeInvalidParam, "%v", err), nil)
		return
	}

	var req types.UpdateServiceStaffReq
	if err := c.ShouldBindJSON(&req); err != nil {
		writer.ResponseErr(c, err, nil)
		return
	}

	err := services.ServiceSvc().UpdateServiceStaff(c.Request.Context(), uri.ServiceId, req.AccountIds)
	if err != nil {
		writer.ResponseErr(c, err, nil)
		return
	}

	writer.ResponseOk(c, writer.Empty())
}
