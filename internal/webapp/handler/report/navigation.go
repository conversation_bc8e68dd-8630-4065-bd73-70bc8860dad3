package reporthandler

import (
	"pebble/internal/webapp/services/report"
	"pebble/pkg/localization"
	"pebble/pkg/ugin/writer"

	"github.com/gin-gonic/gin"
)

// QueryReportNavigation handles GET /api/v1/reports/navigation
// @Summary      Get report navigation
// @Description  Get a structured list of report types for navigation. Supports multiple languages based on Accept-Language header. Supported languages: en-US (English), zh-CN (Chinese). Defaults to English if no supported language is found.
// @Tags         reports
// @Accept       json
// @Produce      json
// @Param        Accept-Language  header  string  false  "Preferred language (e.g., en-US, zh-CN)"
// @Success      200  {object}  writer.Resp{data=object{obj=reporttypes.NavigationResp}}
// @Failure      401  {object}  writer.ErrResp
// @Failure      500  {object}  writer.ErrResp
// @Router       /reports/navigation [get]
// @Security     ApiKeyAuth
func QueryReportNavigation(c *gin.Context) {
	// Detect language from Accept-Language header
	supportedLanguages := localization.GetSupportedLanguages()
	defaultLanguage := localization.GetDefaultLanguage()
	detectedLanguage := localization.DetectLanguageFromRequest(c.Request, supportedLanguages, defaultLanguage)

	// Get multilingual navigation data
	navigationData := report.GetDefaultMultilingualNavigationData()
	navigation := navigationData.GetNavigationForLanguage(detectedLanguage)

	writer.ResponseOk(c, writer.Obj(navigation))
}
