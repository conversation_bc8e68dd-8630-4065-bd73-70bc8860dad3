package reporthandler

import (
	"fmt"
	"net/http"
	reportservice "pebble/internal/webapp/services/report"
	reporttypes "pebble/internal/webapp/types/report"
	"pebble/pkg/ugin/writer"

	"github.com/gin-gonic/gin"
)

func QueryReport(c *gin.Context) {
	reportKey := c.Param("report_key")

	var query reporttypes.ReportQuery
	if err := c.ShouldBindQuery(&query); err != nil {
		writer.ResponseErr(c, err, nil)
		return
	}

	resp, err := reportservice.NewReportService().GenerateReport(c.Request.Context(), reportKey, query)
	if err != nil {
		writer.ResponseErr(c, err, nil)
		return
	}

	writer.ResponseOk(c, writer.Obj(resp))
}

func ExportReport(c *gin.Context) {
	reportKey := c.Param("report_key")

	var query reporttypes.ReportQuery
	if err := c.ShouldBindQuery(&query); err != nil {
		writer.ResponseErr(c, err, nil)
		return
	}

	csvData, err := reportservice.NewReportService().ExportReportToCSV(c.Request.Context(), reportKey, query)
	if err != nil {
		writer.ResponseErr(c, err, nil)
		return
	}

	// Set headers for CSV download
	filename := fmt.Sprintf("%s_%s_to_%s.csv", reportKey, query.StartDate, query.EndDate)
	c.Header("Content-Disposition", fmt.Sprintf("attachment; filename=%s", filename))
	c.Data(http.StatusOK, "text/csv; charset=utf-8", csvData)
}

func GetDashboardReport(c *gin.Context) {
	var query reporttypes.ReportQuery
	if err := c.ShouldBindQuery(&query); err != nil {
		writer.ResponseErr(c, err, nil)
		return
	}

	resp, err := reportservice.NewDashboardReportSvc().GetDashboardReport(c.Request.Context(), query)
	if err != nil {
		writer.ResponseErr(c, err, nil)
		return
	}
	writer.ResponseOk(c, writer.Obj(resp))
}
