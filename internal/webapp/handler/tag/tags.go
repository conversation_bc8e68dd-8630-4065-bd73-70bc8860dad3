package taghandler

import (
	tagservices "pebble/internal/webapp/services/tag"
	tagtypes "pebble/internal/webapp/types/tag"
	"pebble/pkg/uerror"
	"pebble/pkg/ugin/writer"

	"github.com/gin-gonic/gin"
)

func QueryTags(c *gin.Context) {
	var params tagtypes.QueryTagsParams
	if err := c.BindQuery(&params); err != nil {
		writer.ResponseErr(c, uerror.Wrap(uerror.ErrInvalidParam, uerror.ErrCodeInvalidParam, "%v", err), nil)
		return
	}

	resp, err := tagservices.TagSvc().QueryTags(c.Request.Context(), params)
	if err != nil {
		writer.ResponseErr(c, err, nil)
		return
	}

	writer.ResponseOk(c, writer.List(resp))
}

func QueryTag(c *gin.Context) {
	var uri struct {
		TagId string `uri:"tag_id"`
	}
	if err := c.ShouldBindUri(&uri); err != nil {
		writer.ResponseErr(c, uerror.Wrap(uerror.ErrInvalidParam, uerror.ErrCodeInvalidParam, "%v", err), nil)
		return
	}

	resp, err := tagservices.TagSvc().QueryTag(c.Request.Context(), uri.TagId)
	if err != nil {
		writer.ResponseErr(c, err, nil)
		return
	}

	writer.ResponseOk(c, writer.Obj(resp))
}

func CreateTag(c *gin.Context) {
	var req tagtypes.CreateTagReq
	if err := c.ShouldBindJSON(&req); err != nil {
		writer.ResponseErr(c, uerror.Wrap(uerror.ErrInvalidParam, uerror.ErrCodeInvalidParam, "%v", err), nil)
		return
	}

	resp, err := tagservices.TagSvc().CreateTag(c.Request.Context(), req)
	if err != nil {
		writer.ResponseErr(c, err, nil)
		return
	}

	writer.ResponseOk(c, writer.Obj(resp))
}

func UpdateTag(c *gin.Context) {
	var uri struct {
		TagId string `uri:"tag_id"`
	}
	if err := c.ShouldBindUri(&uri); err != nil {
		writer.ResponseErr(c, uerror.Wrap(uerror.ErrInvalidParam, uerror.ErrCodeInvalidParam, "%v", err), nil)
		return
	}

	var req tagtypes.UpdateTagReq
	if err := c.ShouldBindJSON(&req); err != nil {
		writer.ResponseErr(c, uerror.Wrap(uerror.ErrInvalidParam, uerror.ErrCodeInvalidParam, "%v", err), nil)
		return
	}

	ctx := c.Request.Context()
	err := tagservices.TagSvc().UpdateTag(ctx, uri.TagId, req)
	if err != nil {
		writer.ResponseErr(c, err, nil)
		return
	}

	writer.ResponseOk(c, writer.Empty())
}

func DeleteTag(c *gin.Context) {
	var uri struct {
		TagId string `uri:"tag_id"`
	}
	if err := c.ShouldBindUri(&uri); err != nil {
		writer.ResponseErr(c, uerror.Wrap(uerror.ErrInvalidParam, uerror.ErrCodeInvalidParam, "%v", err), nil)
		return
	}

	ctx := c.Request.Context()
	err := tagservices.TagSvc().DeleteTag(ctx, uri.TagId)
	if err != nil {
		writer.ResponseErr(c, err, nil)
		return
	}

	writer.ResponseOk(c, writer.Empty())
}
