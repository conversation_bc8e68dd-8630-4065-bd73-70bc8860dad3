package taghandler

import (
	tagservices "pebble/internal/webapp/services/tag"
	tagtypes "pebble/internal/webapp/types/tag"
	"pebble/pkg/uerror"
	"pebble/pkg/ugin/writer"

	"github.com/gin-gonic/gin"
)

func CreateClientTag(c *gin.Context) {
	var uri struct {
		ClientId string `uri:"client_id" binding:"required" example:"client_123456"`
	}
	if err := c.ShouldBindUri(&uri); err != nil {
		writer.ResponseErr(c, uerror.Wrap(uerror.ErrInvalidParam, uerror.ErrCodeInvalidParam, "%v", err), nil)
		return
	}

	var req tagtypes.CreateClientTagReq
	if err := c.ShouldBindJSON(&req); err != nil {
		writer.ResponseErr(c, err, nil)
		return
	}
	req.ClientId = uri.ClientId

	resp, err := tagservices.ClientTagSvc().CreateClientTag(c.Request.Context(), req)
	if err != nil {
		writer.ResponseErr(c, err, nil)
		return
	}
	writer.ResponseOk(c, writer.List(resp))
}

func UpdateClientTags(c *gin.Context) {
	var uri struct {
		ClientId string `uri:"client_id" binding:"required"`
	}
	if err := c.ShouldBindUri(&uri); err != nil {
		writer.ResponseErr(c, uerror.Wrap(uerror.ErrInvalidParam, uerror.ErrCodeInvalidParam, "%v", err), nil)
		return
	}

	var req tagtypes.UpdateClientTagReq
	if err := c.ShouldBindJSON(&req); err != nil {
		writer.ResponseErr(c, err, nil)
		return
	}

	ctx := c.Request.Context()
	err := tagservices.ClientTagSvc().UpdateClientTags(ctx, uri.ClientId, req)
	if err != nil {
		writer.ResponseErr(c, err, nil)
		return
	}

	writer.ResponseOk(c, writer.Empty())
}

func QueryClientTags(c *gin.Context) {
	var uri struct {
		ClientId string `uri:"client_id" binding:"required"`
	}
	if err := c.ShouldBindUri(&uri); err != nil {
		writer.ResponseErr(c, uerror.Wrap(uerror.ErrInvalidParam, uerror.ErrCodeInvalidParam, "%v", err), nil)
		return
	}

	resp, err := tagservices.ClientTagSvc().QueryClientActiveTags(c.Request.Context(), uri.ClientId)
	if err != nil {
		writer.ResponseErr(c, err, nil)
		return
	}
	writer.ResponseOk(c, writer.List(resp))
}
