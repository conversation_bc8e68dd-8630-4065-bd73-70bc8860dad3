package handler

import (
	"bytes"
	"encoding/json"
	"fmt"
	"log"
	"net/http"

	"github.com/gin-gonic/gin"
)

func Booking(c *gin.Context) {
	var req struct {
		Name      string `json:"name"`
		Phone     string `json:"phone"`
		Date      string `json:"date"`
		Time      string `json:"time"`
		Shop      string `json:"shopId"`
		ServiceId string `json:"service_id"`
	}
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	messageText := fmt.Sprintf(
		"新的预约提醒:\n姓名: %s\n电话: %s\n门店: %s\n服务: %s\n日期: %s\n时间: %s",
		req.Name, req.Phone, req.Shop, req.ServiceId, req.Date, req.Time,
	)

	type FeishuMessage struct {
		MsgType string `json:"msg_type"`
		Content struct {
			Text string `json:"text"`
		} `json:"content"`
	}

	feishuMsg := FeishuMessage{
		MsgType: "text",
	}
	feishuMsg.Content.Text = messageText

	payload, err := json.Marshal(feishuMsg)
	if err != nil {
		log.Printf("Error marshalling feishu payload: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "internal server error"})
		return
	}

	webhookURL := "https://open.feishu.cn/open-apis/bot/v2/hook/d599e142-937e-4587-8f66-f4f075676bfb"
	resp, err := http.Post(webhookURL, "application/json", bytes.NewBuffer(payload))
	if err != nil {
		log.Printf("Error sending to feishu webhook: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "internal server error"})
		return
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		log.Printf("Feishu webhook returned non-200 status: %d", resp.StatusCode)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "failed to send notification"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"status": "ok"})
}
