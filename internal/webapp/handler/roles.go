package handler

import (
	"pebble/internal/webapp/services"
	"pebble/internal/webapp/types"
	"pebble/pkg/uerror"
	"pebble/pkg/ugin/writer"

	"github.com/gin-gonic/gin"
)

func QueryRoles(c *gin.Context) {
	var req types.QueryRolesParams
	if err := c.BindQuery(&req); err != nil {
		writer.ResponseErr(c, uerror.Wrap(uerror.ErrInvalidParam, uerror.ErrCodeInvalidParam, "%v", err), nil)
		return
	}

	resp, err := services.RoleSvc().QueryRoles(c.Request.Context(), req)
	if err != nil {
		writer.ResponseErr(c, err, nil)
		return
	}

	writer.ResponseOk(c, writer.List(resp))
}

func QueryRole(c *gin.Context) {
	var uri struct {
		RoleId string `uri:"role_id" binding:"required"`
	}
	if err := c.ShouldBindUri(&uri); err != nil {
		writer.ResponseErr(c, err, nil)
		return
	}

	resp, err := services.RoleSvc().QueryRole(c.Request.Context(), uri.RoleId)
	if err != nil {
		writer.ResponseErr(c, err, nil)
		return
	}

	writer.ResponseOk(c, writer.Obj(resp))
}

func CreateRole(c *gin.Context) {
	var req types.CreateRoleDetailReq
	if err := c.ShouldBindJSON(&req); err != nil {
		writer.ResponseErr(c, uerror.Wrap(uerror.ErrInvalidParam, uerror.ErrCodeInvalidParam, "%v", err), nil)
		return
	}

	resp, err := services.RoleSvc().CreateRole(c.Request.Context(), req)
	if err != nil {
		writer.ResponseErr(c, err, nil)
		return
	}
	writer.ResponseOk(c, writer.Obj(resp))
}

func UpdateRole(c *gin.Context) {
	var uri struct {
		RoleId string `uri:"role_id" binding:"required"`
	}
	if err := c.ShouldBindUri(&uri); err != nil {
		writer.ResponseErr(c, err, nil)
		return
	}

	var req types.UpdateRoleDetailReq
	if err := c.ShouldBindJSON(&req); err != nil {
		writer.ResponseErr(c, uerror.Wrap(uerror.ErrInvalidParam, uerror.ErrCodeInvalidParam, "%v", err), nil)
		return
	}

	err := services.RoleSvc().UpdateRole(c.Request.Context(), uri.RoleId, req)
	if err != nil {
		writer.ResponseErr(c, err, nil)
		return
	}
}

func DeleteRole(c *gin.Context) {
	var uri struct {
		RoleId string `uri:"role_id" binding:"required"`
	}
	if err := c.ShouldBindUri(&uri); err != nil {
		writer.ResponseErr(c, err, nil)
		return
	}

	err := services.RoleSvc().DeleteRole(c.Request.Context(), uri.RoleId)
	if err != nil {
		writer.ResponseErr(c, err, nil)
		return
	}

	writer.ResponseOk(c, writer.Empty())
}
