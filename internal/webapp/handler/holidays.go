package handler

import (
	"pebble/internal/webapp/services"
	"pebble/internal/webapp/types"
	"pebble/pkg/ugin/writer"

	"github.com/gin-gonic/gin"
)

// BatchCreateHoliday godoc
// @Summary      BatchCreateHoliday
// @Description  BatchCreateHoliday
// @Tags         Holidays
// @Accept       json
// @Produce      json
// @Param        req body []types.CreateHolidayReq true "req"
// @Success      200      {object}  writer.Resp{data=object{list=[]types.Holiday}}
// @Failure      400      {object}  writer.ErrResp
// @Failure      401      {object}  writer.ErrResp
// @Failure      500      {object}  writer.ErrResp
// @Router       /holidays [post]
func BatchCreateHoliday(c *gin.Context) {
	var req []types.CreateHolidayReq
	if err := c.ShouldBindJSON(&req); err != nil {
		writer.ResponseErr(c, err, nil)
		return
	}

	resp, err := services.HolidaySvc().BatchCreateHolidays(c.Request.Context(), req)
	if err != nil {
		writer.ResponseErr(c, err, nil)
		return
	}

	writer.ResponseOk(c, writer.List(resp))
}

// UpdateHoliday godoc
// @Summary      UpdateHoliday
// @Description  UpdateHoliday
// @Tags         Holidays
// @Accept       json
// @Produce      json
// @Param        holiday_id path string true "holiday_id"
// @Param        req body types.UpdateHolidayReq true "req"
// @Success      200      {object}  writer.Resp{data=object{}}
// @Failure      400      {object}  writer.ErrResp
// @Failure      401      {object}  writer.ErrResp
// @Failure      500      {object}  writer.ErrResp
// @Router       /holidays/{holiday_id} [put]
func UpdateHoliday(c *gin.Context) {
	var uri struct {
		HolidayId string `uri:"holiday_id" validate:"required" example:"hol_123456"`
	}
	if err := c.ShouldBindUri(&uri); err != nil {
		writer.ResponseErr(c, err, nil)
		return
	}

	var req types.UpdateHolidayReq
	if err := c.ShouldBindJSON(&req); err != nil {
		writer.ResponseErr(c, err, nil)
		return
	}

	err := services.HolidaySvc().UpdateHoliday(c.Request.Context(), uri.HolidayId, req)
	if err != nil {
		writer.ResponseErr(c, err, nil)
		return
	}

	writer.ResponseOk(c, writer.Empty())
}

// DeleteHoliday godoc
// @Summary      DeleteHoliday
// @Description  DeleteHoliday
// @Tags         Holidays
// @Accept       json
// @Produce      json
// @Param        holiday_id path string true "holiday_id"
// @Success      200      {object}  writer.Resp{data=object{}}
// @Failure      400      {object}  writer.ErrResp
// @Failure      401      {object}  writer.ErrResp
// @Failure      500      {object}  writer.ErrResp
// @Router       /holidays/{holiday_id} [delete]
func DeleteHoliday(c *gin.Context) {
	var uri struct {
		HolidayId string `uri:"holiday_id" validate:"required" example:"hol_123456"`
	}
	if err := c.ShouldBindUri(&uri); err != nil {
		writer.ResponseErr(c, err, nil)
		return
	}

	err := services.HolidaySvc().DeleteHoliday(c.Request.Context(), uri.HolidayId)
	if err != nil {
		writer.ResponseErr(c, err, nil)
		return
	}
	writer.ResponseOk(c, writer.Empty())
}

// QueryHolidaysList godoc
// @Summary      QueryHolidaysList
// @Description  QueryHolidaysList
// @Tags         Holidays
// @Accept       json
// @Produce      json
// @Success      200      {object}  writer.Resp{data=object{list=[]types.Holiday}}
// @Failure      400      {object}  writer.ErrResp
// @Failure      401      {object}  writer.ErrResp
// @Failure      500      {object}  writer.ErrResp
// @Router       /holidays [get]
func QueryHolidaysList(c *gin.Context) {
	resp, err := services.HolidaySvc().QueryHolidays(c.Request.Context())
	if err != nil {
		writer.ResponseErr(c, err, nil)
		return
	}

	writer.ResponseOk(c, writer.List(resp))
}
