package handler

import (
	"pebble/internal/webapp/services"
	"pebble/internal/webapp/types"
	"pebble/pkg/ugin/writer"

	"github.com/gin-gonic/gin"
)

// QueryAccount godoc
// @Summary      Query account information
// @Description  Get detailed information about the current account
// @Tags         accounts
// @Accept       json
// @Produce      json
// @Success      200      {object}  writer.Resp{data=object{obj=types.Account}}
// @Failure      401      {object}  writer.ErrResp
// @Failure      500      {object}  writer.ErrResp
// @Router       /accounts [get]
// @Security     ApiKeyAuth
func QueryAccount(c *gin.Context) {
	ctx := c.Request.Context()
	resp, err := services.AccountSvc().QueryAccountDetail(ctx)
	if err != nil {
		writer.ResponseErr(c, err, nil)
		return
	}
	writer.ResponseOk(c, writer.Obj(resp))
}

// UpdateAccount godoc
// @Summary      Update account information
// @Description  Update the current account's information
// @Tags         accounts
// @Accept       json
// @Produce      json
// @Param        request body types.UpdateAccountReq true "Account update request"
// @Success      200      {object}  writer.Resp{data=object{}}
// @Failure      400      {object}  writer.ErrResp
// @Failure      401      {object}  writer.ErrResp
// @Failure      500      {object}  writer.ErrResp
// @Router       /accounts [put]
// @Security     ApiKeyAuth
func UpdateAccount(c *gin.Context) {
	ctx := c.Request.Context()
	account := types.UpdateAccountReq{}
	if err := c.ShouldBindJSON(&account); err != nil {
		writer.ResponseErr(c, err, nil)
		return
	}
	err := services.AccountSvc().UpdateAccount(ctx, account)
	if err != nil {
		writer.ResponseErr(c, err, nil)
		return
	}

	writer.ResponseOk(c, writer.Empty())
}
