package websocket

import (
	"errors"
	"net/http"
	"strings"

	"pebble/pkg/ujwt"
	"pebble/pkg/uwebsocket"
)

// JWTAuthenticator JWT鉴权器
type JWTAuthenticator struct{}

// NewJWTAuthenticator 创建JWT鉴权器
func NewJWTAuthenticator() *JWTAuthenticator {
	return &JWTAuthenticator{}
}

// Authenticate 验证token并返回用户信息
func (a *JWTAuthenticator) Authenticate(token string) (*uwebsocket.UserInfo, error) {
	if token == "" {
		return nil, uwebsocket.ErrInvalidToken
	}

	// 解析JWT token
	claims, err := ujwt.ParseToken(token)
	if err != nil {
		return nil, uwebsocket.ErrTokenExpired
	}

	// 转换为WebSocket用户信息
	userInfo := &uwebsocket.UserInfo{
		TenantId:   claims.TenantId,
		LocationId: claims.LocationId,
		AccountId:  claims.AccountId,
	}

	return userInfo, nil
}

// HTTPTokenExtractor HTTP请求token提取器
type HTTPTokenExtractor struct{}

// NewHTTPTokenExtractor 创建HTTP token提取器
func NewHTTPTokenExtractor() *HTTPTokenExtractor {
	return &HTTPTokenExtractor{}
}

// ExtractToken 从HTTP请求中提取token
func (e *HTTPTokenExtractor) ExtractToken(r interface{}) (string, error) {
	req, ok := r.(*http.Request)
	if !ok {
		return "", errors.New("invalid request type")
	}

	// 方式1: 从URL参数中获取token
	if token := req.URL.Query().Get("token"); token != "" {
		return token, nil
	}

	// 方式2: 从Authorization头中获取token
	authHeader := req.Header.Get("Authorization")
	if authHeader != "" {
		parts := strings.SplitN(authHeader, " ", 2)
		if len(parts) == 2 && parts[0] == "Bearer" {
			return parts[1], nil
		}
	}

	// 方式3: 从WebSocket子协议中获取token
	protocols := req.Header.Get("Sec-WebSocket-Protocol")
	if protocols != "" {
		protocolList := strings.Split(protocols, ",")
		for _, protocol := range protocolList {
			protocol = strings.TrimSpace(protocol)
			if strings.HasPrefix(protocol, "access_token.") {
				return strings.TrimPrefix(protocol, "access_token."), nil
			}
		}
	}

	return "", uwebsocket.ErrInvalidToken
}
