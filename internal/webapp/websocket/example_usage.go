package websocket

// // ExampleUsage 展示如何使用WebSocket推送功能
// func ExampleUsage() {
// 	// 获取推送器实例
// 	pusher := GetGlobalPusher()
// 	if pusher == nil {
// 		log.Println("WebSocket pusher not initialized")
// 		return
// 	}

// 	// 示例1: 推送预约提醒
// 	appointmentData := map[string]interface{}{
// 		"appointment_id": "apt_123456",
// 		"client_name":    "<PERSON>",
// 		"service_name":   "Haircut",
// 		"scheduled_time": "2025-01-20T10:00:00Z",
// 		"message":        "Your appointment is in 15 minutes",
// 	}

// 	err := pusher.PushAppointmentReminder("tenant_123", "loc_456", "acc_789", appointmentData)
// 	if err != nil {
// 		log.Printf("Failed to push appointment reminder: %v", err)
// 	}

// 	// 示例2: 推送支付完成通知
// 	paymentData := map[string]interface{}{
// 		"payment_id":     "pay_123456",
// 		"amount":         50.00,
// 		"currency":       "USD",
// 		"client_name":    "<PERSON>",
// 		"appointment_id": "apt_456789",
// 	}

// 	err = pusher.PushPaymentCompleted("tenant_123", "loc_456", paymentData)
// 	if err != nil {
// 		log.Printf("Failed to push payment completed: %v", err)
// 	}

// 	// 示例3: 推送系统维护通知
// 	maintenanceData := map[string]interface{}{
// 		"message":    "System maintenance will begin in 10 minutes",
// 		"duration":   "30 minutes",
// 		"start_time": "2025-01-20T02:00:00Z",
// 	}

// 	err = pusher.PushSystemMaintenance(maintenanceData)
// 	if err != nil {
// 		log.Printf("Failed to push system maintenance: %v", err)
// 	}

// 	// 示例4: 推送自定义消息
// 	customData := map[string]interface{}{
// 		"title":   "New Feature Available",
// 		"message": "Check out our new booking feature!",
// 		"action":  "view_features",
// 	}

// 	err = pusher.PushToTenant("tenant_123", uwebsocket.TypeNotify, "feature.announcement", customData)
// 	if err != nil {
// 		log.Printf("Failed to push custom message: %v", err)
// 	}

// 	// 示例5: 推送高优先级消息
// 	urgentData := map[string]interface{}{
// 		"title":   "Urgent: System Alert",
// 		"message": "Please check your appointments immediately",
// 		"level":   "critical",
// 	}

// 	err = pusher.PushWithPriority("tenant_123", "loc_456", "acc_789",
// 		uwebsocket.TypeNotify, "system.alert", urgentData, 5)
// 	if err != nil {
// 		log.Printf("Failed to push urgent message: %v", err)
// 	}

// 	// 示例6: 向账户在所有location推送消息（支持跨location）
// 	crossLocationData := map[string]interface{}{
// 		"title":   "Account Update",
// 		"message": "Your profile has been updated across all locations",
// 		"type":    "profile_update",
// 	}

// 	err = pusher.PushAppointmentReminderAllLocations("tenant_123", "acc_789", crossLocationData)
// 	if err != nil {
// 		log.Printf("Failed to push cross-location message: %v", err)
// 	}

// 	// 示例7: 向账户在所有location推送新消息通知
// 	err = pusher.PushNewMessageAllLocations("tenant_123", "acc_789", map[string]interface{}{
// 		"message": "You have a new message available in all your locations",
// 		"urgent":  true,
// 	})
// 	if err != nil {
// 		log.Printf("Failed to push new message to all locations: %v", err)
// 	}

// 	log.Println("WebSocket push examples completed")
// }

// // GetGlobalPusher 获取全局推送器实例（需要在server.go中实现）
// func GetGlobalPusher() *Pusher {
// 	// 这个函数应该在server.go中实现，这里只是示例
// 	// return GetWebSocketPusher()
// 	return nil
// }

// // ExampleBusinessIntegration 展示如何在业务代码中集成WebSocket推送
// func ExampleBusinessIntegration() {
// 	// 在预约服务中的使用示例

// 	// 当创建新预约时
// 	onAppointmentCreated := func(tenantId, locationId string, appointment interface{}) {
// 		pusher := GetGlobalPusher()
// 		if pusher != nil {
// 			pusher.PushAppointmentCreated(tenantId, locationId, appointment)
// 		}
// 	}

// 	// 当预约状态更新时
// 	onAppointmentUpdated := func(tenantId, locationId string, appointment interface{}) {
// 		pusher := GetGlobalPusher()
// 		if pusher != nil {
// 			pusher.PushAppointmentUpdated(tenantId, locationId, appointment)
// 		}
// 	}

// 	// 当需要发送预约提醒时
// 	onSendReminder := func(tenantId, locationId, accountId string, reminder interface{}) {
// 		pusher := GetGlobalPusher()
// 		if pusher != nil {
// 			pusher.PushAppointmentReminder(tenantId, locationId, accountId, reminder)
// 		}
// 	}

// 	// 在支付服务中的使用示例
// 	onPaymentCompleted := func(tenantId, locationId string, payment interface{}) {
// 		pusher := GetGlobalPusher()
// 		if pusher != nil {
// 			pusher.PushPaymentCompleted(tenantId, locationId, payment)
// 		}
// 	}

// 	// 在消息服务中的使用示例
// 	onNewMessage := func(tenantId, locationId, accountId string, message interface{}) {
// 		pusher := GetGlobalPusher()
// 		if pusher != nil {
// 			pusher.PushNewMessage(tenantId, locationId, accountId, message)
// 		}
// 	}

// 	// 使用这些回调函数
// 	_ = onAppointmentCreated
// 	_ = onAppointmentUpdated
// 	_ = onSendReminder
// 	_ = onPaymentCompleted
// 	_ = onNewMessage

// 	log.Println("Business integration examples defined")
// }

// // ExampleWebSocketStats 展示如何获取WebSocket统计信息
// func ExampleWebSocketStats(ctx context.Context) {
// 	service := GetGlobalService()
// 	if service == nil {
// 		log.Println("WebSocket service not initialized")
// 		return
// 	}

// 	stats := service.GetStats()
// 	log.Printf("WebSocket Stats: %+v", stats)

// 	// 输出示例:
// 	// WebSocket Stats: {
// 	//   TotalConnections: 150,
// 	//   TenantConnections: 10,
// 	//   LocationConnections: 25,
// 	//   AccountConnections: 150,
// 	//   Running: true
// 	// }
// }

// // GetGlobalService 获取全局WebSocket服务实例（需要在server.go中实现）
// func GetGlobalService() *Service {
// 	// 这个函数应该在server.go中实现，这里只是示例
// 	// return GetWebSocketService()
// 	return nil
// }

// // ExampleClientConnection 展示客户端如何连接WebSocket
// func ExampleClientConnection() {
// 	// JavaScript客户端连接示例:
// 	/*
// 		// 方式1: 通过URL参数传递token
// 		const token = "your_jwt_token_here";
// 		const ws = new WebSocket(`ws://localhost:8082/api/v1/ws?token=${token}`);

// 		// 方式2: 通过子协议传递token
// 		const ws2 = new WebSocket("ws://localhost:8082/api/v1/ws", [`access_token.${token}`]);

// 		ws.onopen = function(event) {
// 			console.log("WebSocket connected");
// 		};

// 		ws.onmessage = function(event) {
// 			const message = JSON.parse(event.data);
// 			console.log("Received message:", message);

// 			// 处理不同类型的消息
// 			switch(message.type) {
// 				case "notify":
// 					handleNotification(message);
// 					break;
// 				case "heartbeat":
// 					handleHeartbeat(message);
// 					break;
// 				case "error":
// 					handleError(message);
// 					break;
// 			}
// 		};

// 		ws.onclose = function(event) {
// 			console.log("WebSocket disconnected");
// 		};

// 		ws.onerror = function(error) {
// 			console.error("WebSocket error:", error);
// 		};

// 		// 发送心跳响应
// 		function handleHeartbeat(message) {
// 			if (message.event === "ping") {
// 				ws.send(JSON.stringify({
// 					id: generateId(),
// 					type: "heartbeat",
// 					event: "pong",
// 					data: { status: "alive" },
// 					timestamp: Date.now()
// 				}));
// 			}
// 		}

// 		// 处理通知消息
// 		function handleNotification(message) {
// 			switch(message.event) {
// 				case "appointment.reminder":
// 					showAppointmentReminder(message.data);
// 					break;
// 				case "payment.completed":
// 					showPaymentNotification(message.data);
// 					break;
// 				case "system.maintenance":
// 					showMaintenanceAlert(message.data);
// 					break;
// 			}
// 		}
// 	*/

// 	log.Println("Client connection examples provided in comments")
// }
