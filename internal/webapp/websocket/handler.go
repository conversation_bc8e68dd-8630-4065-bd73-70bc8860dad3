package websocket

import (
	"context"
	"pebble/internal/webapp/config"
	"pebble/pkg/uerror"
	"pebble/pkg/ugin/writer"
	"pebble/pkg/ulog"
	"pebble/pkg/uwebsocket"

	"github.com/coder/websocket"
	"github.com/gin-gonic/gin"
)

// Handler WebSocket处理器
type Handler struct {
	service        *Service
	tokenExtractor uwebsocket.TokenExtractor
}

// NewHandler 创建WebSocket处理器
func NewHandler(service *Service) *Handler {
	return &Handler{
		service:        service,
		tokenExtractor: NewHTTPTokenExtractor(),
	}
}

// HandleWebSocket 处理WebSocket连接升级
func (h *Handler) HandleWebSocket(c *gin.Context) {
	ctx := c.Request.Context()

	// 提取token
	token, err := h.tokenExtractor.ExtractToken(c.Request)
	if err != nil {
		ulog.Errorln(ctx, "extract token error", err)
		writer.ResponseErr(c, uerror.ErrInvalidToken, nil)
		return
	}

	// 验证token
	userInfo, err := h.service.authenticator.Authenticate(token)
	if err != nil {
		ulog.Errorln(ctx, "authenticate error", err)
		writer.ResponseErr(c, uerror.ErrInvalidToken, nil)
		return
	}

	// 根据环境配置Origin验证策略
	acceptOptions := &websocket.AcceptOptions{
		Subprotocols: []string{"access_token"},
	}

	// 开发环境跳过Origin验证，生产环境使用域名验证
	if config.C.App.Env == "development" || config.C.App.Env == "dev" {
		acceptOptions.InsecureSkipVerify = true
	} else {
		// 生产环境配置允许的Origin
		acceptOptions.OriginPatterns = []string{
			config.C.App.Domain,
			"https://" + config.C.App.Domain,
			"http://localhost:*",  // 允许本地测试
			"https://localhost:*", // 允许本地HTTPS测试
		}
	}

	// 升级到WebSocket连接
	conn, err := websocket.Accept(c.Writer, c.Request, acceptOptions)
	if err != nil {
		ulog.Errorln(ctx, "websocket accept error", err)
		writer.ResponseErr(c, uerror.ErrCommon, nil)
		return
	}

	// 创建连接对象
	wsConn := uwebsocket.NewConnection(conn, h.service.hub, userInfo)

	// 注册连接
	h.service.hub.RegisterConnection(wsConn)

	// 启动连接处理
	newctx := ulog.NewContext(context.Background(), c.Request.Context())
	wsConn.Start(newctx)

	ulog.Infof(newctx, "WebSocket connection established: %s (tenant:%s, location:%s, account:%s)",
		wsConn.ID, userInfo.TenantId, userInfo.LocationId, userInfo.AccountId)
}

// HandleWebSocketStats 处理WebSocket统计信息查询
func (h *Handler) HandleWebSocketStats(c *gin.Context) {
	stats := h.service.hub.GetStats()
	writer.ResponseOk(c, writer.Obj(stats))
}
