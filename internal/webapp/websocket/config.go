package websocket

import (
	"errors"
	"time"
)

// Config WebSocket配置
type Config struct {
	// 连接配置
	ReadTimeout    time.Duration `mapstructure:"read_timeout"`    // 读取超时
	WriteTimeout   time.Duration `mapstructure:"write_timeout"`   // 写入超时
	PingInterval   time.Duration `mapstructure:"ping_interval"`   // 心跳间隔
	PongTimeout    time.Duration `mapstructure:"pong_timeout"`    // 心跳超时
	MaxConnections int           `mapstructure:"max_connections"` // 最大连接数

	// 清理配置
	CleanupInterval time.Duration `mapstructure:"cleanup_interval"` // 清理间隔

	// 缓冲区配置
	SendBufferSize      int `mapstructure:"send_buffer_size"`      // 发送缓冲区大小
	BroadcastBufferSize int `mapstructure:"broadcast_buffer_size"` // 广播缓冲区大小
}

// DefaultConfig 默认配置
func DefaultConfig() *Config {
	return &Config{
		ReadTimeout:         60 * time.Second,
		WriteTimeout:        10 * time.Second,
		PingInterval:        54 * time.Second,
		PongTimeout:         60 * time.Second,
		MaxConnections:      10000,
		CleanupInterval:     30 * time.Second,
		SendBufferSize:      256,
		BroadcastBufferSize: 1000,
	}
}

// Validate 验证配置
func (c *Config) Validate() error {
	if c.ReadTimeout <= 0 {
		return errors.New("read_timeout must be positive")
	}
	if c.WriteTimeout <= 0 {
		return errors.New("write_timeout must be positive")
	}
	if c.PingInterval <= 0 {
		return errors.New("ping_interval must be positive")
	}
	if c.PongTimeout <= 0 {
		return errors.New("pong_timeout must be positive")
	}
	if c.MaxConnections <= 0 {
		return errors.New("max_connections must be positive")
	}
	if c.CleanupInterval <= 0 {
		return errors.New("cleanup_interval must be positive")
	}
	if c.SendBufferSize <= 0 {
		return errors.New("send_buffer_size must be positive")
	}
	if c.BroadcastBufferSize <= 0 {
		return errors.New("broadcast_buffer_size must be positive")
	}

	// 逻辑验证
	if c.PongTimeout <= c.PingInterval {
		return errors.New("pong_timeout should be greater than ping_interval")
	}

	return nil
}

// 错误定义
var (
	ErrMaxConnectionsReached = errors.New("maximum connections reached")
	ErrBroadcastChannelFull  = errors.New("broadcast channel is full")
	ErrConnectionClosed      = errors.New("connection is closed")
	ErrInvalidToken          = errors.New("invalid token")
	ErrTokenExpired          = errors.New("token expired")
)
