package websocket

import (
	"context"

	"pebble/pkg/ulog"
	"pebble/pkg/uwebsocket"
)

// WebAppLogger webapp日志适配器
type WebAppLogger struct{}

// NewWebAppLogger 创建webapp日志适配器
func NewWebAppLogger() uwebsocket.Logger {
	return &WebAppLogger{}
}

// Info 记录信息日志
func (l *WebAppLogger) Info(ctx context.Context, msg string, fields map[string]interface{}) {
	if fields != nil {
		ulog.Infof(ctx, "%s: %+v", msg, fields)
	} else {
		ulog.Info(ctx, msg)
	}
}

// Error 记录错误日志
func (l *WebAppLogger) Error(ctx context.Context, msg string, err error) {
	if err != nil {
		ulog.Errorln(ctx, msg, err)
	} else {
		ulog.Error(ctx, msg)
	}
}
