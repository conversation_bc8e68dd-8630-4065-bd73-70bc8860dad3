package websocket

import (
	"pebble/pkg/uwebsocket"
)

var wsPusher *Pusher

// Pusher 全局推送器，供业务代码调用
type Pusher struct {
	service *Service
}

// GetWebSocketPusher 获取WebSocket推送器实例
func GetWebSocketPusher() *Pusher {
	return wsPusher
}

// NewPusher 创建推送器
func NewPusher(service *Service) *Pusher {
	return &Pusher{
		service: service,
	}
}

func (p *Pusher) PushStriepTerminalSucceeded(tenantId, locationId, accountId string, data interface{}) error {
	return p.service.PushNotificationToAccount(tenantId, locationId, accountId, "stripe-terminal.succeeded", data)
}

func (p *Pusher) PushStriepTerminalFailed(tenantId, locationId, accountId string, data interface{}) error {
	return p.service.PushNotificationToAccount(tenantId, locationId, accountId, "stripe-terminal.failed", data)
}

// 预约相关推送

// PushAppointmentReminder 推送预约提醒
func (p *Pusher) PushAppointmentReminder(tenantId, locationId, accountId string, data interface{}) error {
	return p.service.PushNotificationToAccount(tenantId, locationId, accountId, "appointment.reminder", data)
}

// PushAppointmentReminderAllLocations 向账户在所有location推送预约提醒
func (p *Pusher) PushAppointmentReminderAllLocations(tenantId, accountId string, data interface{}) error {
	return p.service.PushNotificationToAccountAllLocations(tenantId, accountId, "appointment.reminder", data)
}

// PushAppointmentCreated 推送预约创建通知
func (p *Pusher) PushAppointmentCreated(tenantId, locationId string, data interface{}) error {
	return p.service.PushNotificationToLocation(tenantId, locationId, "appointment.created", data)
}

// PushAppointmentUpdated 推送预约更新通知
func (p *Pusher) PushAppointmentUpdated(tenantId, locationId string, data interface{}) error {
	return p.service.PushNotificationToLocation(tenantId, locationId, "appointment.updated", data)
}

// PushAppointmentCancelled 推送预约取消通知
func (p *Pusher) PushAppointmentCancelled(tenantId, locationId string, data interface{}) error {
	return p.service.PushNotificationToLocation(tenantId, locationId, "appointment.cancelled", data)
}

// 支付相关推送

// PushPaymentCompleted 推送支付完成通知
func (p *Pusher) PushPaymentCompleted(tenantId, locationId string, data interface{}) error {
	return p.service.PushNotificationToLocation(tenantId, locationId, "payment.completed", data)
}

// PushPaymentFailed 推送支付失败通知
func (p *Pusher) PushPaymentFailed(tenantId, locationId, accountId string, data interface{}) error {
	return p.service.PushNotificationToAccount(tenantId, locationId, accountId, "payment.failed", data)
}

// 客户相关推送

// PushClientUpdated 推送客户信息更新通知
func (p *Pusher) PushClientUpdated(tenantId, locationId string, data interface{}) error {
	return p.service.SendEventToLocation(tenantId, locationId, "client.updated", data)
}

// PushClientCreated 推送新客户创建通知
func (p *Pusher) PushClientCreated(tenantId, locationId string, data interface{}) error {
	return p.service.SendEventToLocation(tenantId, locationId, "client.created", data)
}

// 系统相关推送

// PushSystemNotification 推送系统通知
func (p *Pusher) PushSystemNotification(tenantId string, data interface{}) error {
	return p.service.PushNotificationToTenant(tenantId, "system.notification", data)
}

// PushSystemMaintenance 推送系统维护通知
func (p *Pusher) PushSystemMaintenance(data interface{}) error {
	message := uwebsocket.NewMessage(uwebsocket.TypeBroadcast, "system.maintenance").
		Data(data).
		Priority(4).
		Build()

	return p.service.GetHub().Broadcast(message)
}

// 消息相关推送

// PushNewMessage 推送新消息通知
func (p *Pusher) PushNewMessage(tenantId, locationId, accountId string, data interface{}) error {
	return p.service.PushNotificationToAccount(tenantId, locationId, accountId, "message.new", data)
}

// PushNewMessageAllLocations 向账户在所有location推送新消息通知
func (p *Pusher) PushNewMessageAllLocations(tenantId, accountId string, data interface{}) error {
	return p.service.PushNotificationToAccountAllLocations(tenantId, accountId, "message.new", data)
}

// PushMessageStatusUpdate 推送消息状态更新
func (p *Pusher) PushMessageStatusUpdate(tenantId, locationId string, data interface{}) error {
	return p.service.SendEventToLocation(tenantId, locationId, "message.status_update", data)
}

// 通用推送方法

// PushToTenant 向租户推送自定义消息
func (p *Pusher) PushToTenant(tenantId string, msgType, event string, data interface{}) error {
	return p.service.PushToTenant(tenantId, msgType, event, data)
}

// PushToLocation 向位置推送自定义消息
func (p *Pusher) PushToLocation(tenantId, locationId string, msgType, event string, data interface{}) error {
	return p.service.PushToLocation(tenantId, locationId, msgType, event, data)
}

// PushToAccount 向账户推送自定义消息
func (p *Pusher) PushToAccount(tenantId, locationId, accountId string, msgType, event string, data interface{}) error {
	return p.service.PushToAccount(tenantId, locationId, accountId, msgType, event, data)
}

// 高级推送方法

// PushWithPriority 推送带优先级的消息
func (p *Pusher) PushWithPriority(tenantId, locationId, accountId string, msgType, event string, data interface{}, priority int) error {
	message := uwebsocket.NewMessage(msgType, event).
		Data(data).
		ToAccount(tenantId, locationId, accountId).
		Priority(priority).
		Build()

	return p.service.GetHub().Broadcast(message)
}

// PushReliable 推送可靠消息
func (p *Pusher) PushReliable(tenantId, locationId, accountId string, msgType, event string, data interface{}) error {
	message := uwebsocket.NewMessage(msgType, event).
		Data(data).
		ToAccount(tenantId, locationId, accountId).
		Reliable().
		Build()

	return p.service.GetHub().Broadcast(message)
}

// PushWithTTL 推送带生存时间的消息
func (p *Pusher) PushWithTTL(tenantId, locationId, accountId string, msgType, event string, data interface{}, ttlSeconds int64) error {
	message := uwebsocket.NewMessage(msgType, event).
		Data(data).
		ToAccount(tenantId, locationId, accountId).
		TTL(ttlSeconds).
		Build()

	return p.service.GetHub().Broadcast(message)
}
