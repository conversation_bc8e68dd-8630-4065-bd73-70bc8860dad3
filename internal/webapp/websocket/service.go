package websocket

import (
	"context"

	"pebble/pkg/uwebsocket"
)

// Service WebSocket服务
type Service struct {
	hub           *uwebsocket.Hub
	config        *uwebsocket.Config
	authenticator uwebsocket.Authenticator
	logger        uwebsocket.Logger
	handler       *Handler
}

// NewService 创建WebSocket服务
func NewService(config *uwebsocket.Config) *Service {
	// 创建鉴权器
	authenticator := NewJWTAuthenticator()

	// 创建日志器
	logger := NewWebAppLogger()

	// 创建Hub
	hub := uwebsocket.NewHub(config, authenticator)
	hub.SetLogger(logger)

	service := &Service{
		hub:           hub,
		config:        config,
		authenticator: authenticator,
		logger:        logger,
	}

	// 创建Handler
	service.handler = NewHandler(service)

	return service
}

// Start 启动WebSocket服务
func (s *Service) Start(ctx context.Context) {
	s.hub.Start(ctx)
}

// GetHub 获取Hub实例
func (s *Service) GetHub() *uwebsocket.Hub {
	return s.hub
}

// GetHandler 获取HTTP处理器
func (s *Service) GetHandler() *Handler {
	return s.handler
}

// GetStats 获取统计信息
func (s *Service) GetStats() uwebsocket.HubStats {
	return s.hub.GetStats()
}

// PushToTenant 向租户推送消息
func (s *Service) PushToTenant(tenantId string, msgType, event string, data interface{}) error {
	message := uwebsocket.NewMessage(msgType, event).
		Data(data).
		ToTenant(tenantId).
		Build()

	return s.hub.BroadcastToTenant(tenantId, message)
}

// PushToLocation 向位置推送消息
func (s *Service) PushToLocation(tenantId, locationId string, msgType, event string, data interface{}) error {
	message := uwebsocket.NewMessage(msgType, event).
		Data(data).
		ToLocation(tenantId, locationId).
		Build()

	return s.hub.BroadcastToLocation(tenantId, locationId, message)
}

// PushToAccount 向账户推送消息
func (s *Service) PushToAccount(tenantId, locationId, accountId string, msgType, event string, data interface{}) error {
	message := uwebsocket.NewMessage(msgType, event).
		Data(data).
		ToAccount(tenantId, locationId, accountId).
		Build()

	return s.hub.BroadcastToAccount(tenantId, locationId, accountId, message)
}

// PushNotificationToTenant 向租户推送通知
func (s *Service) PushNotificationToTenant(tenantId string, event string, data interface{}) error {
	return s.PushToTenant(tenantId, uwebsocket.TypeNotify, event, data)
}

// PushNotificationToLocation 向位置推送通知
func (s *Service) PushNotificationToLocation(tenantId, locationId string, event string, data interface{}) error {
	return s.PushToLocation(tenantId, locationId, uwebsocket.TypeNotify, event, data)
}

// PushNotificationToAccount 向账户推送通知
func (s *Service) PushNotificationToAccount(tenantId, locationId, accountId string, event string, data interface{}) error {
	return s.PushToAccount(tenantId, locationId, accountId, uwebsocket.TypeNotify, event, data)
}

// BroadcastToTenant 向租户广播消息
func (s *Service) BroadcastToTenant(tenantId string, event string, data interface{}) error {
	return s.PushToTenant(tenantId, uwebsocket.TypeBroadcast, event, data)
}

// BroadcastToLocation 向位置广播消息
func (s *Service) BroadcastToLocation(tenantId, locationId string, event string, data interface{}) error {
	return s.PushToLocation(tenantId, locationId, uwebsocket.TypeBroadcast, event, data)
}

// SendEventToTenant 向租户发送事件
func (s *Service) SendEventToTenant(tenantId string, event string, data interface{}) error {
	return s.PushToTenant(tenantId, uwebsocket.TypeEvent, event, data)
}

// SendEventToLocation 向位置发送事件
func (s *Service) SendEventToLocation(tenantId, locationId string, event string, data interface{}) error {
	return s.PushToLocation(tenantId, locationId, uwebsocket.TypeEvent, event, data)
}

// SendEventToAccount 向账户发送事件
func (s *Service) SendEventToAccount(tenantId, locationId, accountId string, event string, data interface{}) error {
	return s.PushToAccount(tenantId, locationId, accountId, uwebsocket.TypeEvent, event, data)
}

// PushToAccountAllLocations 向账户在所有location的连接推送消息
func (s *Service) PushToAccountAllLocations(tenantId, accountId string, msgType, event string, data interface{}) error {
	message := uwebsocket.NewMessage(msgType, event).
		Data(data).
		Build()

	return s.hub.BroadcastToAccountAllLocations(tenantId, accountId, message)
}

// PushNotificationToAccountAllLocations 向账户在所有location推送通知
func (s *Service) PushNotificationToAccountAllLocations(tenantId, accountId string, event string, data interface{}) error {
	return s.PushToAccountAllLocations(tenantId, accountId, uwebsocket.TypeNotify, event, data)
}

// SendEventToAccountAllLocations 向账户在所有location发送事件
func (s *Service) SendEventToAccountAllLocations(tenantId, accountId string, event string, data interface{}) error {
	return s.PushToAccountAllLocations(tenantId, accountId, uwebsocket.TypeEvent, event, data)
}
