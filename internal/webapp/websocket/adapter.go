package websocket

import (
	"context"
	"os"

	"github.com/redis/go-redis/v9"
	"pebble/pkg/uwebsocket"
)

// HubAdapter 单机Hub适配器，实现DistributedHub接口
type HubAdapter struct {
	*uwebsocket.Hub
	instanceID string
}

// NewHubAdapter 创建Hub适配器
func NewHubAdapter(hub *uwebsocket.Hub) *HubAdapter {
	hostname, _ := os.Hostname()
	pid := os.Getpid()
	instanceID := hostname + "-" + string(rune(pid))
	
	return &HubAdapter{
		Hub:        hub,
		instanceID: instanceID,
	}
}

// SetInstanceID 设置实例ID
func (h *HubAdapter) SetInstanceID(instanceID string) {
	h.instanceID = instanceID
}

// GetInstanceID 获取实例ID
func (h *HubAdapter) GetInstanceID() string {
	return h.instanceID
}

// EnableDistributed 启用分布式模式（单机版本不支持）
func (h *HubAdapter) EnableDistributed(redisClient *redis.Client) error {
	// 单机版本不支持分布式，直接返回nil
	return nil
}

// DisableDistributed 禁用分布式模式（单机版本不需要）
func (h *HubAdapter) DisableDistributed() {
	// 单机版本不需要禁用
}

// IsDistributed 检查是否启用分布式（单机版本返回false）
func (h *HubAdapter) IsDistributed() bool {
	return false
}

// GetDistributedStats 获取分布式统计信息
func (h *HubAdapter) GetDistributedStats() uwebsocket.DistributedStats {
	localStats := h.Hub.GetStats()
	
	return uwebsocket.DistributedStats{
		HubStats:       localStats,
		InstanceID:     h.instanceID,
		Distributed:    false,
		RedisConnected: false,
	}
}
