package webapp

import (
	"net/http"
	"pebble/internal/webapp/config"
	"pebble/internal/webapp/handler"
	appointmenthandler "pebble/internal/webapp/handler/appointment"
	clienthandler "pebble/internal/webapp/handler/client"
	dashboardhandler "pebble/internal/webapp/handler/dashboard"
	messagehandler "pebble/internal/webapp/handler/message"
	paymenthandler "pebble/internal/webapp/handler/payment"
	reporthandler "pebble/internal/webapp/handler/report"
	"pebble/internal/webapp/handler/stripe"
	taghandler "pebble/internal/webapp/handler/tag"
	workerhandler "pebble/internal/webapp/handler/workers"
	"pebble/pkg/udb"
	"pebble/pkg/ugin/middleware"
	"pebble/pkg/ugin/writer"
	"pebble/pkg/version"

	"github.com/gin-gonic/gin"
	swaggerFiles "github.com/swaggo/files"
	ginSwagger "github.com/swaggo/gin-swagger"
)

// todo 后续用Google/wire重构为依赖注入形式

func router() *gin.Engine {
	gin.SetMode(config.C.App.Mode)
	r := gin.Default()
	r.Use(middleware.Cors(), middleware.RecoveryLog()) // gzip.Gzip(gzip.DefaultCompression),

	r.GET("/version", func(c *gin.Context) {
		writer.ResponseOk(c, writer.Obj(version.Get()))
	})
	r.GET("/health", func(c *gin.Context) {
		err := udb.HealthCheck(c.Request.Context())
		if err != nil {
			writer.ResponseErr(c, err, nil)
			return
		}
		c.Status(http.StatusOK)
	})

	// Swagger documentation endpoint
	r.GET("/swagger/*any", ginSwagger.WrapHandler(swaggerFiles.Handler))

	r.Use(middleware.TraceLogger(), middleware.ParamLogger())

	// todo temporary
	booking := r.Group("/booking/v1")
	{
		booking.POST("/submit", handler.Booking)
	}

	api := r.Group("/api")

	// Internal worker routes, protected by a secret header
	internal := api.Group("/internal")
	internal.Use(middleware.InternalAuth())
	{
		workers := internal.Group("/workers")
		workers.POST("/trigger-appointment-reminders", workerhandler.TriggerAppointmentReminders)
		workers.POST("/trigger-rebook-appointments", workerhandler.TriggerRebookAppointments)
	}

	v1 := api.Group("/v1")
	metadata := v1.Group("/metadata")
	{
		metadata.GET("/localization", handler.GetLocalizationData)
		metadata.GET("/localization/major", handler.GetMajorLocalizationData)
		metadata.GET("/currencies", handler.GetCurrencies)
		metadata.GET("/countries", handler.GetCountries)
		metadata.GET("/languages", handler.GetLanguages)
		metadata.GET("/timezones", handler.GetTimezones)
	}

	auth := v1.Group("/auth")
	{
		auth.POST("/register", handler.Register)
		auth.POST("/login", handler.Login)
		auth.POST("/refresh", handler.Refresh)
		auth.POST("/validate", middleware.JWTAuth(), handler.Validate)
		auth.POST("/logout", func(ctx *gin.Context) { /*todo */ })
		auth.POST("/forgot-password", func(ctx *gin.Context) { /*todo */ })
	}

	v1.Use(middleware.JWTAuth())
	tenant := v1.Group("/tenants")
	{
		tenant.GET("", handler.QueryTenant)
		tenant.PUT("", handler.UpdateTenant)
	}

	location := v1.Group("/locations")
	{
		location.GET("", handler.QueryLocation)
		location.PUT("", handler.UpdateLocation)
		// location.POST("/bind-phone", handler.BindPhoneToLocation)
		location.GET("/bind-phones", handler.GetBingPhoneNumber)
	}

	dashboard := v1.Group("/dashboards")
	{
		dashboard.GET("/appointments", dashboardhandler.GetAppointmentsDashboard)
	}

	account := v1.Group("/accounts")
	{
		account.GET("/detail", handler.QueryAccount)
		account.PUT("", handler.UpdateAccount)
	}

	client := v1.Group("/clients")
	{
		client.GET("", clienthandler.QueryAllClients)
		client.GET("/:client_id", clienthandler.QueryClient)
		client.GET("/:client_id/appointments", appointmenthandler.QueryClientAppointments)
		client.POST("", clienthandler.CreateClient)
		client.POST("/walk-in", clienthandler.CreateWalkInClients)
		client.PUT("/:client_id", clienthandler.UpdateClient)
		client.DELETE("/:client_id", clienthandler.DeleteClient)
		// tag
		client.GET("/:client_id/tags", taghandler.QueryClientTags)
		// client.POST("/:client_id/tags", taghandler.CreateClientTag)
		client.PUT("/:client_id/tags", taghandler.UpdateClientTags)
	}

	staff := v1.Group("/staffs")
	{
		staff.POST("", handler.InviteStaff)
		staff.GET("", handler.QueryStaffs)
		staff.PUT("/:account_id", handler.UpdateStaff)
		staff.GET("/:account_id", handler.QueryStaff)
		staff.DELETE("/:account_id", handler.RemoveStaff)
		staff.PUT("/sort", handler.SortStaffs)
		staff.PUT("/roles", handler.BindStaffRole)
	}

	tax := v1.Group("/taxes")
	{
		tax.GET("/:tax_id", handler.QueryTaxById)
		tax.GET("", handler.QueryTaxList)
		tax.POST("", handler.CreateTax)
		tax.PUT("/:tax_id", handler.UpdateTax)
		tax.DELETE("/:tax_id", handler.DeleteTax)
	}

	serviceCategories := v1.Group("/service-categories")
	{
		serviceCategories.GET("/:category_id", handler.QueryServiceCategory)
		serviceCategories.GET("", handler.QueryServiceCategoryList)
		serviceCategories.POST("", handler.CreateServiceCategory)
		serviceCategories.PUT("/:category_id", handler.UpdateServiceCategory)
		serviceCategories.DELETE("/:category_id", handler.DeleteServiceCategory)
	}

	service := v1.Group("/services")
	{
		service.GET("/:service_id", handler.QueryService)
		service.GET("", handler.QueryServiceList)
		service.POST("", handler.CreateService)
		service.PUT("/:service_id", handler.UpdateService)
		service.DELETE("/:service_id", handler.DeleteService)
		service.PUT("/:service_id/staffs", handler.UpdateServiceStaffs)
	}

	appointment := v1.Group("/appointments")
	{
		appointment.GET("/:appointment_id", appointmenthandler.QueryAppointment)
		appointment.GET("/:appointment_id/bills", appointmenthandler.CreateAppointmentBills)
		appointment.GET("/:appointment_id/aa-bills", appointmenthandler.CreateAppointmentAABills)
		appointment.POST("/:appointment_id/bills", appointmenthandler.CreateAppointmentBills)
		appointment.POST("/:appointment_id/aa-bills", appointmenthandler.CreateAppointmentAABills)
		appointment.GET("", appointmenthandler.QueryAppointmentList)
		appointment.GET("/calenders", appointmenthandler.QueryAppointmentCalender)
		appointment.POST("", appointmenthandler.CreateAppointment)
		appointment.PUT("/:appointment_id", appointmenthandler.UpdateAppointment)
		appointment.PUT("/:appointment_id/status", appointmenthandler.UpdateAppointmentStatus)
		appointment.PUT("/:appointment_id/drag", appointmenthandler.DragAppointment)
		appointment.PUT("/:appointment_id/time", appointmenthandler.UpdateAppointmentTime)
		appointment.PUT("/:appointment_id/tips", appointmenthandler.UpdateAppointmentTips)
		appointment.POST("/:appointment_id/tips", appointmenthandler.AddAppointmentTips)
		appointment.DELETE("/:appointment_id/tips/:appt_tip_id", appointmenthandler.DeleteAppointmentTip)
		appointment.PUT("/:appointment_id/services/:appt_service_id", appointmenthandler.UpdateAppointmentService)
		appointment.PUT("/:appointment_id/services", appointmenthandler.BatchUpdateAppointmentService)
		appointment.PUT("/:appointment_id/services/assignment", appointmenthandler.AssignmentAppointmentService)
		appointment.PUT("/:appointment_id/notes", appointmenthandler.UpdateAppointmentNote)
		// appointment.PUT("/:appointment_id/payments", handler.UpdateAppointmentPayment)
		appointment.DELETE("/:appointment_id", appointmenthandler.DeleteAppointment)
	}

	scheduleShifts := v1.Group("/schedule-shifts")
	{
		scheduleShifts.GET("/available", handler.QueryAvailableScheduleShifts)
		scheduleShifts.GET("staff", handler.QueryStaffScheduleShifts)
	}

	message := v1.Group("/connections")
	{
		message.POST("/phone-numbers", handler.PurchaseNumber)
		message.DELETE("/phone-numbers", handler.ReleaseNumber)
		message.GET("/phone-numbers", handler.SearchAvailabeNumbers)
		message.POST("/messages", handler.SendMessage)

		message.GET("/message-group", handler.QueryMessagesGroupedByClient)
		message.GET("/messages/:client_id", handler.QueryClientMessages)
	}

	messageTemplates := v1.Group("/message-templates")
	{
		messageTemplates.GET("/navigations", messagehandler.GetMessageTemplateNavigation)
		// messageTemplates.GET("", messagehandler.ListMessageTemplates)
		messageTemplates.GET("/:template_type", messagehandler.GetMessageTemplate)
		messageTemplates.PUT("/:template_type", messagehandler.UpdateMessageTemplate)
	}

	payment := v1.Group("/payments")
	{
		payment.POST("", paymenthandler.Payments)
		payment.POST("/tips", paymenthandler.Payments)
		payment.GET("", paymenthandler.QueryPayments)
		payment.POST("/refunds", paymenthandler.Refund)
		payment.POST("/appointment-refunds", paymenthandler.Refund)
	}

	paymentMethods := v1.Group("/payment-methods")
	{
		paymentMethods.GET("/:method_id", handler.QueryPaymentMethod)
		paymentMethods.GET("", handler.QueryPaymentMethodsList)
		paymentMethods.POST("", handler.CreatePaymentMethod)
		paymentMethods.PUT("/:method_id", handler.UpdatePaymentMethod)
		paymentMethods.DELETE("/:method_id", handler.DeletePaymentMethod)
	}

	holiday := v1.Group("/holidays")
	{
		holiday.GET("", handler.QueryHolidaysList)
		holiday.POST("", handler.BatchCreateHoliday)
		holiday.PUT("/:holiday_id", handler.UpdateHoliday)
		holiday.DELETE("/:holiday_id", handler.DeleteHoliday)
	}

	workingSchedules := v1.Group("/working-schedules")
	{
		workingSchedules.GET("", handler.StaffSchedulesHours)
		workingSchedules.PUT("", handler.BatchUpdateStaffSchedule)
		workingSchedules.PUT("/reset", handler.ResetStaffSchedule)

		workingSchedules.GET("/dates", handler.QueryStaffSchedulesDate)
		workingSchedules.PUT("/dates", handler.BatchUpdateStaffScheduleDate)

		workingSchedules.GET("/settings", handler.QueryStaffSchedulesSettings)
		workingSchedules.PUT("/settings", handler.UpdateStaffSchedulesSettings)
	}

	permissions := v1.Group("/permissions")
	{
		permissions.GET("", handler.QueryPermissions)
		permissions.GET("/:permission_id", handler.QueryPermission)
		permissions.POST("", handler.CreatePermission)
		permissions.PUT("/:permission_id", handler.UpdatePermission)
		permissions.DELETE("/:permission_id", handler.DeletePermission)
	}

	roles := v1.Group("/roles")
	{
		roles.GET("", handler.QueryRoles)
		roles.GET("/:role_id", handler.QueryRole)
		roles.POST("", handler.CreateRole)
		roles.PUT("/:role_id", handler.UpdateRole)
		roles.DELETE("/:role_id", handler.DeleteRole)
	}

	tags := v1.Group("/tags")
	{
		tags.GET("", taghandler.QueryTags)
		tags.GET("/:tag_id", taghandler.QueryTag)
		tags.POST("", taghandler.CreateTag)
		tags.PUT("/:tag_id", taghandler.UpdateTag)
		tags.DELETE("/:tag_id", taghandler.DeleteTag)
	}

	report := v1.Group("/reports")
	{
		report.GET("/navigations", reporthandler.QueryReportNavigation)
		report.GET("/dashboards", reporthandler.GetDashboardReport)
		report.GET("/:report_key", reporthandler.QueryReport)
		report.GET("/:report_key/export", reporthandler.ExportReport)
	}

	twilioCallback := api.Group("/twilio-callback")
	{
		twilioCallback.POST("/messageing/tenant/:tenant_id/location/:location_id/status", handler.TwilioSendSmsStatusCallBack)
		twilioCallback.POST("/messageing/tenant/:tenant_id/location/:location_id/inbound", handler.TwilioSmsInboundWebhook)
		twilioCallback.POST("/sms/tenant/:tenant_id/location/:location_id/status", handler.TwilioSendSmsStatusCallBack)

		// twilioCallback.POST("/voice", handler.TwilioVoiceCallback)
	}

	stripeGroup := api.Group("/stripe")
	{
		stripeGroup.POST("/webhook", stripe.Webhook)
	}

	stripeSettingGroup := v1.Group("/stripe-setting")
	{
		stripeSettingGroup.GET("", stripe.QuerySettings)
		stripeSettingGroup.PUT("", stripe.UpdateSettings)
		stripeSettingGroup.GET("/connect_link", stripe.GetConnectLink)

		stripeSettingGroup.GET("/locations", stripe.QueryLocations)
		stripeSettingGroup.POST("/locations", stripe.CreateLocation)
	}

	stripeTerminalGroup := v1.Group("/stripe-terminal")
	{
		stripeTerminalGroup.POST("/register_readers", stripe.RegisterStripeReader)
		stripeTerminalGroup.GET("/readers", stripe.QueryStripeReaders)
		stripeTerminalGroup.POST("/intent", stripe.StripeReaderIntent)
		stripeTerminalGroup.POST("/cancel", stripe.CancelStripeReaderAction)
	}

	stripeBalanceGroup := v1.Group("/stripe-balance")
	{
		stripeBalanceGroup.GET("", stripe.GetStripeBalance)
		stripeBalanceGroup.GET("/payout-records", stripe.GetStripeBalancePayoutLogs)
		stripeBalanceGroup.GET("/payment-records", stripe.GetStripeBalancePaymentLogs)
		stripeBalanceGroup.POST("/payouts", stripe.StripeBalancePayout)
		stripeBalanceGroup.PUT("/payout-setting", stripe.UpdateStripePayoutSetting)
		stripeBalanceGroup.GET("/payout-setting", stripe.QueryStripePayoutSetting)
	}

	return r
}
