package debug

import (
	"strconv"
	"time"

	commontypes "pebble/internal/webapp/types/common"
	"pebble/internal/webapp/websocket"
	"pebble/pkg/auth"
	"pebble/pkg/uerror"
	"pebble/pkg/ugin/writer"
	"pebble/pkg/ulog"

	"github.com/gin-gonic/gin"
)

// WebSocketDebugHandler WebSocket调试处理器
type WebSocketDebugHandler struct{}

// NewWebSocketDebugHandler 创建WebSocket调试处理器
func NewWebSocketDebugHandler() *WebSocketDebugHandler {
	return &WebSocketDebugHandler{}
}

// SendStripeTerminalSuccess 发送Stripe Terminal成功消息
func (h *WebSocketDebugHandler) SendStripeTerminalSuccess(c *gin.Context) {
	ctx := c.Request.Context()

	actx := auth.AuthConText(ctx)
	// 获取参数
	tenantId := actx.TenantId
	locationId := actx.LocationId
	accountId := actx.AccountId
	clientId := c.DefaultQuery("client_id", "client_001")
	orderId := c.DefaultQuery("order_id", "order_"+strconv.FormatInt(time.Now().Unix(), 10))

	// 解析金额参数
	amount, _ := strconv.ParseInt(c.DefaultQuery("amount", "5000"), 10, 64) // 默认$50.00
	tip, _ := strconv.ParseInt(c.DefaultQuery("tip", "500"), 10, 64)        // 默认$5.00
	fee, _ := strconv.ParseInt(c.DefaultQuery("fee", "150"), 10, 64)        // 默认$1.50
	tax, _ := strconv.ParseInt(c.DefaultQuery("tax", "400"), 10, 64)        // 默认$4.00

	// 构造伪造的Stripe Terminal成功消息
	msg := struct {
		TenantId      string                          `json:"tenant_id"`
		LocationId    string                          `json:"location_id"`
		ClientId      string                          `json:"client_id"`
		TargetId      string                          `json:"target_id"`
		OrderId       string                          `json:"order_id"`
		OrderType     commontypes.OrderType           `json:"order_type"`
		Amount        int64                           `json:"amount"`
		Tip           int64                           `json:"tip"`
		Fee           int64                           `json:"fee"`
		Tax           int64                           `json:"tax"`
		FeePayer      commontypes.FeePayer            `json:"fee_payer"`
		PaymentStatus commontypes.PaymentRecordStatus `json:"payment_status"`
		Timestamp     int64                           `json:"timestamp"`
		Debug         bool                            `json:"debug"`
	}{
		TenantId:      tenantId,
		LocationId:    locationId,
		ClientId:      clientId,
		TargetId:      "target_" + clientId,
		OrderId:       orderId,
		OrderType:     commontypes.OrderTypeAppointment, // 假设是预约订单
		Amount:        amount,
		Tip:           tip,
		Fee:           fee,
		Tax:           tax,
		FeePayer:      commontypes.FeePayerCustomer, // 假设客户支付手续费
		PaymentStatus: commontypes.PaymentStatusPaid,
		Timestamp:     time.Now().UnixMilli(),
		Debug:         true, // 标记为调试消息
	}

	// 发送WebSocket消息
	err := websocket.GetWebSocketPusher().PushStriepTerminalSucceeded(actx.TenantId, actx.LocationId, actx.AccountId, msg)
	if err != nil {
		ulog.Errorln(ctx, "send stripe terminal success websocket message error", err)
		writer.ResponseErr(c, uerror.ErrCommon, map[string]interface{}{
			"error": "Failed to send WebSocket message: " + err.Error(),
		})
		return
	}

	ulog.Infof(ctx, "debug stripe terminal success message sent: tenant=%s, location=%s, account=%s, order=%s",
		tenantId, locationId, accountId, orderId)

	writer.ResponseOk(c, writer.Obj(map[string]interface{}{
		"message": "Stripe Terminal success message sent successfully",
		"data":    msg,
	}))
}

// SendStripeTerminalFailed 发送Stripe Terminal失败消息
func (h *WebSocketDebugHandler) SendStripeTerminalFailed(c *gin.Context) {
	ctx := c.Request.Context()

	actx := auth.AuthConText(ctx)

	// 获取参数
	tenantId := actx.TenantId
	locationId := actx.LocationId
	accountId := actx.AccountId
	clientId := c.DefaultQuery("client_id", "client_001")
	orderId := c.DefaultQuery("order_id", "order_"+strconv.FormatInt(time.Now().Unix(), 10))
	errorCode := c.DefaultQuery("error_code", "card_declined")
	errorMessage := c.DefaultQuery("error_message", "Your card was declined")

	// 解析金额参数
	amount, _ := strconv.ParseInt(c.DefaultQuery("amount", "5000"), 10, 64)

	// 构造伪造的Stripe Terminal失败消息
	msg := struct {
		TenantId      string                          `json:"tenant_id"`
		LocationId    string                          `json:"location_id"`
		ClientId      string                          `json:"client_id"`
		TargetId      string                          `json:"target_id"`
		OrderId       string                          `json:"order_id"`
		OrderType     commontypes.OrderType           `json:"order_type"`
		Amount        int64                           `json:"amount"`
		PaymentStatus commontypes.PaymentRecordStatus `json:"payment_status"`
		ErrorCode     string                          `json:"error_code"`
		ErrorMessage  string                          `json:"error_message"`
		Timestamp     int64                           `json:"timestamp"`
		Debug         bool                            `json:"debug"`
	}{
		TenantId:      tenantId,
		LocationId:    locationId,
		ClientId:      clientId,
		TargetId:      "target_" + clientId,
		OrderId:       orderId,
		OrderType:     commontypes.OrderTypeAppointment,
		Amount:        amount,
		PaymentStatus: commontypes.PaymentStatusFailed,
		ErrorCode:     errorCode,
		ErrorMessage:  errorMessage,
		Timestamp:     time.Now().UnixMilli(),
		Debug:         true,
	}

	// 发送WebSocket消息
	err := websocket.GetWebSocketPusher().PushStriepTerminalFailed(actx.TenantId, actx.LocationId, actx.AccountId, msg)
	if err != nil {
		ulog.Errorln(ctx, "send stripe terminal failed websocket message error", err)
		writer.ResponseErr(c, uerror.ErrCommon, map[string]interface{}{
			"error": "Failed to send WebSocket message: " + err.Error(),
		})
		return
	}

	ulog.Infof(ctx, "debug stripe terminal failed message sent: tenant=%s, location=%s, account=%s, order=%s",
		tenantId, locationId, accountId, orderId)

	writer.ResponseOk(c, writer.Obj(map[string]interface{}{
		"message": "Stripe Terminal failed message sent successfully",
		"data":    msg,
	}))
}

// SendCustomWebSocketMessage 发送自定义WebSocket消息
func (h *WebSocketDebugHandler) SendCustomWebSocketMessage(c *gin.Context) {
	ctx := c.Request.Context()

	actx := auth.AuthConText(ctx)
	// 获取参数
	tenantId := actx.TenantId
	locationId := actx.LocationId
	accountId := actx.AccountId
	msgType := c.DefaultQuery("type", "notify")
	event := c.DefaultQuery("event", "debug.test")
	message := c.DefaultQuery("message", "This is a debug message")

	// 构造自定义消息
	data := map[string]interface{}{
		"message":   message,
		"timestamp": time.Now().UnixMilli(),
		"debug":     true,
		"source":    "debug_api",
	}

	// 获取推送器
	pusher := websocket.GetWebSocketPusher()

	var err error
	// 根据参数决定推送范围
	if accountId != "" {
		err = pusher.PushToAccount(tenantId, locationId, accountId, msgType, event, data)
	} else if locationId != "" {
		err = pusher.PushToLocation(tenantId, locationId, msgType, event, data)
	} else {
		err = pusher.PushToTenant(tenantId, msgType, event, data)
	}

	if err != nil {
		ulog.Errorln(ctx, "send custom websocket message error", err)
		writer.ResponseErr(c, uerror.ErrCommon, map[string]interface{}{
			"error": "Failed to send WebSocket message: " + err.Error(),
		})
		return
	}

	ulog.Infof(ctx, "debug custom message sent: tenant=%s, location=%s, account=%s, type=%s, event=%s",
		tenantId, locationId, accountId, msgType, event)

	writer.ResponseOk(c, writer.Obj(map[string]interface{}{
		"message": "Custom WebSocket message sent successfully",
		"data":    data,
		"target": map[string]string{
			"tenant_id":   tenantId,
			"location_id": locationId,
			"account_id":  accountId,
		},
	}))
}

// GetWebSocketStats 获取WebSocket统计信息
func (h *WebSocketDebugHandler) GetWebSocketStats(c *gin.Context) {
	service := websocket.GetWebSocketService()
	if service == nil {
		writer.ResponseErr(c, uerror.ErrCommon, map[string]interface{}{
			"error": "WebSocket service not initialized",
		})
		return
	}

	stats := service.GetStats()

	writer.ResponseOk(c, writer.Obj(map[string]interface{}{
		"stats":     stats,
		"timestamp": time.Now().UnixMilli(),
	}))
}
