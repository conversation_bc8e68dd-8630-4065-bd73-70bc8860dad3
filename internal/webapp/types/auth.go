package types

type LoginReq struct {
	Email    string `json:"email" binding:"required,email" example:"<EMAIL>"`
	Password string `json:"password" binding:"required" example:"password123"`
}

func (l LoginReq) Hide() LoginReq {
	l.Password = "****"
	return l
}

type LoginRespItem struct {
	LocationEntity
	IsOwner      bool   `json:"is_owner" example:"true"`
	AccessToken  string `json:"access_token" example:"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."`
	RefreshToken string `json:"refresh_token" example:"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."`
	Expires      int64  `json:"expires" example:"3600"`
	ExpireTime   int64  `json:"expire_time" example:"**********"`
}

type LoginResp struct {
	Account         AccountDefaultInfo `json:"account"`
	DefaultToken    LoginRespItem      `json:"default_token"`
	BelongLocations []LoginRespItem    `json:"belong_locations"`
}

type AccountDefaultInfo struct {
	AccountId   string `json:"account_id" gorm:"column:account_id" example:"acc_123456"`
	FirstName   string `json:"first_name" gorm:"column:first_name" example:"John"`
	LastName    string `json:"last_name" gorm:"column:last_name" example:"Doe"`
	Email       string `json:"email" gorm:"column:email" example:"<EMAIL>"`
	PhoneNumber string `json:"phone_number" gorm:"column:phone_number" example:"+**********"`
}

type RefreshReq struct {
	RefreshToken string `json:"refresh_token" binding:"required" example:"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."`
}

type RefreshResp struct {
	TenantId     string `json:"tenant_id" example:"tenant_123456"`
	LocationId   string `json:"location_id" example:"loc_123456"`
	AccessToken  string `json:"access_token" example:"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."`
	RefreshToken string `json:"refresh_token" example:"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."`
	Expires      int64  `json:"expires" example:"3600"`
	ExpireTime   int64  `json:"expire_time" example:"**********"`
}

type RegisterReq struct {
	LocationName string `json:"location_name" binding:"required" example:"Downtown Salon"`
	FirstName    string `json:"first_name" example:"John"`
	LastName     string `json:"last_name" example:"Doe"`
	PhoneNumber  string `json:"phone_number" binding:"omitempty,phone_number" example:"+15551234567"`
	Email        string `json:"email" binding:"required,email" example:"<EMAIL>"`
	Source       string `json:"source" example:"web"`
	Password     string `json:"password" binding:"required,min=6" example:"password123"`
	Timezone     string `json:"timezone" binding:"required,timezone" example:"America/New_York"`
	Country      string `json:"country" binding:"required,country" example:"US"`
	Currency     string `json:"currency" binding:"required,currency" example:"USD"`
}

func (r RegisterReq) Hide() RegisterReq {
	r.Password = "****"
	return r
}

type RegisterResp struct {
	TenantId     string `json:"tenant_id" example:"tenant_123456"`
	AccountId    string `json:"account_id" example:"acc_123456"`
	LocationId   string `json:"location_id" example:"loc_123456"`
	Email        string `json:"email" example:"<EMAIL>"`
	LocationName string `json:"location_name" example:"Downtown Salon"`
	FirstName    string `json:"first_name" example:"John"`
	LastName     string `json:"last_name" example:"Doe"`
	Timezone     string `json:"timezone" example:"America/New_York"`
	Country      string `json:"country" binding:"required" example:"US"`
	Currency     string `json:"currency" binding:"required" example:"USD"`
}
