package reminder

import "pebble/internal/webapp/types/message"

// PreviewRequest 定义预览请求参数
type PreviewRequest struct {
	TemplateType  message.TemplateType `json:"template_type" binding:"required"`
	AppointmentId string               `json:"appointment_id,omitempty"`
	ClientId      string               `json:"client_id,omitempty"`
}

// PreviewMessage 定义预览响应数据
type PreviewMessage struct {
	Content       string               `json:"content"`                  // 渲染后的消息内容
	Variables     map[string]string    `json:"variables"`                // 模板变量
	TemplateType  message.TemplateType `json:"template_type"`            // 模板类型
	AppointmentId string               `json:"appointment_id,omitempty"` // 预约ID（appointment reminder时有值）
	ClientPhone   string               `json:"client_phone"`             // 客户手机号
	ClientEmail   string               `json:"client_email"`             // 客户邮箱
}

// SendRequest 定义发送请求参数
type SendRequest struct {
	TemplateType  message.TemplateType `json:"template_type" binding:"required"`
	Content       string               `json:"content" binding:"required"` // 要发送的消息内容
	Phone         string               `json:"phone" binding:"required"`   // 目标手机号
	AppointmentId string               `json:"appointment_id,omitempty"`   // appointment reminder时需要
	ClientId      string               `json:"client_id,omitempty"`        // rebook reminder时需要
}

// SendResult 定义发送响应数据
type SendResult struct {
	// Success   bool   `json:"success"`
	MessageId string `json:"message_id,omitempty"`
	// Error     string `json:"error,omitempty"`
}
