package reminder

import "pebble/internal/webapp/types/message"

// ReminderTypeItem 定义reminder类型条目，包含类型和多语言名称
type ReminderTypeItem struct {
	Type string `json:"type"`
	Name string `json:"name"`
}

// MultilingualReminderTypesData 存储不同语言的reminder类型数据
type MultilingualReminderTypesData struct {
	Types map[string][]ReminderTypeItem
}

// GetTypesForLanguage 返回指定语言的reminder类型数据
func (m *MultilingualReminderTypesData) GetTypesForLanguage(language string) []ReminderTypeItem {
	if types, exists := m.Types[language]; exists {
		return types
	}
	// 回退到英文如果请求的语言不可用
	return m.Types["en-US"]
}

// GetDefaultReminderTypesData 返回默认的多语言reminder类型数据
func GetDefaultReminderTypesData() *MultilingualReminderTypesData {
	return &MultilingualReminderTypesData{
		Types: map[string][]ReminderTypeItem{
			"en-US": {
				{
					Type: string(message.AppointmentReminder),
					Name: "Appointment Reminder",
				},
				{
					Type: string(message.RebookAppointment),
					Name: "Rebook Appointment",
				},
			},
			"zh-CN": {
				{
					Type: string(message.AppointmentReminder),
					Name: "预约提醒",
				},
				{
					Type: string(message.RebookAppointment),
					Name: "重新预约",
				},
			},
		},
	}
}