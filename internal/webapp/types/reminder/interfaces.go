package reminder

import (
	"context"
	"pebble/internal/webapp/types/message"
)

// ReminderProcessor 定义reminder处理器接口
type ReminderProcessor interface {
	PreviewMessage(ctx context.Context, req PreviewRequest) (*PreviewMessage, error)
	SendMessage(ctx context.Context, req SendRequest) (*SendResult, error)
	GetTemplateType() message.TemplateType
}

// ReminderFactory 定义reminder工厂接口
type ReminderFactory interface {
	CreateProcessor(templateType message.TemplateType) (ReminderProcessor, error)
	GetSupportedTypes() []message.TemplateType
}