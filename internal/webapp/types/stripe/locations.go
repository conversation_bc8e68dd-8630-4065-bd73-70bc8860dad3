package stripe

import commontypes "pebble/internal/webapp/types/common"

type StripeLocations struct {
	StripeLocationsEntity
	commontypes.Model
}

type StripeLocationsEntity struct {
	TenantId         string             `json:"tenant_id" gorm:"column:tenant_id" example:"tenant_2viQlj16EjDj8zLRbgibcidla4i"`
	LocationId       string             `json:"location_id" gorm:"column:location_id" example:"loc_2viQlkAbQuyb04fe9cF04JP2AOj"`
	Country          string             `json:"country" gorm:"column:country" binding:"required" example:"US"`
	State            string             `json:"state" gorm:"column:state" binding:"required" example:"CA"`
	City             string             `json:"city" gorm:"column:city" binding:"required" example:"San Francisco"`
	Address1         string             `json:"address1" gorm:"column:address1" binding:"required" example:"123 Main St"`
	Address2         string             `json:"address2" gorm:"column:address2" binding:"omitempty" example:"Apt 1"`
	Zipcode          string             `json:"zipcode" gorm:"column:zipcode" binding:"omitempty" example:"94105"`
	Lat              float64            `json:"lat" gorm:"column:-" binding:"omitempty,min=-90,max=90" example:"37.7749"`
	Lng              float64            `json:"lng" gorm:"column:-" binding:"omitempty,min=-90,max=90" example:"-122.4194"`
	Name             string             `json:"name" gorm:"column:name" binding:"omitempty" example:"San Francisco"`
	StripeLocationId string             `json:"stripe_location_id" gorm:"column:stripe_location_id" `
	StripeAccountId  string             `json:"stripe_account_id" gorm:"column:stripe_account_id" example:"acc_2viQlj16EjDj8zLRbgibcidla4i"`
	Status           commontypes.Status `json:"status" gorm:"column:status" example:"1"`
}
