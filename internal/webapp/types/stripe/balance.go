package stripe

import "pebble/pkg/paystream"

type GetStripeBalancePayoutLogsReq struct {
	Page     int64 `form:"page" binding:"required" example:"1"`
	PageSize int64 `form:"page_size" binding:"required" example:"10"`
}

type GetStripePayoutLogsRes struct {
	paystream.GetStripeBalancePayoutLogsRes
	CurrencySymbol string `json:"currency_symbol"`
}

type GetStripeBalancePaymentLogsReq struct {
	Page     int64 `form:"page" binding:"required" example:"1"`
	PageSize int64 `form:"page_size" binding:"required" example:"10"`
}

type GetStripeBalancePaymentLogsRes struct {
	paystream.GetStripeBalancePaymentLogsRes
	CurrencySymbol string `json:"currency_symbol"`
}

type StripeBalancePayoutReq struct {
	Amount int64 `json:"amount" binding:"required" example:"100"`
}

type UpdateStripePayoutSettingReq struct {
	Method       int64 `json:"method"`
	MethodParams int64 `json:"method_params"`
}

type QueryStripePayoutSettingRes struct {
	Method       int64 `json:"method"`
	MethodParams int64 `json:"method_params"`
}
