package stripe

import (
	"encoding/json"
	appointmenttypes "pebble/internal/webapp/types/appointment"
	commontypes "pebble/internal/webapp/types/common"
	"pebble/pkg/paystream"

	"gorm.io/datatypes"
	"gorm.io/gorm"
)

type PaymentIntentStatus int8

const (
	PaymentIntentStatusPending   PaymentIntentStatus = 1
	PaymentIntentStatusSucceeded PaymentIntentStatus = 2
	PaymentIntentStatusFailed    PaymentIntentStatus = 3
)

type StripePaymentIntent struct {
	StripePaymentIntentEntity
	commontypes.Model
}

type StripePaymentIntentEntity struct {
	TenantId              string                `json:"tenant_id" gorm:"column:tenant_id" example:"tenant_2viQlj16EjDj8zLRbgibcidla4i"`
	LocationId            string                `json:"location_id" gorm:"column:location_id" example:"loc_2viQlkAbQuyb04fe9cF04JP2AOj"`
	OrderId               string                `json:"order_id" gorm:"column:order_id" example:"appt_1N"`
	OrderType             commontypes.OrderType `json:"order_type" gorm:"column:order_type" example:"1"`
	ClientId              string                `json:"client_id" gorm:"column:client_id" example:"client_2viR3b4cZgmpSORwJ127jHKFZHh"`
	AccountId             string                `json:"account_id" gorm:"column:account_id" example:"acc_2viQlj16EjDj8zLRbgibcidla4i"`
	StripePaymentIntentId string                `json:"stripe_payment_intent_id" gorm:"column:stripe_payment_intent_id" example:"pi_1N"`
	StripeAccountId       string                `json:"stripe_account_id" gorm:"column:stripe_account_id" example:"acc_2viQlj16EjDj8zLRbgibcidla4i"`
	StripeCustomerId      string                `json:"stripe_customer_id" gorm:"column:stripe_customer_id" example:"cus_1N"`
	Amount                int64                 `json:"amount" gorm:"column:amount" example:"1000"`
	AmountTip             int64                 `json:"amount_tip" gorm:"column:amount_tip" example:"100"`
	AmountFee             int64                 `json:"amount_fee" gorm:"column:amount_fee" example:"100"`
	Currency              string                `json:"currency" gorm:"column:currency" example:"usd"`
	Status                PaymentIntentStatus   `json:"status" gorm:"column:status" example:"1"`
	MetadataJson          datatypes.JSON        `json:"-" gorm:"column:metadata"`
	Metadata              Metadata              `json:"metadata" gorm:"-" example:"{\"order_id\":\"**********\",\"order_type\":\"1\"}"`
}

type Metadata struct {
	TargetId        string `json:"target_id,omitempty"`
	PaymentMethodId string `json:"payment_method_id,omitempty"`

	appointmenttypes.BaseBills
	paystream.Fee
	// Amount         int64 `json:"amount"`
	// OriginalAmount int64 `json:"original_amount"`

	// ApplicationFee        int64 `json:"application_fee"`
	// ApplicationFeePercent int64 `json:"application_fee_percent"`

	// PaymentFee        int64 `json:"payment_fee"`
	// PaymentFeePercent int64 `json:"payment_fee_percent"`
	// PaymentBaseFee    int64 `json:"payment_base_fee"`
}

type UpdateStripePaymentIntentReq struct {
	Status    *PaymentIntentStatus `json:"status"`
	Amount    *int64               `json:"amount"`
	AmountTip *int64               `json:"amount_tip"`
	AmountFee *int64               `json:"amount_fee"`
}

func (p *StripePaymentIntent) BeforeCreate(tx *gorm.DB) (err error) {
	raw, err := json.Marshal(p.Metadata)
	if err != nil {
		return err
	}
	p.MetadataJson = datatypes.JSON(raw)
	return nil
}

func (p *StripePaymentIntent) BeforeUpdate(tx *gorm.DB) (err error) {
	raw, err := json.Marshal(p.Metadata)
	if err != nil {
		return err
	}
	p.MetadataJson = datatypes.JSON(raw)
	return nil
}

func (p *StripePaymentIntent) AfterFind(tx *gorm.DB) (err error) {
	if len(p.MetadataJson) > 0 {
		var intentContext Metadata
		if err := json.Unmarshal(p.MetadataJson, &intentContext); err != nil {
			return err
		}
		p.Metadata = intentContext
	}
	return nil
}
