package stripe

import (
	commontypes "pebble/internal/webapp/types/common"
)

type OnlineStatus string

const (
	OnlineStatusOnline  OnlineStatus = "online"
	OnlineStatusOffline OnlineStatus = "offline"
)

type StripeTerminal struct {
	StripeTerminalEntity
	commontypes.Model
}

type StripeTerminalEntity struct {
	TenantId           string `json:"tenant_id" gorm:"column:tenant_id" example:"tenant_2viQlj16EjDj8zLRbgibcidla4i"`
	LocationId         string `json:"location_id" gorm:"column:location_id" example:"loc_2viQlkAbQuyb04fe9cF04JP2AOj"`
	Name               string `json:"name" gorm:"column:name" example:"Terminal 1"`
	StripeLocationId   string `json:"stripe_location_id" gorm:"column:stripe_location_id" example:"loc_2viQlkAbQuyb04fe9cF04JP2AOj"`
	StripeLocationName string `json:"stripe_location_name" gorm:"column:stripe_location_name" example:"Terminal 1"`
	Code               string `json:"code" gorm:"column:code" example:"1234567890"`
	// Status             int64  `json:"status" gorm:"column:status" example:"1"`
	ReaderId     string       `json:"reader_id" gorm:"column:reader_id" example:"rdr_1234567890"`
	DeviceType   string       `json:"device_type" gorm:"column:device_type" example:"chipper2x"`
	SerialNumber string       `json:"serial_number" gorm:"column:serial_number" example:"1234567890"`
	OnlineStatus OnlineStatus `json:"online_status" gorm:"column:-" example:"online"`
}

type RegisterStripeReaderReq struct {
	StripeLocationId string `json:"stripe_location_id" gorm:"column:stripe_location_id" example:"loc_2viQlkAbQuyb04fe9cF04JP2AOj"`
	Name             string `json:"name" gorm:"column:name" example:"Terminal 1"`
	RegistrationCode string `json:"registration_code" gorm:"column:registration_code" example:"1234567890"`
}

type QueryStripeReadersParams struct {
	OnlineStatus OnlineStatus `form:"online_status" binding:"omitempty,oneof=online offline" example:"online"`
}

type StripeReaderIntentReq struct {
	Amount   int64  `json:"amount" binding:"required" example:"100"`
	ReaderID string `json:"reader_id" binding:"required" example:"rdr_1234567890"`
	// AmountFee int64  `json:"amount_fee" binding:"required" example:"10"`
	OrderId   string                `json:"order_id" binding:"required" example:"1234567890"`
	OrderType commontypes.OrderType `json:"order_type" binding:"required" example:"1"`

	TargetId string `json:"target_id,omitempty"`
	Tip      int64  `json:"tip" binding:"omitempty" example:"1000"`
	Tax      int64  `json:"tax" binding:"omitempty" example:"1000"`
	Fee      int64  `json:"fee" binding:"omitempty" example:"1000"`
}

type CancelStripeReaderActionReq struct {
	ReaderID string `json:"reader_id" binding:"required" example:"rdr_1234567890"`
}
