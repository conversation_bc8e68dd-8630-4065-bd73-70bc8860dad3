package stripe

import (
	commontypes "pebble/internal/webapp/types/common"
	"pebble/pkg/util"

	"gorm.io/gorm"
)

type StripeSetting struct {
	StripeSettingEntity
	commontypes.Model
}

type StripeSettingEntity struct {
	TenantId              string               `json:"tenant_id" gorm:"column:tenant_id" example:"tenant_2viQlj16EjDj8zLRbgibcidla4i"`
	LocationId            string               `json:"location_id" gorm:"column:location_id" example:"loc_2viQlkAbQuyb04fe9cF04JP2AOj"`
	StripeAccountId       string               `json:"stripe_account_id" gorm:"column:stripe_account_id" example:"acc_2viQlj16EjDj8zLRbgibcidla4i"`
	TipPercentage         string               `json:"-" gorm:"column:tip_percentage" example:"10"`
	TipPercentageList     []int64              `json:"tip_percentage" gorm:"-" example:"10"`
	PayoutInterval        int64                `json:"payout_interval" gorm:"column:payout_interval;default:0" example:"1"`
	PayoutDeleayDays      int64                `json:"payout_delay_days" gorm:"column:payout_delay_days;default:1" example:"1"`
	PayoutDeleayTimes     int64                `json:"payout_delay_times" gorm:"column:payout_delay_times;default:0" example:"1"`
	OnlineFeePercent      int64                `json:"online_fee_percent" gorm:"column:online_fee_percent;default:340" example:"1"`
	OnlineBaseFee         int64                `json:"online_base_fee" gorm:"column:online_base_fee;default:30" example:"1"`
	CardFeePercent        int64                `json:"card_fee_percent" gorm:"column:card_fee_percent;default:290" example:"1"`
	CardBaseFee           int64                `json:"card_base_fee" gorm:"column:card_base_fee;default:50" example:"1"`
	PayoutFee             int64                `json:"payout_fee" gorm:"column:payout_fee;default:50" example:"1"`
	ApplicationFeePercent int64                `json:"application_fee_percent" gorm:"column:application_fee_percent;default:1000" example:"1"`
	FeePayer              commontypes.FeePayer `json:"fee_payer" gorm:"column:fee_payer;default:1" example:"1"`
}

type UpdateStripeSettingReq struct {
	TipPercentage     *string               `json:"-" gorm:"column:tip_percentage" example:"10"`
	TipPercentageList []int64               `json:"tip_percentage" gorm:"-" example:"10"`
	PayoutInterval    *int64                `json:"payout_interval" gorm:"column:payout_interval" example:"1"`
	PayoutDeleayDays  *int64                `json:"payout_delay_days" gorm:"column:payout_delay_days" example:"1"`
	PayoutDeleayTimes *int64                `json:"payout_delay_times" gorm:"column:payout_delay_times" example:"1"`
	OnlineFeePercent  *int64                `json:"online_fee_percent" gorm:"column:online_fee_percent" example:"1"`
	OnlineBaseFee     *int64                `json:"online_base_fee" gorm:"column:online_base_fee" example:"1"`
	CardFeePercent    *int64                `json:"card_fee_percent" gorm:"column:card_fee_percent" example:"1"`
	CardBaseFee       *int64                `json:"card_base_fee" gorm:"column:card_base_fee" example:"1"`
	FeePayer          *commontypes.FeePayer `json:"fee_payer" gorm:"column:fee_payer" example:"1"`
}

type QueryStripeSettingRes struct {
	StripeSettingEntity
	// Link           string `json:"link" gorm:"-"`
	Connected      bool `json:"connected" gorm:"-"`
	PayoutsEnabled bool `json:"payouts_enabled" gorm:"-"`
	ChargesEnabled bool `json:"charges_enabled" gorm:"-"`
}

type GetConnectLinkRes struct {
	Link string `json:"link" example:"https://connect.stripe.com/oauth/authorize?client_id=ca_1234567890&redirect_uri=https://example.com/stripe/callback&response_type=code&scope=read_write"`
}

func (s *StripeSetting) BeforeCreate(tx *gorm.DB) (err error) {
	if len(s.TipPercentageList) > 0 {
		s.TipPercentage = util.Join(s.TipPercentageList, ",")
	} else {
		s.TipPercentage = "5,10,15"
	}
	return nil
}

func (s *StripeSetting) BeforeUpdate(tx *gorm.DB) (err error) {
	if len(s.TipPercentageList) > 0 {
		s.TipPercentage = util.Join(s.TipPercentageList, ",")
	}
	return nil
}

func (s *StripeSetting) AfterFind(tx *gorm.DB) (err error) {
	s.TipPercentageList = util.NoNilSlice(util.SplitInt64(s.TipPercentage))
	return nil
}
