package message

import (
	commontypes "pebble/internal/webapp/types/common"

	"gorm.io/gorm"
)

// TemplateType defines the type of a message template.
type TemplateType string

const (
	// AppointmentReminder is for appointment reminders.
	AppointmentReminder TemplateType = "appointment_reminder"
	// RebookAppointment is for rebooking reminders.
	RebookAppointment TemplateType = "rebook_appointment"
)

// MessageTemplate is the core model for the message_templates table.
type MessageTemplate struct {
	TenantId           string               `json:"tenant_id" gorm:"column:tenant_id" example:"tenant_123456"`
	LocationId         string               `json:"location_id" gorm:"column:location_id" example:"loc_123456"`
	Name               string               `json:"name" gorm:"column:name" example:"Daily Appointment Reminder"`
	TemplateType       TemplateType         `json:"template_type" gorm:"column:template_type" example:"appointment_reminder"`
	ChannelType        string               `json:"channel_type" gorm:"column:channel_type;default:'sms'" example:"sms"`
	Subject            string               `json:"subject" gorm:"column:subject" example:"Appointment Reminder"`
	Content            string               `json:"content" gorm:"column:content;not null"`
	TriggerConfigHours int                  `json:"trigger_config_hours" gorm:"column:trigger_config_hours" example:"24"`
	Status             commontypes.Status   `json:"status" gorm:"column:status;default:1"`
	SupportedVariables []SupportedVariables `json:"supported_variables" gorm:"-"`
	commontypes.Model
}

type SupportedVariables struct {
	Variable string `json:"variable"`
	Name     string `json:"name"`
	Example  string `json:"example"`
}

func (m *MessageTemplate) AfterFind(tx *gorm.DB) (err error) {
	m.SupportedVariables = GetFormattedSupportedVariables(m.TemplateType)
	return
}

// CreateMessageTemplateReq defines the request body for creating a new message template.
type CreateMessageTemplateReq struct {
	Name               string       `json:"name" binding:"required,max=100"`
	TemplateType       TemplateType `json:"template_type" binding:"required,oneof=appointment_reminder rebook_appointment"`
	ChannelType        string       `json:"channel_type" binding:"required,oneof=sms email"`
	Subject            string       `json:"subject,omitempty"`
	Content            string       `json:"content" binding:"required"`
	TriggerConfigHours int          `json:"trigger_config_hours" binding:"required,min=0"`
}

// UpdateMessageTemplateReq defines the request body for updating an existing message template.
type UpdateMessageTemplateReq struct {
	Name               *string             `json:"name,omitempty" binding:"omitempty,max=100"`
	Subject            *string             `json:"subject,omitempty" binding:"omitempty,max=255"`
	Content            *string             `json:"content,omitempty" binding:"omitempty,max=65535"`
	TriggerConfigHours *int                `json:"trigger_config_hours,omitempty" binding:"omitempty,min=0"`
	Status             *commontypes.Status `json:"status,omitempty" binding:"omitempty,oneof=1 2"`
}
