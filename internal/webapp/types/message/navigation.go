package message

// NavigationCategory represents a category in the message template navigation.
type NavigationCategory struct {
	Category string            `json:"category"`
	Items    []NavigationEvent `json:"items"`
}

// NavigationEvent represents a specific event type within a category.
type NavigationEvent struct {
	Name         string       `json:"name"`
	TemplateType TemplateType `json:"template_type"`
}
