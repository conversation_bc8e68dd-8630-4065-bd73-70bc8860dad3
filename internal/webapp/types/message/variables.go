package message

// SupportedVariablesMap maps template types to their supported template variables.
var SupportedVariablesMap = map[TemplateType][]SupportedVariables{
	AppointmentReminder: {
		{
			Variable: "{{client_full_name}}",
			Name:     "Client Name",
			Example:  "<PERSON>",
		},
		{
			Variable: "{{client_first_name}}",
			Name:     "Client First Name",
			Example:  "<PERSON>",
		},
		{
			Variable: "{{client_last_name}}",
			Name:     "Client Last Name",
			Example:  "<PERSON>e",
		},
		{
			Variable: "{{client_email}}",
			Name:     "Client Email",
			Example:  "<EMAIL>",
		},
		{
			Variable: "{{client_phone}}",
			Name:     "Client Phone Number",
			Example:  "+1234567890",
		},
		{
			Variable: "{{client_address}}",
			Name:     "Client Address",
			Example:  "123 Main St, Anytown, CA 12345",
		},
		{
			Variable: "{{appointment_date}}",
			Name:     "Appointment Date",
			Example:  "2023-04-15",
		},
		{
			Variable: "{{appointment_time}}",
			Name:     "Appointment Time",
			Example:  "10:00 AM",
		},
	},
	RebookAppointment: {
		{
			Variable: "{{client_full_name}}",
			Name:     "Client Name",
			Example:  "John Doe",
		},
		{
			Variable: "{{client_first_name}}",
			Name:     "Client First Name",
			Example:  "John",
		},
		{
			Variable: "{{client_last_name}}",
			Name:     "Client Last Name",
			Example:  "Doe",
		},
		{
			Variable: "{{client_phone_number}}",
			Name:     "Client Phone Number",
			Example:  "+1234567890",
		},
		{
			Variable: "{{client_email}}",
			Name:     "Client Email",
			Example:  "<EMAIL>",
		},
		{
			Variable: "{{location_address}}",
			Name:     "Location Address",
			Example:  "123 Main St, Anytown, CA 12345",
		},
	},
}

// GetFormattedSupportedVariables returns the list of supported variables for a given template type,
// formatted as "{{variable_name}}".
func GetFormattedSupportedVariables(templateType TemplateType) []SupportedVariables {
	vars, ok := SupportedVariablesMap[templateType]
	if !ok {
		return []SupportedVariables{}
	}

	return vars
}
