package message

import commontypes "pebble/internal/webapp/types/common"

// DefaultMessageTemplate defines the default values for a message template.
type DefaultMessageTemplate struct {
	Name               string             `gorm:"column:name;type:varchar(100);not null" json:"name"`
	TemplateType       TemplateType       `gorm:"column:template_type;type:varchar(50);not null;uniqueIndex:idx_tenant_location_template_type" json:"template_type"`
	ChannelType        string             `gorm:"column:channel_type;type:varchar(20);not null;default:'sms'" json:"channel_type"`
	Subject            string             `gorm:"column:subject;type:varchar(255)" json:"subject"`
	Content            string             `gorm:"column:content;type:text;not null" json:"content"`
	TriggerConfigHours int                `gorm:"column:trigger_config_hours;not null" json:"trigger_config_hours"`
	Status             commontypes.Status `gorm:"column:status;type:tinyint;not null;default:1" json:"status"`
}

// DefaultTemplatesMap holds the default data for each event type.
var DefaultTemplatesMap = map[TemplateType]DefaultMessageTemplate{
	AppointmentReminder: {
		Name:               "Appointment Reminder",
		Subject:            "Your Appointment Reminder",
		TemplateType:       AppointmentReminder,
		ChannelType:        "sms",
		Status:             commontypes.StatusInactive,
		Content:            "Hi {{client_full_name}}, this is a reminder for your appointment on {{appointment_date}} at {{appointment_time}}.",
		TriggerConfigHours: 2, // e.g., 2 Hour
	},
	RebookAppointment: {
		Name:               "Rebook Appointment",
		Subject:            "It's Time to Rebook Your Appointment",
		TemplateType:       RebookAppointment,
		ChannelType:        "sms",
		Status:             commontypes.StatusInactive,
		Content:            "Hi {{client_full_name}}, it's been a while! We miss you. It's time to book your next appointment with us.",
		TriggerConfigHours: 30 * 24, // e.g., 30 days after last appointment
	},
}
