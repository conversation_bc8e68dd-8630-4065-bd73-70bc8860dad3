package types

import (
	"encoding/json"
	commontypes "pebble/internal/webapp/types/common"

	"gorm.io/gorm"
)

type StaffScheduleOverride struct {
	StaffScheduleOverrideEntity
	commontypes.Model
}

type StaffScheduleOverrideEntity struct {
	TenantID     string            `json:"tenant_id" gorm:"column:tenant_id" example:"tenant_123456"`
	LocationID   string            `json:"location_id" gorm:"column:location_id" example:"loc_123456"`
	AccountID    string            `json:"account_id" gorm:"column:account_id" example:"acc_123456"`
	Date         string            `json:"date" gorm:"column:date" example:"2023-07-01"`
	Reason       string            `json:"reason" gorm:"column:reason" example:"holiday"`
	WorkingTime  string            `json:"-" gorm:"column:working_time" example:"[]"`
	WorkingTimes []WorkingTimeItem `json:"working_times"  gorm:"-"`
	IsDayOff     bool              `json:"is_day_off" gorm:"column:is_day_off" example:"false"`
}

func (s *StaffScheduleOverride) AfterFind(tx *gorm.DB) (err error) {
	if s.WorkingTime != "" {
		var workingTimes []WorkingTimeItem
		_ = json.Unmarshal([]byte(s.WorkingTime), &workingTimes)
		s.WorkingTimes = workingTimes
	}

	if s.WorkingTimes == nil {
		s.WorkingTimes = []WorkingTimeItem{}
	}

	return
}

func (s *StaffScheduleOverride) BeforeCreate(tx *gorm.DB) (err error) {
	b, _ := json.Marshal(s.WorkingTimes)
	s.WorkingTime = string(b)
	return
}

func (s *StaffScheduleOverride) BeforeUpdate(tx *gorm.DB) (err error) {
	b, _ := json.Marshal(s.WorkingTimes)
	s.WorkingTime = string(b)
	return
}

type QueryStaffSchedulesDateParams struct {
	StartDate *string `form:"start_date" binding:"required,date" example:"2023-07-01"`
	EndDate   *string `form:"end_date" binding:"required,date" example:"2023-07-31"`
	AccountId *string `form:"account_id" binding:"omitempty" example:"acc_123456"`
}

type QueryMultiStaffSchedulesDateParams struct {
	StartDate  *string  `form:"start_date" binding:"required,date" example:"2023-07-01"`
	EndDate    *string  `form:"end_date" binding:"required,date" example:"2023-07-31"`
	AccountIds []string `form:"account_ids,omitempty" binding:"required" example:"[\"acc_123456\"]"`
}

type QueryMultiStaffSchedulesDateResp struct {
	AccountID string `json:"account_id" gorm:"column:account_id" example:"acc_123456"`
	// AccountFirstName string            `json:"account_first_name" gorm:"column:account_first_name" example:"John"`
	// AccountLastName  string            `json:"account_last_name" gorm:"column:account_last_name" example:"Doe"`
	Date         string            `json:"date" example:"2023-07-01"`
	Reason       string            `json:"reason" gorm:"column:reason" example:"holiday"`
	WorkingTimes []WorkingTimeItem `json:"working_times"  gorm:"-"`
	IsDayOff     bool              `json:"is_day_off" gorm:"column:is_day_off" example:"false"`
}

type QueryStaffSchedulesDateResp struct {
	AccountID        string                                      `json:"account_id" gorm:"column:account_id" example:"acc_123456"`
	AccountFirstName string                                      `json:"account_first_name" gorm:"column:account_first_name" example:"John"`
	AccountLastName  string                                      `json:"account_last_name" gorm:"column:account_last_name" example:"Doe"`
	WorkingDates     map[string]QueryMultiStaffSchedulesDateResp `json:"working_dates"`
}

type CreateStaffScheduleOverrideReq struct {
	AccountID    string            `json:"account_id" gorm:"column:account_id" example:"acc_123456"`
	Date         string            `json:"date" gorm:"column:date" example:"2023-07-01"`
	Reason       string            `json:"reason" gorm:"column:reason" example:"holiday"`
	WorkingTimes []WorkingTimeItem `json:"working_times"  gorm:"-"`
	IsDayOff     bool              `json:"is_day_off" gorm:"column:is_day_off" example:"false"`
}

type BatchUpdateStaffScheduleOverrideReq struct {
	AccountID    []string          `json:"account_id" binding:"required" example:"[\"acc_123456\"]"`
	Dates        []string          `json:"dates" binding:"required" example:"[\"2023-07-01\",\"2023-07-02\"]"`
	WorkingTimes []WorkingTimeItem `json:"working_times" binding:"required"`
	IsDayOff     *bool             `json:"is_day_off" binding:"required" example:"false"`
	Reason       *string           `json:"reason" binding:"required" example:"holiday"`
}

type UpdateStaffScheduleOverrideItem struct {
	WorkingTime *string `json:"-" gorm:"column:working_time"`
	IsDayOff    *bool   `json:"is_day_off" binding:"required" example:"false"`
}
