package types

import (
	"encoding/json"
	commontypes "pebble/internal/webapp/types/common"

	"gorm.io/datatypes"
	"gorm.io/gorm"
)

// SMSMessageType represents the type of SMS message
// @enum.1=Verification message, used for sending verification codes
// @enum.2=Notification message, used for sending notifications
// @enum.3=Marketing message, used for sending marketing content
type SMSMessageType int

const (
	SMSMessageTypeVerification SMSMessageType = 1 // Verification message, used for sending verification codes
	SMSMessageTypeNotification SMSMessageType = 2 // Notification message, used for sending notifications
	SMSMessageTypeMarketing    SMSMessageType = 3 // Marketing message, used for sending marketing content
)

// SMSMessageStatus represents the status of SMS message
// @enum.1=Pending status, message is waiting to be sent
// @enum.2=Sending status, message is in the process of being sent
// @enum.3=Delivered status, message was successfully delivered
// @enum.4=Failed status, message failed to send
// @enum.5=Rejected status, message was rejected by the provider
type SMSMessageStatus int

const (
	SMSMessageStatusPending   SMSMessageStatus = 1 // Pending status, message is waiting to be sent
	SMSMessageStatusSending   SMSMessageStatus = 2 // Sending status, message is in the process of being sent
	SMSMessageStatusDelivered SMSMessageStatus = 3 // Delivered status, message was successfully delivered
	SMSMessageStatusFailed    SMSMessageStatus = 4 // Failed status, message failed to send
	SMSMessageStatusRejected  SMSMessageStatus = 5 // Rejected status, message was rejected by the provider
)

// SMSMessage represents an SMS message record
type SMSMessage struct {
	SMSMessageEntity
	commontypes.Model
}

// SMSMessageEntity represents the data needed to create a new SMS message
type SMSMessageEntity struct {
	TenantID           string            `json:"tenant_id" gorm:"column:tenant_id" example:"tenant_123456"`
	LocationID         string            `json:"location_id" gorm:"column:location_id" example:"loc_123456"`
	ClientID           string            `json:"client_id" gorm:"column:client_id" example:"client_123456"`
	MessageID          string            `json:"message_id" gorm:"column:message_id" example:"msg_123456"`
	PhoneNumber        string            `json:"phone_number" gorm:"column:phone_number" example:"+15557654321"`
	ToNumber           string            `json:"to_number" gorm:"column:to_number" example:"+15551234567"`
	Provider           string            `json:"provider" gorm:"column:provider" example:"twilio"`
	MsgType            SMSMessageType    `json:"msg_type" gorm:"column:msg_type" example:"2"`
	Content            string            `json:"content" gorm:"column:content" example:"Your appointment is confirmed for tomorrow at 2pm."`
	TemplateID         string            `json:"template_id" gorm:"column:template_id" example:"tmpl_123456"`
	TemplateParamsJson datatypes.JSON    `json:"-" gorm:"column:template_params;type:json" example:"{\"name\":\"John\",\"time\":\"2pm\"}"`
	TemplateParams     map[string]string `json:"template_params" gorm:"-" example:"{\"name\":\"John\",\"time\":\"2pm\"}"`
	Status             SMSMessageStatus  `json:"status" gorm:"column:status;default:1" example:"3"`
	ErrorCode          string            `json:"error_code" gorm:"column:error_code" example:"30001"`
	ErrorMsg           string            `json:"error_msg" gorm:"column:error_msg" example:"Message delivery failed"`
	ProviderMsgID      string            `json:"provider_msg_id" gorm:"column:provider_msg_id" example:"SM123456789abcdef"`
}

func (m *SMSMessageEntity) BeforeCreate(tx *gorm.DB) error {
	return m.syncTemplateParamsToJSON()
}

func (m *SMSMessageEntity) BeforeUpdate(tx *gorm.DB) error {
	return m.syncTemplateParamsToJSON()
}

func (m *SMSMessageEntity) AfterFind(tx *gorm.DB) error {
	if len(m.TemplateParamsJson) > 0 {
		var params map[string]string
		if err := json.Unmarshal(m.TemplateParamsJson, &params); err != nil {
			return err
		}
		m.TemplateParams = params
	}
	return nil
}

func (m *SMSMessageEntity) syncTemplateParamsToJSON() error {
	if len(m.TemplateParams) > 0 {
		b, err := json.Marshal(m.TemplateParams)
		if err != nil {
			return err
		}
		m.TemplateParamsJson = datatypes.JSON(b)
	}
	return nil
}

// SendSMSReq represents the request to send an SMS message
type SendMessageReq struct {
	ClientId string `json:"client_id" binding:"required" example:"client_123456"`
	To       string `json:"to" binding:"required" example:"+15551234567"`
	Content  string `json:"content" binding:"required" example:"Your appointment is confirmed for tomorrow at 2pm."`
	// MsgType  SMSMessageType `json:"msg_type" binding:"required"`
}

// SendSMSResp represents the response of sending an SMS message
type SendMessageResp struct {
	MessageID     string           `json:"message_id" example:"msg_123456"`
	ProviderMsgID string           `json:"provider_msg_id" example:"SM123456789abcdef"`
	Status        SMSMessageStatus `json:"status" example:"2"`
}

// UpdateSMSStatusReq represents the request to update SMS message status
type UpdateSMSStatusReq struct {
	PhoneNumber *string           `json:"phone_number" binding:"omitempty,phone_number" example:"+15551234567"`
	Status      *SMSMessageStatus `json:"status" binding:"omitempty,oneof=1 2 3 4 5" example:"3"`
	ErrorCode   *string           `json:"error_code" example:"30001"`
	ErrorMsg    *string           `json:"error_msg" example:"Message delivery failed"`
}
