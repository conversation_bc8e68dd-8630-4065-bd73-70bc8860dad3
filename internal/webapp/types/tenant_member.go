package types

import commontypes "pebble/internal/webapp/types/common"

type TenantMemberEntity struct {
	AccountId   string             `json:"account_id" gorm:"column:account_id" example:"acc_123456"`
	TenantId    string             `json:"tenant_id" gorm:"column:tenant_id" example:"tenant_123456"`
	LocationId  string             `json:"location_id" gorm:"column:location_id" example:"loc_123456"`
	FirstName   string             `json:"first_name" gorm:"column:first_name" example:"John"`
	LastName    string             `json:"last_name" gorm:"column:last_name" example:"Doe"`
	Email       string             `json:"email" gorm:"column:email" example:"<EMAIL>"`
	PhoneNumber string             `json:"phone_number" gorm:"column:phone_number" example:"+**********"`
	Color       string             `json:"color" gorm:"column:color;default:#000000" example:"#000000"`
	IsOwner     bool               `json:"is_owner" gorm:"column:is_owner" example:"true"`
	Status      commontypes.Status `json:"status" gorm:"column:status;default:1" example:"1"`
	SortIndex   uint64             `json:"sort_index" gorm:"column:sort_index;default:0" example:"1"`
}

type TenantMember struct {
	TenantMemberEntity
	commontypes.Model
}

type InviteTenantMemberReq struct {
	Email       string `json:"email" binding:"required,email" example:"<EMAIL>"`
	FirstName   string `json:"first_name" binding:"required" example:"John"`
	LastName    string `json:"last_name" binding:"required" example:"Doe"`
	PhoneNumber string `json:"phone_number" binding:"omitempty,phone_number" example:"+***********"`
	Password    string `json:"password" binding:"omitempty,min=6" example:"password123"` // todo
	Color       string `json:"color" binding:"omitempty" example:"#000000"`
}

type QueryTenantMemberParams struct {
	// Page     int    `form:"page" binding:"required,min=1" example:"1"`
	// PageSize int    `form:"page_size" binding:"required,min=1,max=100" example:"10"`
	// Keyword  string `form:"keyword" example:"john"`
}

type RemoveTenantMemberReq struct {
	AccountId string `uri:"account_id" binding:"required" example:"acc_123456"`
}

type QueryTenantMemberReq struct {
	AccountId string `uri:"account_id" binding:"required" example:"acc_123456"`
}

type UpdateTenantMemberReq struct {
	FirstName   *string `json:"first_name" binding:"omitempty" example:"John"`
	LastName    *string `json:"last_name" binding:"omitempty" example:"Doe"`
	PhoneNumber *string `json:"phone_number" binding:"omitempty,phone_number" example:"+***********"`
	Color       *string `json:"color" binding:"omitempty" example:"#000000"`
	SortIndex   *uint64 `json:"sort_index" binding:"omitempty" example:"1"`
}

type SortTenantMemberReq struct {
	Items []SortTenantMemberItem `json:"items" binding:"required"`
}

type SortTenantMemberItem struct {
	AccountId string `json:"account_id" binding:"required"`
}

type TenantMemberDetail struct {
	TenantMemberEntity
	Roles []RoleDetails `json:"roles"`
}
