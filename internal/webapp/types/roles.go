package types

import commontypes "pebble/internal/webapp/types/common"

type Role struct {
	RoleEntity
	commontypes.Model
}

type RoleEntity struct {
	TenantId   string `json:"tenant_id" gorm:"column:tenant_id"`
	LocationId string `json:"location_id" gorm:"column:location_id"`
	RoleId     string `json:"role_id" gorm:"column:role_id"`
	Name       string `json:"name" gorm:"column:name"`
}

type QueryRolesParams struct {
	Name *string `form:"name" binding:"omitempty"`
}

type CreateRoleReq struct {
	Name string `json:"name" binding:"required"`
}

type CreateRoleDetailReq struct {
	CreateRoleReq
	PermissionIds []string `json:"permission_ids" binding:"omitempty"`
}

type UpdateRoleReq struct {
	Name string `json:"name" binding:"omitempty"`
}

type UpdateRoleDetailReq struct {
	UpdateRoleReq

	PermissionIds []string `json:"permission_ids" binding:"omitempty"`
}

type RoleDetails struct {
	RoleEntity
	Permissions []PermissionEntity `json:"permissions"`
}
