package types

import commontypes "pebble/internal/webapp/types/common"

const (
	InviteStaffSrouce = "Invite Staff"
)

type AccountEntity struct {
	AccountId string `json:"account_id" gorm:"column:account_id" example:"acc_123456"`
	// FirstName    string `json:"first_name" gorm:"column:first_name" example:"<PERSON>"`
	// LastName     string `json:"last_name" gorm:"column:last_name" example:"Doe"`
	Email        string `json:"email" gorm:"column:email" example:"<EMAIL>"`
	PhoneNumber  string `json:"phone_number" gorm:"column:phone_number" example:"+**********"`
	PasswordHash string `json:"-" gorm:"column:password_hash"`
	Source       string `json:"-" gorm:"column:source"`
}

type Account struct {
	AccountEntity
	commontypes.Model
}

type CreateAccountReq struct {
	Email string `json:"email" binding:"required,email" example:"<EMAIL>"`
	// FirstName   string `json:"first_name" binding:"required" example:"<PERSON>"`
	// LastName    string `json:"last_name" binding:"required" example:"Doe"`
	PhoneNumber string `json:"phone_number" binding:"omitempty,phone_number" example:"+***********"`
	Password    string `json:"password" binding:"required" example:"password123"`
	Source      string `json:"source" gorm:"column:source" example:"web"`
}

type UpdateAccountReq struct {
	FirstName   *string `json:"first_name" binding:"omitempty" example:"John"`
	LastName    *string `json:"last_name" binding:"omitempty" example:"Doe"`
	PhoneNumber *string `json:"phone_number" binding:"omitempty,phone_number" example:"+***********"`
	// Email       *string `json:"email" binding:"omitempty,email" example:"<EMAIL>"`
}

type AccountDetail struct {
	TenantMemberEntity
	Tenant   TenantEntity   `json:"tenant"`
	Location LocationEntity `json:"location"`
}
