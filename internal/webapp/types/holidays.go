package types

import commontypes "pebble/internal/webapp/types/common"

type Holiday struct {
	HolidayEntity
	commontypes.Model
}

type HolidayEntity struct {
	TenantID    string `json:"tenant_id" gorm:"column:tenant_id" example:"tenant_123456"`
	LocationID  string `json:"location_id" gorm:"column:location_id" example:"loc_123456"`
	HolidayID   string `json:"holiday_id" gorm:"column:holiday_id" example:"hol_123456"`
	Name        string `json:"name" gorm:"column:name" example:"Independence Day"`
	Date        string `json:"date" gorm:"column:date" example:"2023-07-01"`
	IsDayOff    bool   `json:"is_day_off" gorm:"column:is_day_off" example:"false"`
	Description string `json:"description" gorm:"column:description" example:"Independence Day"`
}

type CreateHolidayReq struct {
	Date        string `json:"date" validate:"required,date" example:"2023-07-01"`
	Name        string `json:"name" gorm:"column:name" example:"Independence Day"`
	IsDayOff    bool   `json:"is_day_off" validate:"required" example:"false"`
	Description string `json:"description" validate:"required" example:"Independence Day"`
}

type UpdateHolidayReq struct {
	Date        *string `json:"date" validate:"required,date" example:"2023-07-01"`
	Name        *string `json:"name" gorm:"column:name" example:"Independence Day"`
	IsDayOff    *bool   `json:"is_day_off" validate:"required" example:"false"`
	Description *string `json:"description" validate:"required" example:"Independence Day"`
}
