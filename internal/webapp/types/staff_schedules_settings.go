package types

import commontypes "pebble/internal/webapp/types/common"

type StaffScheduleSettings struct {
	StaffScheduleSettingsEntity
	commontypes.Model
}

type StaffScheduleSettingsEntity struct {
	TenantID   string `json:"tenant_id" gorm:"column:tenant_id" example:"tenant_123456"`
	LocationID string `json:"location_id" gorm:"column:location_id" example:"loc_123456"`
	StartDate  string `json:"start_date" gorm:"column:start_date" example:"2025-01-01"`
}

type CreateStaffScheduleSettingsReq struct {
	StartDate string `json:"start_date" binding:"required" example:"2025-01-01"`
}

type UpdateStaffScheduleSettingsReq struct {
	StartDate *string `json:"start_date" binding:"omitempty" example:"2025-01-01"`
}
