package types

import commontypes "pebble/internal/webapp/types/common"

type TenantMemberRolesEntity struct {
	TenantId   string `json:"tenant_id" gorm:"column:tenant_id"`
	LocationId string `json:"location_id" gorm:"column:location_id"`
	AccountId  string `json:"account_id" gorm:"column:account_id"`
	RoleId     string `json:"role_id" gorm:"column:role_id"`
}

type TenantMemberRoles struct {
	TenantMemberRolesEntity
	commontypes.Model
}

type BindStaffRoleReq struct {
	AccountId string   `json:"account_id" binding:"required"`
	RoleIds   []string `json:"role_ids" binding:"required"`
}

type CreateTenantMemberRolesReq struct {
	AccountId string   `json:"account_id" binding:"required"`
	RoleIds   []string `json:"role_ids" binding:"required"`
}

type DeleteTenantMemberRolesReq struct {
	AccountId string   `json:"account_id" binding:"required"`
	RoleIds   []string `json:"role_ids" binding:"required"`
}
