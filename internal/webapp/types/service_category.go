package types

import commontypes "pebble/internal/webapp/types/common"

type ServiceCategoryEntity struct {
	TenantId   string             `json:"tenant_id" gorm:"column:tenant_id" example:"tenant_2viQlj16EjDj8zLRbgibcidla4i"`
	LocationId string             `json:"location_id" gorm:"column:location_id" example:"loc_2viQlkAbQuyb04fe9cF04JP2AOj"`
	CategoryId string             `json:"category_id" gorm:"column:category_id" example:"cat_2viQlkAbQuyb04fe9cF04JP2AOj"`
	Name       string             `json:"name" gorm:"column:name" example:"Haircut Services"`
	Status     commontypes.Status `json:"status" gorm:"column:status;default:1" example:"1"`
}

type ServiceCategory struct {
	ServiceCategoryEntity
	commontypes.Model
}

type UpdateServiceCategoryReq struct {
	Name *string `json:"name" validate:"required" example:"Haircut Services"`
}

type CreateServiceCategoryReq struct {
	Name string `json:"name" validate:"required" example:"Haircut Services"`
}
