package types

import (
	"encoding/json"
	commontypes "pebble/internal/webapp/types/common"
	"time"

	"gorm.io/datatypes"
	"gorm.io/gorm"
)

// PhoneCapability represents the capabilities of a phone number
type PhoneCapability string

const (
	PhoneCapabilitySMS   PhoneCapability = "SMS"
	PhoneCapabilityVoice PhoneCapability = "Voice"
	PhoneCapabilityMMS   PhoneCapability = "MMS"
)

// PhoneStatus represents the status of a phone number
// @enum.1=Active status, phone number is currently in use
// @enum.2=Inactive status, phone number is not currently in use
// @enum.3=Suspended status, phone number has been temporarily suspended
type PhoneStatus int

const (
	PhoneStatusActive    PhoneStatus = 1 // Active status, phone number is currently in use
	PhoneStatusInactive  PhoneStatus = 2 // Inactive status, phone number is not currently in use
	PhoneStatusSuspended PhoneStatus = 3 // Suspended status, phone number has been temporarily suspended
)

// PhoneConfig represents the configuration of a phone number
type PhoneConfig struct {
	PhoneConfigEntity
	commontypes.Model
}

// PhoneConfigEntity represents the data needed to create a new phone configuration
type PhoneConfigEntity struct {
	TenantID         string            `json:"tenant_id" gorm:"column:tenant_id" example:"tenant_123456"`
	LocationID       string            `json:"location_id" gorm:"column:location_id" example:"loc_123456"`
	PhoneNumber      string            `json:"phone_number" gorm:"column:phone_number" example:"+15551234567"`
	Region           string            `json:"region" gorm:"column:region" example:"US"`
	ProviderPhoneSID string            `json:"provider_phone_sid" gorm:"column:provider_phone_sid" example:"PN123456789abcdef"`
	Provider         string            `json:"provider" gorm:"column:provider" example:"twilio"`
	CapabilitiesJson datatypes.JSON    `json:"-" gorm:"column:capabilities" `
	Capabilities     []PhoneCapability `json:"capabilities" gorm:"-" example:"[\"SMS\",\"Voice\"]"`
	BindTime         time.Time         `json:"bind_time" gorm:"column:bind_time" example:"**********"`
	ExpireTime       time.Time         `json:"expire_time" gorm:"column:expire_time" example:"**********"`
	Status           PhoneStatus       `json:"status" gorm:"column:status;default:1" example:"1"`
}

// UpdatePhoneConfigReq represents the data needed to update a phone configuration
type UpdatePhoneConfigReq struct {
	Status     *PhoneStatus `json:"status" example:"1"`
	ExpireTime *time.Time   `json:"expire_time" example:"**********"`
}

func (p *PhoneConfigEntity) BeforeCreate(tx *gorm.DB) (err error) {
	if len(p.Capabilities) > 0 {
		raw, err := json.Marshal(p.Capabilities)
		if err != nil {
			return err
		}
		p.CapabilitiesJson = datatypes.JSON(raw)
	}
	return nil
}

func (p *PhoneConfigEntity) BeforeUpdate(tx *gorm.DB) (err error) {
	if len(p.Capabilities) > 0 {
		raw, err := json.Marshal(p.Capabilities)
		if err != nil {
			return err
		}
		p.CapabilitiesJson = datatypes.JSON(raw)
	}
	return nil
}

func (p *PhoneConfigEntity) AfterFind(tx *gorm.DB) (err error) {
	if len(p.CapabilitiesJson) > 0 {
		var caps []PhoneCapability
		if err := json.Unmarshal(p.CapabilitiesJson, &caps); err != nil {
			return err
		}
		p.Capabilities = caps
	}
	return nil
}
