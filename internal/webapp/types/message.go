package types

import commontypes "pebble/internal/webapp/types/common"

// MessageChannelType represents the type of message channel
type MessageChannelType int

const (
	MessageChannelTypeSMS   MessageChannelType = 1 // SMS message
	MessageChannelTypeEmail MessageChannelType = 2 // Email message
	MessageChannelTypePhone MessageChannelType = 3 // Phone call
)

// MessageDirection represents the direction of message
type MessageDirection int

const (
	MessageDirectionSend     MessageDirection = 1 // Outgoing message
	MessageDirectionReceived MessageDirection = 2 // Incoming message
)

// MessageRecord represents a message record in database
type MessageRecord struct {
	MessageRecordEntity
	commontypes.Model
}

// MessageRecordEntity represents the data needed to create a new message record
type MessageRecordEntity struct {
	TenantID     string             `json:"tenant_id" binding:"required" gorm:"column:tenant_id" example:"tenant_123456"`
	LocationID   string             `json:"location_id" binding:"required" gorm:"column:location_id" example:"loc_123456"`
	ClientID     string             `json:"client_id" binding:"required" gorm:"column:client_id" example:"client_123456"`
	MessageID    string             `json:"message_id" binding:"required" gorm:"column:message_id" example:"msg_123456"`
	ChannelType  MessageChannelType `json:"channel_type" binding:"required" gorm:"column:channel_type" example:"1"`
	MsgDirection MessageDirection   `json:"msg_direction" binding:"required" gorm:"column:msg_direction" example:"1"`
	SendTime     int64              `json:"send_time" gorm:"column:send_time" example:"1625097600"`
}

type QueryMessageParams struct {
	ClientID *string `form:"client_id" binding:"omitempty" example:"client_123456"`
	Page     *int    `form:"page" binding:"omitempty,min=1" example:"1"`
	PageSize *int    `form:"page_size" binding:"omitempty,min=1,max=100" example:"20"`
}

type QueryMessageGroupedByClientResp struct {
	MessageRecord
	ClientFirstName string `json:"client_first_name"  example:"John"`
	ClientLastName  string `json:"client_last_name"  example:"Doe"`
	MessageContent  string `json:"message_context"  example:"Your appointment is confirmed for tomorrow at 2pm."`
}

type QueryClientMessagesParams struct {
	Page     *int `form:"page" binding:"omitempty,min=1" example:"1"`
	PageSize *int `form:"page_size" binding:"omitempty,min=1" example:"20"`
}

type MessageRecordDetail struct {
	MessageRecord
	SmsMessage *SMSMessage `json:"sms_message"`
}
