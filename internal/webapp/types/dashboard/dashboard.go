package dashboardtypes

import (
	appointmenttypes "pebble/internal/webapp/types/appointment"
)

type DashboardAppointmentsSummary struct {
	AllCount             int `json:"all_count"`
	ExpectedCount        int `json:"expected_count"`
	CheckedInCount       int `json:"checked_in_count"`
	PendingCheckoutCount int `json:"pending_checkout_count"`
	CompletedCount       int `json:"completed_count"`
}

type DashboardAppointmentsResp struct {
	Summary         DashboardAppointmentsSummary         `json:"summary"`
	All             []appointmenttypes.AppointmentDetail `json:"all"`
	Expected        []appointmenttypes.AppointmentDetail `json:"expected"`
	CheckedIn       []appointmenttypes.AppointmentDetail `json:"checked_in"`
	PendingCheckout []appointmenttypes.AppointmentDetail `json:"pending_checkout"`
	Completed       []appointmenttypes.AppointmentDetail `json:"completed"`
}
