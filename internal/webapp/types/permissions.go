package types

import commontypes "pebble/internal/webapp/types/common"

type Permission struct {
	PermissionEntity
	commontypes.Model
}

type PermissionEntity struct {
	TenantId     string `json:"tenant_id" gorm:"column:tenant_id"`
	LocationId   string `json:"location_id" gorm:"column:location_id"`
	PermissionId string `json:"permission_id" gorm:"column:permission_id"`
	Name         string `json:"name" gorm:"column:name" example:"read"`
	Description  string `json:"description" gorm:"column:description" example:"Read permission"`
}

func PermissionToEntity(permission []Permission) []PermissionEntity {
	var entities []PermissionEntity
	for _, permission := range permission {
		entities = append(entities, permission.PermissionEntity)
	}
	return entities
}

type CreatePermissionReq struct {
	Name        string `json:"name" binding:"required" example:"read"`
	Description string `json:"description" binding:"required" example:"Read permission"`
}

type UpdatePermissionReq struct {
	Name        *string `json:"name" binding:"omitempty" example:"read"`
	Description *string `json:"description" binding:"omitempty" example:"Read permission"`
}

type QueryPermissionsParams struct {
	Name *string `form:"name" binding:"omitempty" example:"read"`
}
