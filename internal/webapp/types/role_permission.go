package types

import commontypes "pebble/internal/webapp/types/common"

type RolePermission struct {
	RolePermissionEntity
	commontypes.Model
}

type RolePermissionEntity struct {
	TenantId     string `json:"tenant_id" gorm:"column:tenant_id"`
	LocationId   string `json:"location_id" gorm:"column:location_id"`
	RoleId       string `json:"role_id" gorm:"column:role_id"`
	PermissionId string `json:"permission_id" gorm:"column:permission_id"`
}

type BatchCreateRolePermissionReq struct {
	RoleId        string   `json:"role_id" binding:"required"`
	PermissionIds []string `json:"permission_ids" binding:"required"`
}

type CreateRolePermissionReq struct {
	RoleId       string `json:"role_id" binding:"required"`
	PermissionId string `json:"permission_id" binding:"required"`
}
