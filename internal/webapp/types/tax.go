package types

import commontypes "pebble/internal/webapp/types/common"

type TaxEntity struct {
	TenantId   string `json:"tenant_id" gorm:"column:tenant_id" example:"tenant_123456"`
	LocationId string `json:"location_id" gorm:"column:location_id" example:"loc_123456"`
	TaxId      string `json:"tax_id" gorm:"column:tax_id" example:"tax_123456"`
	Name       string `json:"name" db:"name" example:"Sales Tax"`
	Rate       int64  `json:"rate" db:"rate" example:"800"`
}

type Tax struct {
	TaxEntity
	commontypes.Model
}

type CreateTaxReq struct {
	Name string `json:"name" binding:"required" example:"Sales Tax"`
	Rate int64  `json:"rate" binding:"required,min=0,max=100000" example:"800"`
}

type UpdateTaxReq struct {
	Name *string  `json:"name" example:"Sales Tax"`
	Rate *float64 `json:"rate" binding:"omitempty,min=0,max=100000" example:"8.0"`
}
