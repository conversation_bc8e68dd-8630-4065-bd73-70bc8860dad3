package reporttypes

import (
	"pebble/internal/webapp/types"
	paymenttypes "pebble/internal/webapp/types/payment"
)

type CalculateStaffSummaryReq struct {
	Location           types.Location
	Staffs             []types.TenantMember
	PaymentItemRecords []paymenttypes.PaymentItemRecords
}

type StaffSummary struct {
	AccountId    string `json:"account_id"`
	AccountName  string `json:"account_name"`
	Revenue      string `json:"revenue"`
	RevenueNum   int64  `json:"revenue_num"`
	Tip          string `json:"tip"`
	TipNum       int64  `json:"tip_num"`
	ServiceCount int64  `json:"service_count"`
	ClientCount  int64  `json:"client_count"`
}
