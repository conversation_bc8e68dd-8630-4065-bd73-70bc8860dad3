package reporttypes

import (
	"pebble/internal/webapp/types"
	paymenttypes "pebble/internal/webapp/types/payment"
)

type DashboardReportResp struct {
	// ApptointmentCount     int64 `json:"appointment_count"`
	// ServicedCustomerCount int64 `json:"serviced_customer_count"`
	// TotalRevenue          int64 `json:"total_revenue"`
	// ServiceRevenue        int64 `json:"service_revenue"`
	// GiftCardRevenue       int64 `json:"gift_card_revenue"`
	// CashRevenue           int64 `json:"cash_revenue"`
	// TotalTips             int64 `json:"total_tips"`
	// TotalTax              int64 `json:"total_tax"`
	BusinessSummary
	// RevenueSummary        []RevenueSummary `json:"revenue_summary"`
	StaffSummary  []StaffSummary  `json:"staff_summary"`
	MethodSummary []MethodSummary `json:"method_summary"`
}

type CalculateBusinessSummaryReq struct {
	Location           types.Location
	PaymentMethods     []paymenttypes.PaymentMethods
	PaymentRecords     []paymenttypes.PaymentRecords
	PaymentItemRecords []paymenttypes.PaymentItemRecords
}

type BusinessSummary struct {
	ApptointmentCount       int64 `json:"appointment_count"`
	UnpaidApptointmentCount int64 `json:"unpaid_appointment_count"`
	ServicedCustomerCount   int64 `json:"serviced_customer_count"`

	TotalRevenue    string `json:"total_revenue"`
	TotalRevenueNum int64  `json:"total_revenue_num"`

	ServiceRevenue    string `json:"service_revenue"`
	ServiceRevenueNum int64  `json:"service_revenue_num"`

	GiftCardRevenue    string `json:"gift_card_revenue"`
	GiftCardRevenueNum int64  `json:"gift_card_revenue_num"`

	CashRevenue    string `json:"cash_revenue"`     // todo delete
	CashRevenueNum int64  `json:"cash_revenue_num"` // todo delete

	TotalTip    string `json:"total_tip"`
	TotalTipNum int64  `json:"total_tip_num"`

	TotalTax    string `json:"total_tax"`
	TotalTaxNum int64  `json:"total_tax_num"`
}

type RevenueSummary struct {
	Name          string `json:"name"`
	Revenue       string `json:"revenue"`
	AmountRevenue int64  `json:"amount_revenue"`
}
