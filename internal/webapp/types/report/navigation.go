package reporttypes

// NavigationCategory represents a group of reports in the navigation menu.
type NavigationCategory struct {
	GroupName string           `json:"group_name"`
	Items     []NavigationItem `json:"items"`
}

// NavigationItem represents a single report in the navigation menu.
type NavigationItem struct {
	Key         string `json:"key"`
	Name        string `json:"name"`
	Description string `json:"description"`
}

// NavigationResp is the response payload for GET /api/v1/reports/navigation.
type NavigationResp struct {
	Categories []NavigationCategory `json:"categories"`
}

// DefaultNavigationResp provides the default navigation structure for the report navigation API.
var DefaultNavigationResp = NavigationResp{
	Categories: []NavigationCategory{
		{
			GroupName: "Sale",
			Items: []NavigationItem{
				{Key: "sale-daily", Name: "Daily", Description: ""},
				{Key: "sale-payment-methods", Name: "Payment Methods", Description: ""},
				{Key: "sale-all", Name: "All Sales", Description: ""},
				{Key: "sale-unpaid-appointment", Name: "Unpaid Appointment", Description: ""},
			},
		},
		{
			GroupName: "Staff",
			Items: []NavigationItem{
				{Key: "staff-performance", Name: "Staff Performance", Description: ""},
			},
		},
	},
}
