package reporttypes

import (
	"pebble/internal/webapp/types"
	paymenttypes "pebble/internal/webapp/types/payment"
)

type CalculateMethodSummaryReq struct {
	Location       types.Location
	PaymentMethods []paymenttypes.PaymentMethods
	PaymentRecords []paymenttypes.PaymentRecords
}

type MethodSummary struct {
	MethodId   string `json:"method_id"`
	MethodName string `json:"method_name"`
	Revenue    string `json:"revenue"`
	RevenueNum int64  `json:"revenue_num"`
}
