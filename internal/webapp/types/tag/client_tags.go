package tagtypes

import (
	commontypes "pebble/internal/webapp/types/common"
)

type ClientTag struct {
	ClientTagEntity
	commontypes.Model
}

type ClientTagEntity struct {
	TenantId   string `json:"tenant_id" gorm:"column:tenant_id" example:"tenant_123456"`
	LocationId string `json:"location_id" gorm:"column:location_id" example:"loc_123456"`
	TagId      string `json:"tag_id" gorm:"column:tag_id" example:"tag_123456"`
	ClientId   string `json:"client_id" gorm:"column:client_id" example:"client_123456"`
	Sttutus    int8   `json:"status" gorm:"column:status;default:1" example:"1"`
}

type CreateClientTagReq struct {
	ClientId string   `json:"client_id" example:"client_123456"`
	TagIds   []string `json:"tag_ids" binding:"required" example:"tag_123456"`
}

type UpdateClientTagReq struct {
	TagIds []string `json:"tag_ids" example:"tag_123456"`
}

type GetClientTagCondition struct {
	ClientIds []string            `json:"client_ids" example:"client_123456"`
	Status    *commontypes.Status `json:"status" example:"1"`
	TagIds    []string            `json:"tag_ids" example:"tag_123456"`
}
