package tagtypes

import (
	commontypes "pebble/internal/webapp/types/common"
)

type TagType int

const (
	TagTypeClient   TagType = 1
	TagTypePe       TagType = 2
	TagTypeSubClien TagType = 3
)

type Tag struct {
	TagEntity
	commontypes.Model
}

type TagEntity struct {
	TenantId    string             `json:"tenant_id" gorm:"column:tenant_id" example:"tenant_123456"`
	LocationId  string             `json:"location_id" gorm:"column:location_id" example:"loc_123456"`
	TagId       string             `json:"tag_id" gorm:"column:tag_id" example:"tag_123456"`
	Tag         string             `json:"tag" gorm:"column:tag" example:"Tag Name"`
	Color       string             `json:"color" gorm:"column:color;default:#000000" example:"#000000"`
	Type        TagType            `json:"type" gorm:"column:type;default:1" example:"1"`
	Status      commontypes.Status `json:"status" gorm:"column:status;default:1" example:"1"`
	Description string             `json:"description" gorm:"column:description" example:"Tag Description"`
}

type QueryTagsParams struct {
	// LocationId *string  `uri:"location_id" binding:"required" example:"loc_123456"`
	Type *TagType `form:"type" example:"1"`
}

type CreateTagReq struct {
	Tag         string  `json:"tag" binding:"required" example:"Tag Name"`
	Color       string  `json:"color" binding:"omitempty" example:"#000000"`
	Type        TagType `json:"type" binding:"omitempty" example:"0"`
	Description string  `json:"description" binding:"omitempty" example:"Tag Description"`
}

type UpdateTagReq struct {
	Tag         *string  `json:"tag" binding:"omitempty" example:"Tag Name"`
	Color       *string  `json:"color" binding:"omitempty" example:"#000000"`
	Type        *TagType `json:"type" binding:"omitempty" example:"0"`
	Description *string  `json:"description" binding:"omitempty" example:"Tag Description"`
}
