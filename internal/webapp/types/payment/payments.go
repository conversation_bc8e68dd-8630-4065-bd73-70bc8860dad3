package paymenttypes

import commontypes "pebble/internal/webapp/types/common"

type PaymentsReq struct {
	TargetId  string                `json:"target_id,omitempty"`
	OrderType commontypes.OrderType `json:"order_type" binding:"required,min=1,max=2" example:"1"`
	OrderId   string                `json:"order_id" binding:"required" example:"appt_123456789"`
	MethodId  string                `json:"method_id" binding:"required" example:"method_123456"`
	Amount    int64                 `json:"amount" binding:"required" example:"10000"`
	Tip       int64                 `json:"tip" binding:"omitempty" example:"1000"`
	Tax       int64                 `json:"tax" binding:"omitempty" example:"1000"`
	Fee       int64                 `json:"fee" binding:"omitempty" example:"1000"`
}

type QueryPaymentsParams struct {
	Page       *int                              `form:"page" binding:"omitempty,min=1" example:"1"`
	PageSize   *int                              `form:"page_size" binding:"omitempty,min=1,max=100" example:"20"`
	StatusList []commontypes.PaymentRecordStatus `form:"status" binding:"omitempty,oneof=1 2 3 4"`
	// Types      []PaymentRecordType   `json:"types" binding:"omitempty,oneof=1 2"`
	OrderType *commontypes.OrderType `form:"order_type" binding:"omitempty,oneof=1 2"`
	OrderId   *string                `form:"order_id" binding:"omitempty"`
	MethodId  *string                `form:"method_id" binding:"omitempty"`
	ClientId  *string                `form:"client_id" binding:"omitempty"`
	StartDate *string                `form:"start_date" binding:"omitempty"`
	EndDate   *string                `form:"end_date" binding:"omitempty"`
	Timezone  string                 `form:"-"`
}

type CreateRefundReq struct {
	PaymentIds     []string `json:"payment_ids" binding:"required"`
	PaymentItemIds []string `json:"payment_item_ids" binding:"omitempty"`
}
