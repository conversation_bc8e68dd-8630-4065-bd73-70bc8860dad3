package paymenttypes

import commontypes "pebble/internal/webapp/types/common"

type PaymentMethods struct {
	PaymentMethodsEntity
	commontypes.Model
}

type PaymentMethodsEntity struct {
	TenantId   string                          `json:"tenant_id" binding:"required" gorm:"column:tenant_id" example:"tenant_123456"`
	LocationId string                          `json:"location_id" binding:"required" gorm:"column:location_id" example:"loc_123456"`
	MethodId   string                          `json:"method_id" binding:"required" gorm:"column:method_id" example:"method_123456"`
	Type       commontypes.PaymentMethodTypes  `json:"type" binding:"required" gorm:"column:type" example:"1"`
	Name       string                          `json:"name" binding:"required" gorm:"column:name" example:"Cash"`
	Status     commontypes.PaymentMethodStatus `json:"status" binding:"required" gorm:"column:status;default:1" example:"1"`
}

type CreatePaymentMethodReq struct {
	Type   commontypes.PaymentMethodTypes  `json:"type" binding:"required,min=1,max=3" gorm:"column:type" example:"1"`
	Name   string                          `json:"name" binding:"required" gorm:"column:name" example:"Cash"`
	Status commontypes.PaymentMethodStatus `json:"status" binding:"required" gorm:"column:status;default:1" example:"1"`
}

type UpdatePaymentMethodReq struct {
	Type   commontypes.PaymentMethodTypes  `json:"type" binding:"required,min=1,max=3" gorm:"column:type" example:"1"`
	Name   string                          `json:"name" binding:"required" gorm:"column:name" example:"Cash"`
	Status commontypes.PaymentMethodStatus `json:"status" binding:"required" gorm:"column:status;default:1" example:"1"`
}

type QueryPaymentMethodParams struct {
	Type   *commontypes.PaymentMethodTypes  `form:"type" binding:"omitempty,min=1,max=3" gorm:"column:type" example:"1"`
	Name   *string                          `form:"name" binding:"omitempty" gorm:"column:name" example:"Cash"`
	Status *commontypes.PaymentMethodStatus `form:"status" binding:"omitempty,min=1" gorm:"column:status;default:1" example:"1"`
}
