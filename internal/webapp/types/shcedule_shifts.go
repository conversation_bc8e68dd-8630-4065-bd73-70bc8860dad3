package types

type QueryAvailableScheduleShiftsParams struct {
	Date string `form:"date" binding:"required,date" example:"2023-07-31"`
}

type QueryAvailableScheduleShiftsReps struct {
	Date string `json:"date" example:"2023-07-31"`
	Time int64  `json:"time" example:"36000"`

	AvailableStaffNum int64 `json:"available_staff_num" example:"2"`
	StaffNum          int64 `json:"staff_num" example:"2"`
}

type QueryStaffScheduleShiftsParams struct {
	Date     string `form:"date" binding:"required,date" example:"2023-07-31"`
	Time     int64  `form:"time" binding:"required,min=0,max=86400" example:"36000"`
	Duration int64  `form:"duration" binding:"omitempty,min=0,max=86400" example:"3600"`
}

type QueryStaffScheduleShiftsReps struct {
	AccountId  string `json:"account_id"  example:"acc_123456"`
	TenantId   string `json:"tenant_id"  example:"tenant_123456"`
	LocationId string `json:"location_id"  example:"loc_123456"`
	FirstName  string `json:"first_name"  example:"<PERSON>"`
	LastName   string `json:"last_name"  example:"Doe"`
	Available  bool   `json:"available"  example:"true"`
}
