package types

import commontypes "pebble/internal/webapp/types/common"

type ServiceStaffEntity struct {
	TenantId   string `json:"tenant_id" gorm:"column:tenant_id" example:"tenant_123456"`
	LocationId string `json:"location_id" gorm:"column:location_id" example:"loc_123456"`
	ServiceId  string `json:"service_id" gorm:"column:service_id" example:"srv_123456"`
	AccountId  string `json:"account_id" gorm:"column:account_id" example:"acc_123456"`
}

type ServiceStaff struct {
	ServiceStaffEntity
	commontypes.Model
}

type ServiceStaffItem struct {
	AccountId string `json:"account_id" validate:"required" example:"acc_123456"`
	ServiceId string `json:"service_id" validate:"required" example:"srv_123456"`
}

type UpdateServiceStaffReq struct {
	AccountIds []string `json:"account_ids" validate:"required" example:"[\"acc_123456\",\"acc_234567\"]"`
}
