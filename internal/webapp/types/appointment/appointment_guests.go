package appointmenttypes

import commontypes "pebble/internal/webapp/types/common"

type AppointmentGuestEntity struct {
	TenantId      string `gorm:"column:tenant_id;type:varchar(64);not null"`
	LocationId    string `gorm:"column:location_id;type:varchar(64);not null"`
	AppointmentId string `gorm:"column:appointment_id;type:varchar(64);not null"`
	ClientId      string `gorm:"column:client_id;type:varchar(64);not null"`
}

type AppointmentGuest struct {
	AppointmentGuestEntity
	commontypes.Model
}
