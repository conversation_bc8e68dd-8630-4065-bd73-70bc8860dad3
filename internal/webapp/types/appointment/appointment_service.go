package appointmenttypes

import (
	"pebble/internal/webapp/types"
	commontypes "pebble/internal/webapp/types/common"
)

type AppointmentServiceEntity struct {
	TenantId           string                 `json:"tenant_id" gorm:"column:tenant_id" example:"tenant_123456"`
	LocationId         string                 `json:"location_id" gorm:"column:location_id" example:"loc_123456"`
	AccountId          string                 `json:"account_id" gorm:"column:account_id" example:"acc_123456"`
	AppointmentId      string                 `json:"appointment_id" gorm:"column:appointment_id" example:"appt_123456789"`
	ScheduledStartTime int64                  `json:"scheduled_start_time" gorm:"column:scheduled_start_time;default:0" example:"36000"`
	ServiceType        types.ServiceType      `json:"service_type" gorm:"column:service_type" example:"1"`
	ServiceId          string                 `json:"service_id" gorm:"column:service_id" example:"srv_123456"`
	ApptServiceId      string                 `json:"appt_service_id" gorm:"column:appt_service_id" example:"aptsrv_123456"`
	TargetType         commontypes.TargetType `json:"target_type" gorm:"column:target_type" example:"1"`
	TargetId           string                 `json:"target_id" gorm:"column:target_id" example:"tgt_123456"`
	Name               string                 `json:"name" gorm:"column:name" example:"Haircut"`
	Duration           int64                  `json:"duration" gorm:"column:duration" example:"30"`
	TaxRate            int64                  `json:"tax_rate" gorm:"column:tax_rate" example:"800"`
	// Tax           int64             `json:"tax" gorm:"column:tax" example:"2500"`
	Price         int64  `json:"price" gorm:"column:price" example:"2500"`
	OriginTaxId   string `json:"origin_tax_id" gorm:"column:origin_tax_id" example:"tax_123456"`
	OriginTaxRate int64  `json:"origin_tax_rate" gorm:"column:origin_tax_rate" example:"800"`
	OriginPrice   int64  `json:"origin_price" gorm:"column:origin_price" example:"2500"`
	Snapshot      string `json:"snapshot" gorm:"column:snapshot" example:"{\"name\":\"Haircut\",\"duration\":30,\"price\":2500}"`
}

type AppointmentService struct {
	AppointmentServiceEntity
	commontypes.Model
}

type AppointmentSerivceDetail struct {
	AppointmentService
	AccountFirstName string `json:"account_first_name" example:"John"`
	AccountLastName  string `json:"account_last_name" example:"Doe"`
	TargetFirstName  string `json:"target_first_name" example:"John"`
	TargetLastName   string `json:"target_last_name" example:"Doe"`
}

type CreateAppointmentServiceReq struct {
	ScheduledStartTime int64                  `json:"scheduled_start_time" binding:"required"`
	AppointmentId      string                 `json:"appointment_id" binding:"required"`
	ServiceType        types.ServiceType      `json:"service_type" binding:"required"`
	ServiceId          string                 `json:"service_id" binding:"required"`
	AccountId          string                 `json:"account_id" binding:"required"`
	TargetType         commontypes.TargetType `json:"target_type" binding:"required"`
	TargetId           string                 `json:"target_id" binding:"required"`
	Name               string                 `json:"name" binding:"required"`
	Duration           int64                  `json:"duration" binding:"required"`
	TaxRate            int64                  `json:"tax_rate" binding:"required"`
	Price              int64                  `json:"price" binding:"required"`
	OriginTaxId        string                 `json:"origin_tax_id" binding:"required"`
	OriginTaxRate      int64                  `json:"origin_tax_rate" binding:"required"`
	OriginPrice        int64                  `json:"origin_price" binding:"required"`
	Snapshot           string                 `json:"snapshot" binding:"required"`
}

type UpdateAppointmentServiceReq struct {
	ServiceType types.ServiceType      `json:"service_type" binding:"required"`
	ServiceId   string                 `json:"service_id" binding:"required"`
	AccountId   string                 `json:"account_id" binding:"required" example:"staff_456"`
	TargetType  commontypes.TargetType `json:"target_type" binding:"omitempty,min=0" example:"1"`
	TargetId    string                 `json:"target_id" binding:"required" example:"client_123"`
	Duration    int                    `json:"duration" binding:"required" example:"4500"`
	TaxRate     int64                  `json:"tax_rate" binding:"omitempty,min=0,max=100000" example:"1000"`
	Price       int64                  `json:"price" binding:"required,min=0" example:"6000"`
}

type BatchUpdateAppointmentServiceReq struct {
	Items []BatchUpdateAppointmentServiceReqItem `json:"items" binding:"required"`
}

type BatchUpdateAppointmentServiceReqItem struct {
	ApptServiceId string `json:"appt_service_id" binding:"required" example:"aptsrv_123456789"`
	UpdateAppointmentServiceReq
}

type AssignmentAppointmentServiceReq struct {
	Items []AssignmentAppointmentServiceItem `json:"items" binding:"required"`
}

type AssignmentAppointmentServiceItem struct {
	ApptServiceId string `json:"appt_service_id" binding:"required" example:"aptsrv_123456789"`
	TargetId      string `json:"target_id" binding:"required" example:"client_123"`
}

type UpdateAppointmentStatusReq struct {
	Status commontypes.AppointmentStatusType `json:"status" binding:"required,min=0,max=7" example:"2"`
}

type AppointmentServiceSnapshot struct {
	AppointmentServiceSnapshotItem
	Items []AppointmentServiceSnapshotItem `json:"items,omitempty"`
}

type AppointmentServiceSnapshotItem struct {
	ServiceType types.ServiceType `json:"service_type" gorm:"column:service_type" example:"1"`
	ServiceId   string            `json:"service_id" gorm:"column:service_id" example:"srv_123456"`
	Name        string            `json:"name" gorm:"column:name" example:"Haircut"`
	Duration    int64             `json:"duration" gorm:"column:duration" example:"30"`
	TaxRate     int64             `json:"tax_rate" gorm:"column:tax_rate,min=0,max=100000" example:"800"`
	Price       int64             `json:"price" gorm:"column:price,min=0" example:"2500"`
	TaxId       string            `json:"tax_id" gorm:"column:tax_id" example:"tax_123456"`
}

type AppointmentPaymentReq struct {
	TargetId string               `json:"target_id,omitempty" example:"client_123456"`
	MethodId string               `json:"method_id" binding:"required" example:"method_123456"`
	Amount   int64                `json:"amount" binding:"required" example:"10000"`
	Tax      int64                `json:"tax" binding:"required" example:"10000"`
	Fee      int64                `json:"fee" binding:"required" example:"10000"`
	Tip      int64                `json:"tip" binding:"required" example:"10000"`
	FeePayer commontypes.FeePayer `json:"fee_payer" binding:"omitempty" example:"1"`
}

type UpdateAccountAndTime struct {
	SrcAccountId       string `json:"src_account_id" binding:"required" example:"acc_123456"`
	DstAccountId       string `json:"dst_account_id" binding:"required" example:"acc_123456"`
	ScheduledStartTime int64  `json:"scheduled_start_time" binding:"required" example:"39600"`
}
