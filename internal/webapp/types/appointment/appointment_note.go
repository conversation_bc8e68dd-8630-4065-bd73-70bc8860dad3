package appointmenttypes

import (
	commontypes "pebble/internal/webapp/types/common"
)

type AppointmentNote struct {
	AppointmentNoteEntity
	commontypes.Model
}

type AppointmentNoteEntity struct {
	TenantId      string `json:"tenant_id" gorm:"column:tenant_id" example:"tenant_123456"`
	LocationId    string `json:"location_id" gorm:"column:location_id" example:"loc_123456"`
	AppointmentId string `json:"appointment_id" gorm:"column:appointment_id" example:"appt_123456"`
	// NoteId        string `json:"note_id" gorm:"column:note_id" example:"note_123456"`
	Note string `json:"note" gorm:"column:note" example:"This is a note"`
}

type CreateAppointmentNoteReq struct {
	AppointmentId string `json:"appointment_id" binding:"required" example:"appt_123456"`
	Note          string `json:"note" binding:"required" example:"This is a note"`
}

type UpdateAppointmentNoteReq struct {
	Note *string `json:"note" example:"This is a note"`
}
