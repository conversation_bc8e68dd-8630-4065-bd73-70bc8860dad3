package appointmenttypes

import (
	commontypes "pebble/internal/webapp/types/common"
	paymenttypes "pebble/internal/webapp/types/payment"
	"pebble/pkg/paystream"
)

type Bills struct {
	CurrencySymbol  string                              `json:"currency_symbol" gorm:"-" example:"$"`
	AmountTip       int64                               `json:"amount_tip" binding:"required" gorm:"column:amount_tip" example:"10000"`
	AmountTax       int64                               `json:"amount_tax" binding:"required" gorm:"column:amount_tax" example:"10000"`
	AmountFee       int64                               `json:"amount_fee" binding:"required" gorm:"column:amount_fee" example:"10000"`
	AmountTotal     int64                               `json:"amount_total" binding:"required" gorm:"column:amount_total" example:"10000"`
	AmountSubtotal  int64                               `json:"amount_subtotal" binding:"required" gorm:"column:amount_subtotal" example:"10000"`
	AmountRefund    int64                               `json:"amount_refund" binding:"required" gorm:"column:amount_refund" example:"10000"`
	AmountPaid      int64                               `json:"amount_paid" binding:"required" gorm:"column:amount_paid" example:"10000"`
	AmountUnpaid    int64                               `json:"amount_unpaid" binding:"required" gorm:"column:amount_unpaid" example:"10000"`
	AmountTipUnpaid int64                               `json:"amount_tip_unpaid" binding:"required" gorm:"column:amount_tip_unpaid" example:"10000"`
	AmountTipPaid   int64                               `json:"amount_tip_paid" binding:"required" gorm:"column:amount_tip_paid" example:"10000"`
	FeePayer        commontypes.FeePayer                `json:"fee_payer" binding:"required" gorm:"column:fee_payer" example:"1"`
	ClientId        string                              `json:"client_id" example:"client_123456"`
	ClientFirstName string                              `json:"client_first_name" example:"John"`
	ClientLastName  string                              `json:"client_last_name" example:"Doe"`
	PaymentStatus   commontypes.PaymentRecordStatus     `json:"payment_status" binding:"required" gorm:"column:payment_status" example:"1"`
	Services        []ServiceBills                      `json:"services"`
	Tips            []AppointmentTipDetail              `json:"tips"`
	PaymentRecords  []paymenttypes.PaymentRecordDetails `json:"payment_records"`
	Fee             paystream.Fee                       `json:"-"`
}

type ServiceBills struct {
	ApptServiceId    string                 `json:"appt_service_id" example:"aptsrv_123456"`
	AccountId        string                 `json:"account_id" example:"account_123456"`
	AccountFirstName string                 `json:"account_first_name" example:"John"`
	AccountLastName  string                 `json:"account_last_name" example:"Doe"`
	TargetType       commontypes.TargetType `json:"target_type" gorm:"column:target_type" example:"1"`
	TargetId         string                 `json:"target_id" gorm:"column:target_id" example:"tgt_123456"`
	TargetFirstName  string                 `json:"target_first_name" example:"John"`
	TargetLastName   string                 `json:"target_last_name" example:"Doe"`
	ServiceId        string                 `json:"service_id" example:"srv_123456"`
	Name             string                 `json:"name" example:"Haircut"`
	Duration         int64                  `json:"duration" example:"30"`
	Price            int64                  `json:"price" example:"2500"`
	Tax              int64                  `json:"tax" example:"2500"`
	OriginPrice      int64                  `json:"origin_price" example:"2500"`
	TaxRate          int64                  `json:"tax_rate" example:"800"`
	OriginTaxRate    int64                  `json:"origin_tax_rate" example:"800"`
}

type AABills struct {
	CurrencySymbol  string                          `json:"currency_symbol" gorm:"-" example:"$"`
	AmountTip       int64                           `json:"amount_tip" binding:"required" gorm:"column:amount_tip" example:"10000"`
	AmountTax       int64                           `json:"amount_tax" binding:"required" gorm:"column:amount_tax" example:"10000"`
	AmountFee       int64                           `json:"amount_fee" binding:"required" gorm:"column:amount_fee" example:"10000"`
	AmountTotal     int64                           `json:"amount_total" binding:"required" gorm:"column:amount_total" example:"10000"`
	AmountSubtotal  int64                           `json:"amount_subtotal" binding:"required" gorm:"column:amount_subtotal" example:"10000"`
	AmountRefund    int64                           `json:"amount_refund" binding:"required" gorm:"column:amount_refund" example:"10000"`
	AmountPaid      int64                           `json:"amount_paid" binding:"required" gorm:"column:amount_paid" example:"10000"`
	AmountUnpaid    int64                           `json:"amount_unpaid" binding:"required" gorm:"column:amount_unpaid" example:"10000"`
	AmountTipUnpaid int64                           `json:"amount_tip_unpaid" binding:"required" gorm:"column:amount_tip_unpaid" example:"10000"`
	AmountTipPaid   int64                           `json:"amount_tip_paid" binding:"required" gorm:"column:amount_tip_paid" example:"10000"`
	FeePayer        commontypes.FeePayer            `json:"fee_payer" binding:"required" gorm:"column:fee_payer" example:"1"`
	PaymentStatus   commontypes.PaymentRecordStatus `json:"payment_status" binding:"required" gorm:"column:payment_status" example:"1"`
	Items           []Bills                         `json:"items"`
	Fee             paystream.Fee                   `json:"-"`
}

type BillReq struct {
	CurrencySymbol    string
	Appointment       AppointmentDetail
	PaymentMethodName string
}
