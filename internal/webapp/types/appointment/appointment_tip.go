package appointmenttypes

import commontypes "pebble/internal/webapp/types/common"

type AppointmentTip struct {
	AppointmentTipEntity
	commontypes.Model
}

type AppointmentTipEntity struct {
	TenantId      string                          `json:"tenant_id" gorm:"column:tenant_id" example:"tenant_123456"`
	LocationId    string                          `json:"location_id" gorm:"column:location_id" example:"loc_123456"`
	AppointmentId string                          `json:"appointment_id" gorm:"column:appointment_id" example:"appt_123456"`
	ApptTipId     string                          `json:"appt_tip_id" gorm:"column:appt_tip_id" example:"appttip_123456"`
	AccountId     string                          `json:"account_id" gorm:"column:account_id" example:"acc_123456"`
	ClientId      string                          `json:"client_id" gorm:"column:client_id" example:"cli_123456"`
	AmountTip     int64                           `json:"amount_tip" gorm:"column:amount_tip" example:"10000"`
	PaymentStatus commontypes.PaymentRecordStatus `json:"payment_status" gorm:"column:payment_status" example:"1"`
}

type AppointmentTipDetail struct {
	AppointmentTip
	AccountFirstName string `json:"account_first_name" example:"John"`
	AccountLastName  string `json:"account_last_name" example:"Doe"`
	ClientFirstName  string `json:"client_first_name" example:"John"`
	ClientLastName   string `json:"client_last_name" example:"Doe"`
}

type BatchUpdateAppointmentTipReqItem struct {
	AppTipId  string `json:"app_tip_id" binding:"omitempty"`
	AccountId string `json:"account_id" binding:"required"`
	ClientId  string `json:"client_id" binding:"required"`
	AmountTip int64  `json:"amount_tip" binding:"required"`
}

type BatchUpdateTipsReq struct {
	Items []BatchUpdateAppointmentTipReqItem `json:"items" binding:"required"`
}

type UpdateTipReq struct {
	AccountId string `json:"account_id" binding:"required"`
	AmountTip int64  `json:"amount_tip" binding:"required"`
	ClientId  string `json:"client_id" binding:"required"`
}

type AddAppointmentTipsReq struct {
	AccountId string `json:"account_id" binding:"required"`
	AmountTip int64  `json:"amount_tip" binding:"required"`
	ClientId  string `json:"client_id" binding:"required"`
}

type CreateAppointmentTipReq struct {
	AppointmentId string                          `json:"appointment_id" gorm:"column:appointment_id" example:"appt_123456"`
	AccountId     string                          `json:"account_id" gorm:"column:account_id" example:"acc_123456"`
	ClientId      string                          `json:"client_id" gorm:"column:client_id" example:"cli_123456"`
	AmountTip     int64                           `json:"amount_tip" gorm:"column:amount_tip" example:"10000"`
	PaymentStatus commontypes.PaymentRecordStatus `json:"payment_status" gorm:"column:payment_status" example:"1"`
}
