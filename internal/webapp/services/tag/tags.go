package tagservices

import (
	"context"
	"pebble/internal/webapp/models"
	"pebble/internal/webapp/models/sqlserver"
	tagtypes "pebble/internal/webapp/types/tag"
	"pebble/pkg/auth"
)

type tagSvc struct {
	store models.IStore
}

func TagSvc() *tagSvc {
	return &tagSvc{
		store: sqlserver.NewStore(),
	}
}

func (t *tagSvc) CreateTag(ctx context.Context, req tagtypes.CreateTagReq) (*tagtypes.Tag, error) {
	tag, err := t.store.Tags().CreateTag(auth.AuthConText(ctx), req)
	if err != nil {
		return nil, err
	}
	return tag, nil
}

func (t *tagSvc) UpdateTag(ctx context.Context, tagId string, req tagtypes.UpdateTagReq) error {
	return t.store.Tags().UpdateTag(auth.AuthConText(ctx), tagId, req)
}

func (t *tagSvc) DeleteTag(ctx context.Context, tagId string) error {
	return t.store.Tags().DeleteTag(auth.AuthConText(ctx), tagId)
}

func (t *tagSvc) QueryTags(ctx context.Context, params tagtypes.QueryTagsParams) ([]tagtypes.Tag, error) {
	return t.store.Tags().ListTags(auth.AuthConText(ctx), params)
}

func (t *tagSvc) QueryTag(ctx context.Context, tagId string) (*tagtypes.Tag, error) {
	return t.store.Tags().GetTag(auth.AuthConText(ctx), tagId)
}
