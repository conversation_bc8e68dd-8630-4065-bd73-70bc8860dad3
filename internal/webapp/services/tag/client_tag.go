package tagservices

import (
	"context"
	"pebble/internal/webapp/models"
	"pebble/internal/webapp/models/sqlserver"
	commontypes "pebble/internal/webapp/types/common"
	tagtypes "pebble/internal/webapp/types/tag"
	"pebble/pkg/auth"
	"pebble/pkg/util"
)

type clienttagSvc struct {
	store models.IStore
}

func ClientTagSvc() *clienttagSvc {
	return &clienttagSvc{
		store: sqlserver.NewStore(),
	}
}

func (c *clienttagSvc) CreateClientTag(ctx context.Context, req tagtypes.CreateClientTagReq) ([]tagtypes.ClientTag, error) {
	return c.store.ClientTags().BatchCreateClientTag(auth.AuthConText(ctx), req)
}

func (c *clienttagSvc) UpdateClientTags(ctx context.Context, clientId string, req tagtypes.UpdateClientTagReq) error {
	tags, err := TagSvc().QueryTags(ctx, tagtypes.QueryTagsParams{Type: util.Ptr(tagtypes.TagTypeClient)})
	if err != nil {
		return err
	}

	actx := auth.AuthConText(ctx)
	clientTags, err := c.store.ClientTags().GetClientTags(actx,
		tagtypes.GetClientTagCondition{ClientIds: []string{clientId}})
	if err != nil {
		return err
	}

	tagMap := make(map[string]tagtypes.Tag)
	for _, tag := range tags {
		tagMap[tag.TagId] = tag
	}
	clientTagMap := make(map[string]bool)
	for _, clientTag := range clientTags {
		clientTagMap[clientTag.TagId] = true
	}

	var deleteTagIds []string
	var addTagIds []string
	var updateTagIds []string
	for _, tagId := range req.TagIds {
		if _, ok := tagMap[tagId]; !ok {
			deleteTagIds = append(deleteTagIds, tagId)
			continue
		}
		if _, ok := clientTagMap[tagId]; !ok {
			addTagIds = append(addTagIds, tagId)
		} else {
			updateTagIds = append(updateTagIds, tagId)
			delete(clientTagMap, tagId)
		}
	}

	for tagId := range clientTagMap {
		deleteTagIds = append(deleteTagIds, tagId)
	}
	deleteTagIds = util.UniqueString(deleteTagIds)

	err = c.store.ClientTags().UpdateClientTagStatus(actx, clientId, deleteTagIds, commontypes.StatusInactive)
	if err != nil {
		return err
	}
	err = c.store.ClientTags().UpdateClientTagStatus(actx, clientId, updateTagIds, commontypes.StatusActive)
	if err != nil {
		return err
	}

	_, err = c.store.ClientTags().BatchCreateClientTag(auth.AuthConText(ctx), tagtypes.CreateClientTagReq{
		ClientId: clientId,
		TagIds:   addTagIds,
	})
	if err != nil {
		return err
	}
	return nil
}

func (c *clienttagSvc) QueryClientActiveTags(ctx context.Context, clientId string) ([]tagtypes.Tag, error) {
	tags, err := TagSvc().QueryTags(ctx, tagtypes.QueryTagsParams{Type: util.Ptr(tagtypes.TagTypeClient)})
	if err != nil {
		return nil, err
	}

	var tagIds []string
	tagMap := make(map[string]tagtypes.Tag)
	for _, tag := range tags {
		tagIds = append(tagIds, tag.TagId)
		tagMap[tag.TagId] = tag
	}

	clientTags, err := c.store.ClientTags().GetClientTags(auth.AuthConText(ctx),
		tagtypes.GetClientTagCondition{ClientIds: []string{clientId},
			Status: util.Ptr(commontypes.StatusActive), TagIds: tagIds})
	if err != nil {
		return nil, err
	}

	resp := make([]tagtypes.Tag, 0)
	for _, clientTag := range clientTags {
		resp = append(resp, tagMap[clientTag.TagId])
	}

	return resp, nil
}

func (c *clienttagSvc) QueryClientActiveTagsByClietIds(ctx context.Context, clientIds []string) (map[string][]tagtypes.Tag, error) {
	tags, err := TagSvc().QueryTags(ctx, tagtypes.QueryTagsParams{Type: util.Ptr(tagtypes.TagTypeClient)})
	if err != nil {
		return nil, err
	}

	var tagIds []string
	tagMap := make(map[string]tagtypes.Tag)
	for _, tag := range tags {
		tagIds = append(tagIds, tag.TagId)
		tagMap[tag.TagId] = tag
	}

	clientTags, err := c.store.ClientTags().GetClientTags(auth.AuthConText(ctx),
		tagtypes.GetClientTagCondition{ClientIds: clientIds,
			Status: util.Ptr(commontypes.StatusActive), TagIds: tagIds})
	if err != nil {
		return nil, err
	}

	resp := make(map[string][]tagtypes.Tag)
	for _, clientTag := range clientTags {
		resp[clientTag.ClientId] = append(resp[clientTag.ClientId], tagMap[clientTag.TagId])
	}

	return resp, nil
}
