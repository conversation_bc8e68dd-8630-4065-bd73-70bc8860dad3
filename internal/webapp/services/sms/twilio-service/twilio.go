package twilioservice

import (
	"context"
	"fmt"
	"pebble/internal/webapp/config"
	"pebble/internal/webapp/models"
	"pebble/internal/webapp/models/sqlserver"
	"pebble/internal/webapp/services/sms"
	"pebble/internal/webapp/types"
	"pebble/pkg/auth"
	"pebble/pkg/udb"
	"pebble/pkg/ulog"
	"pebble/pkg/utwilio"
	"time"

	"go.uber.org/zap"
)

type twilioProvider struct {
	store models.IStore
}

func NewTwilioProvider() *twilioProvider {

	return &twilioProvider{
		store: sqlserver.NewStore(),
	}
}

func (t *twilioProvider) Name() string {
	return "twilio"
}

func (t *twilioProvider) PurchaseNumber(ctx context.Context, req sms.PurchaseNumberReq) (*sms.PhoneNumber, error) {
	actx := auth.AuthConText(ctx)

	phone, err := t.store.PhoneConfigs().GetByTenantAndLocation(actx)
	if phone != nil && err == nil {
		return &sms.PhoneNumber{
			PhoneNumber: phone.PhoneNumber,
			Country:     phone.Region,
			Capability:  phone.Capabilities,
		}, nil
	}
	if err != nil && !udb.IsRecordNotFound(err) {
		return nil, err
	}

	twilioAccount, err := t.store.TwilioAccounts().GetByTenantAndLocation(actx)
	if err != nil && !udb.IsRecordNotFound(err) {
		return nil, err
	}
	if udb.IsRecordNotFound(err) {
		// subAccount, err := utwilio.CreataSubAccount(fmt.Sprintf("%s-%s", actx.TenantId, actx.LocationId))
		// if err != nil {
		// 	return nil, err
		// }
		// ulog.Infoln(ctx, "create twilio sub account", zap.Any("subAccount", subAccount))

		// friendlyName := fmt.Sprintf("%s-%s", actx.TenantId, actx.LocationId)
		// messageing, err := utwilio.CreateMessagingService(utwilio.CreateMessagingServiceReq{
		// 	// SID:               subAccount.SID,
		// 	// Token:             subAccount.Token,
		// 	SID:               config.C.Twilio.AccountSid,
		// 	Token:             config.C.Twilio.AuthToken,
		// 	FriendlyName:      friendlyName,
		// 	StatusCallback:    fmt.Sprintf("%s/api/twilio-callback/messageing/tenant/%v/location/%v/status", config.C.App.Domain, actx.TenantId, actx.LocationId),
		// 	InboundRequestUrl: fmt.Sprintf("%s/api/twilio-callback/messageing/tenant/%v/location/%v/inbound", config.C.App.Domain, actx.TenantId, actx.LocationId),
		// })
		// if err != nil {
		// 	return nil, err
		// }

		twilioAccount, err = t.store.TwilioAccounts().Create(actx, types.TwilioAccountEntity{
			FriendlyName:        "Default",
			TwilioSID:           config.C.Twilio.AccountSid,
			AuthToken:           config.C.Twilio.AuthToken,
			Status:              types.TwilioAccountStatusActive,
			MessagingServiceSID: config.C.Twilio.MessagingServiceSID,
		})
		if err != nil {
			return nil, err
		}
	}

	// location, err := t.store.Locations().GetLocation(actx)
	// if err != nil {
	// 	return nil, err
	// }
	// numbers, err := utwilio.SearchAvailabeNumbers(utwilio.SearchAvailabeNumbersReq{
	// 	SubSID:   twilioAccount.TwilioSID,
	// 	SubToken: twilioAccount.AuthToken,
	// 	Country:  location.Country,
	// 	// AreaCode: 0, // todo
	// })
	// numbers, err := t.GetAvailabeNumbers(ctx)
	// if err != nil {
	// 	return nil, err
	// }
	// if len(numbers) == 0 {
	// 	return nil, errors.New("no available numbers")
	// }

	// number := numbers[0]
	twilioNumber, err := utwilio.BuyNumber(utwilio.BuyNumberReq{
		SID:         twilioAccount.TwilioSID,
		Token:       twilioAccount.AuthToken,
		PhoneNumber: req.PhoneNumber,
		// SmsWebhookUrl:   fmt.Sprintf("%s/api/twilio-callback/sms/tenant/%v/location/%v/status", config.C.App.Domain, actx.TenantId, actx.LocationId),
		VoiceWebhookUrl: fmt.Sprintf("https://%s/api/twilio-callback/voice/tenant/%v/location/%v", config.C.App.Domain, actx.TenantId, actx.LocationId),
	})
	if err != nil {
		return nil, err
	}
	defer func() {
		if err == nil {
			return
		}
		err = utwilio.DeleteNumber(twilioAccount.TwilioSID, twilioAccount.AuthToken, twilioNumber.Sid)
		if err != nil {
			ulog.Error(ctx, "delete twilio number error", zap.Any("number", twilioNumber), zap.Error(err))
		}
	}()

	err = utwilio.AddNumberToMessagingService(twilioAccount.TwilioSID, twilioAccount.AuthToken, twilioAccount.MessagingServiceSID, twilioNumber.Sid)
	if err != nil {
		return nil, err
	}

	capabilities := make([]types.PhoneCapability, 0, 2)
	if twilioNumber.SmsEnabled {
		capabilities = append(capabilities, types.PhoneCapabilitySMS)
	}
	if twilioNumber.VoiceEnabled {
		capabilities = append(capabilities, types.PhoneCapabilityVoice)
	}
	// number, err := utwilio.GetNumber(twilioAccount.TwilioSID, twilioAccount.AuthToken, twilioNumber.Sid)

	_, err = t.store.PhoneConfigs().Create(actx, types.PhoneConfigEntity{
		PhoneNumber:      twilioNumber.PhoneNumber,
		Region:           req.Country,
		ProviderPhoneSID: twilioNumber.Sid,
		Provider:         t.Name(),
		Capabilities:     capabilities,
		BindTime:         time.Now().UTC(),
		ExpireTime:       time.Now().UTC().AddDate(10, 0, 0),
		Status:           types.PhoneStatusActive,
	})
	if err != nil {
		return nil, err
	}

	return &sms.PhoneNumber{
		PhoneNumber: twilioNumber.PhoneNumber,
		Capability:  capabilities,
		Country:     req.Country,
	}, err
}

func (t *twilioProvider) ReleaseNumber(ctx context.Context, number string) error {
	actx := auth.AuthConText(ctx)
	phone, err := t.store.PhoneConfigs().GetByPhoneNumber(actx, number)
	if err != nil {
		return err
	}

	twilioAccount, err := t.store.TwilioAccounts().GetByTenantAndLocation(actx)
	if err != nil {
		return err
	}

	err = utwilio.DeleteNumber(twilioAccount.TwilioSID, twilioAccount.AuthToken, phone.ProviderPhoneSID)
	if err != nil {
		return err
	}

	return t.store.PhoneConfigs().Delete(actx)
}

func (t *twilioProvider) SearchAvailabeNumbers(ctx context.Context, req sms.SearchAvailabeNumbersReq) ([]sms.PhoneNumber, error) {
	// actx := auth.AuthConText(ctx)
	// location, err := t.store.Locations().GetLocation(actx)
	// if err != nil {
	// 	return nil, err
	// }

	// twilioAccount, err := t.store.TwilioAccounts().GetByTenantAndLocation(actx)
	// if err != nil && !udb.IsRecordNotFound(err) {
	// 	return nil, err
	// }
	// if udb.IsRecordNotFound(err) {

	// }

	numbers, err := utwilio.SearchAvailabeNumbers(utwilio.SearchAvailabeNumbersReq{
		SID:      config.C.Twilio.AccountSid,
		Token:    config.C.Twilio.AuthToken,
		Country:  req.Country,
		AreaCode: req.AreaCode,
	})
	if err != nil {
		return nil, err
	}

	list := make([]sms.PhoneNumber, 0, len(numbers)+1)
	for _, number := range numbers {
		capabilities := make([]types.PhoneCapability, 0, 2)
		if number.SmsEnabled {
			capabilities = append(capabilities, types.PhoneCapabilitySMS)
		}
		if number.VoiceEnabled {
			capabilities = append(capabilities, types.PhoneCapabilityVoice)
		}
		list = append(list, sms.PhoneNumber{
			PhoneNumber: number.PhoneNumber,
			Capability:  capabilities,
			Country:     number.IsoCountry,
		})
	}

	return list, nil
}

func (t *twilioProvider) GetNumberInfo(ctx context.Context, number string) (*sms.PhoneNumber, error) {
	actx := auth.AuthConText(ctx)
	twilioAccount, err := t.store.TwilioAccounts().GetByTenantAndLocation(actx)
	if err != nil {
		return nil, err
	}

	phone, err := t.store.PhoneConfigs().GetByTenantAndLocation(actx)
	if err != nil {
		return nil, err
	}

	twilioNumber, err := utwilio.GetNumber(twilioAccount.TwilioSID, twilioAccount.AuthToken, phone.ProviderPhoneSID)
	if err != nil {
		return nil, err
	}

	capabilities := make([]types.PhoneCapability, 0, 2)
	if twilioNumber.SmsEnabled {
		capabilities = append(capabilities, types.PhoneCapabilitySMS)
	}
	if twilioNumber.VoiceEnabled {
		capabilities = append(capabilities, types.PhoneCapabilityVoice)
	}

	return &sms.PhoneNumber{
		PhoneNumber: number,
		Capability:  capabilities,
	}, nil
}
func (t *twilioProvider) SendMessage(ctx context.Context, msg sms.SendMessageParams) (*sms.SendMessageResult, error) {
	actx := auth.AuthConText(ctx)
	twilioAccount, err := t.store.TwilioAccounts().GetByTenantAndLocation(actx)
	if err != nil {
		return nil, err
	}

	phone, err := t.store.PhoneConfigs().GetByTenantAndLocation(actx)
	if err != nil {
		return nil, err
	}

	resp, err := utwilio.SendMessage(utwilio.SendMessageReq{
		Sid:                 twilioAccount.TwilioSID,
		Token:               twilioAccount.AuthToken,
		From:                phone.PhoneNumber,
		To:                  msg.To,
		Content:             msg.Content,
		MessagingServiceSid: twilioAccount.MessagingServiceSID,
		StatusCallback:      fmt.Sprintf("https://%s/api/twilio-callback/sms/tenant/%v/location/%v/status", config.C.App.Domain, actx.TenantId, actx.LocationId),
	})
	if err != nil {
		return nil, err
	}

	smsMsg, err := t.store.SMSMessages().Create(actx, types.SMSMessageEntity{
		ClientID:       msg.ClientId,
		PhoneNumber:    phone.PhoneNumber,
		ToNumber:       msg.To,
		Provider:       t.Name(),
		MsgType:        types.SMSMessageTypeNotification,
		Content:        msg.Content,
		TemplateID:     msg.TemplateID,
		TemplateParams: msg.TemplateParams,
		Status:         types.SMSMessageStatusSending,
		ProviderMsgID:  resp.MessageSid,
	})
	if err != nil {
		return nil, err
	}

	_, err = t.store.MessageRecords().Create(actx, types.MessageRecordEntity{
		MessageID:    smsMsg.MessageID,
		ChannelType:  types.MessageChannelTypeSMS,
		ClientID:     msg.ClientId,
		MsgDirection: types.MessageDirectionSend,
		SendTime:     time.Now().Unix(),
	})

	return &sms.SendMessageResult{
		MessageID: smsMsg.MessageID,
	}, err
}

func (t *twilioProvider) GetMessage(ctx context.Context, messageID string) (*sms.Message, error) {
	actx := auth.AuthConText(ctx)
	twilioAccount, err := t.store.TwilioAccounts().GetByTenantAndLocation(actx)
	if err != nil {
		return nil, err
	}

	msg, err := t.store.SMSMessages().GetByMessageID(actx, messageID)
	if err != nil {
		return nil, err
	}

	twilioMsg, err := utwilio.QueryMessage(twilioAccount.TwilioSID, twilioAccount.AuthToken, msg.ProviderMsgID)
	if err != nil {
		return nil, err
	}

	return &sms.Message{
		From:    twilioMsg.From,
		To:      twilioMsg.To,
		Content: twilioMsg.Content,
		Status:  twilioMsg.Status,
	}, nil
}

func (t *twilioProvider) StatusCallBack(ctx context.Context, data types.TwilioSendStatusCallback) error {
	actx := auth.AuthConText(ctx)
	msg, err := t.store.SMSMessages().GetByProviderMsgID(actx, data.MessageSid)
	if err != nil {
		ulog.Errorln(ctx, "get sms message err", err)
		return err
	}
	if msg.Status == types.SMSMessageStatusDelivered {
		return nil
	}

	status := convertStatus(data.MessageStatus)
	err = t.store.SMSMessages().UpdateStatus(actx, msg.MessageID, types.UpdateSMSStatusReq{
		PhoneNumber: &data.From,
		Status:      &status,
		ErrorCode:   &data.ErrorCode,
		ErrorMsg:    &data.ErrorMessage,
	})
	if err != nil {
		ulog.Errorln(ctx, "update sms message status err", err)
	}

	return err
}

func (t *twilioProvider) InboundWebhook(ctx context.Context, data types.TwilioSMSInboundWebhook) error {
	actx := auth.AuthConText(ctx)

	client, err := t.store.Clients().GetClientByPhoneNumber(actx, data.From)
	if err != nil {
		ulog.Errorln(ctx, "get client by phone number err", err)
		return err
	}

	msgType := types.SMSMessageTypeNotification
	status := types.SMSMessageStatusDelivered
	msg, err := t.store.SMSMessages().Create(actx, types.SMSMessageEntity{
		ClientID:      client.ClientId,
		PhoneNumber:   data.From,
		ToNumber:      data.To,
		Provider:      t.Name(),
		MsgType:       msgType,
		Content:       data.Body,
		Status:        status,
		ProviderMsgID: data.MessageSid,
	})
	if err != nil {
		ulog.Errorln(ctx, "create sms message err", err)
		return err
	}

	_, err = t.store.MessageRecords().Create(actx, types.MessageRecordEntity{
		MessageID:    msg.MessageID,
		ChannelType:  types.MessageChannelTypeSMS,
		ClientID:     client.ClientId,
		MsgDirection: types.MessageDirectionReceived,
		SendTime:     time.Now().Unix(),
	})
	if err != nil {
		ulog.Errorln(ctx, "create message record err", err)
	}

	return err
}

func convertStatus(status string) types.SMSMessageStatus {
	twilioStatusMap := map[string]types.SMSMessageStatus{
		"queued":      types.SMSMessageStatusPending,
		"sending":     types.SMSMessageStatusSending,
		"delivered":   types.SMSMessageStatusDelivered,
		"failed":      types.SMSMessageStatusFailed,
		"undelivered": types.SMSMessageStatusFailed,
		"rejected":    types.SMSMessageStatusRejected,
	}
	return twilioStatusMap[status]
}
