package services

import (
	"context"
	"pebble/internal/webapp/models"
	"pebble/internal/webapp/models/sqlserver"
	"pebble/internal/webapp/types"
	"pebble/pkg/auth"
	"pebble/pkg/udb"
	"pebble/pkg/ulog"
)

type locationSvc struct {
	store models.IStore
}

func LocationSvc() *locationSvc {
	return &locationSvc{
		store: sqlserver.NewStore(),
	}
}

func (l *locationSvc) QueryLocation(ctx context.Context) (*types.Location, error) {
	loaction, err := l.store.Locations().GetLocation(auth.AuthConText(ctx))
	if err != nil {
		ulog.Errorln(ctx, "query location error:", err)
		return nil, err
	}
	return loaction, nil
}

func (l *locationSvc) UpdateLocation(ctx context.Context, data types.UpdateLocationReq) error {
	err := l.store.Locations().UpdateLocation(auth.AuthConText(ctx), data)
	if err != nil {
		ulog.Errorln(ctx, "update location error:", err)
		return err
	}
	return nil
}

func (l *locationSvc) GetBingPhoneNumber(ctx context.Context) (*types.PhoneConfig, error) {
	actx := auth.AuthConText(ctx)
	phoneConfig, err := l.store.PhoneConfigs().GetByTenantAndLocation(actx)
	if err != nil && !udb.IsRecordNotFound(err) {
		ulog.Errorln(ctx, "get phone config error:", err)
		return nil, err
	}
	if udb.IsRecordNotFound(err) {
		return &types.PhoneConfig{
			PhoneConfigEntity: types.PhoneConfigEntity{
				TenantID:   actx.TenantId,
				LocationID: actx.LocationId,
			},
		}, nil
	}

	return phoneConfig, nil
}
