package services

import (
	"context"
	"pebble/internal/webapp/models"
	"pebble/internal/webapp/models/sqlserver"
	"pebble/internal/webapp/types"
	"pebble/pkg/auth"
)

type permissionSvc struct {
	store models.IStore
}

func PermissionSvc() *permissionSvc {
	return &permissionSvc{
		store: sqlserver.NewStore(),
	}
}

func (p *permissionSvc) QueryPermissions(ctx context.Context, params types.QueryPermissionsParams) ([]types.Permission, error) {
	return p.store.Permissions().QueryPermissions(auth.AuthConText(ctx), params)
}

func (p *permissionSvc) QueryPermission(ctx context.Context, permissionId string) (*types.Permission, error) {
	return p.store.Permissions().GetPermission(auth.AuthConText(ctx), permissionId)
}

func (p *permissionSvc) CreatePermission(ctx context.Context, req types.CreatePermissionReq) (*types.Permission, error) {
	return p.store.Permissions().CreatePermission(auth.AuthConText(ctx), req)
}

func (p *permissionSvc) UpdatePermission(ctx context.Context, permissionId string, req types.UpdatePermissionReq) error {
	return p.store.Permissions().UpdatePermission(auth.AuthConText(ctx), permissionId, req)
}

func (p *permissionSvc) DeletePermission(ctx context.Context, permissionId string) error {
	return p.store.Permissions().DeletePermission(auth.AuthConText(ctx), permissionId)
}
