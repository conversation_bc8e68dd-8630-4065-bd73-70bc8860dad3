package services

import (
	"context"
	"pebble/internal/webapp/models"
	"pebble/internal/webapp/models/sqlserver"
	"pebble/internal/webapp/types"
	"pebble/pkg/auth"
	"pebble/pkg/ulog"
)

type taxSvc struct {
	store models.IStore
}

func TaxSvc() *taxSvc {
	return &taxSvc{
		store: sqlserver.NewStore(),
	}
}

func (s *taxSvc) Create(ctx context.Context, req types.CreateTaxReq) (*types.Tax, error) {
	actx := auth.AuthConText(ctx)
	tax, err := s.store.Taxes().Create(actx, types.TaxEntity{
		Name: req.Name,
		Rate: req.Rate,
	})
	if err != nil {
		ulog.Errorln(ctx, "create tax err", err)
		return nil, err
	}
	return tax, nil
}

func (s *taxSvc) Update(ctx context.Context, id string, req types.UpdateTaxReq) error {
	actx := auth.AuthConText(ctx)
	err := s.store.Taxes().Update(actx, id, req)
	if err != nil {
		ulog.Errorln(ctx, "update tax err", err)
		return err
	}
	return nil
}

func (s *taxSvc) Delete(ctx context.Context, id string) error {
	actx := auth.AuthConText(ctx)
	err := s.store.Taxes().Delete(actx, id)
	if err != nil {
		ulog.Errorln(ctx, "delete tax err", err)
		return err
	}
	return nil
}

func (s *taxSvc) GetTaxById(ctx context.Context, id string) (*types.Tax, error) {
	actx := auth.AuthConText(ctx)
	tax, err := s.store.Taxes().Get(actx, id)
	if err != nil {
		ulog.Errorln(ctx, "get tax err", err)
		return nil, err
	}
	return tax, nil
}

func (s *taxSvc) List(ctx context.Context) ([]*types.Tax, error) {
	actx := auth.AuthConText(ctx)
	taxes, err := s.store.Taxes().List(actx)
	if err != nil {
		ulog.Errorln(ctx, "list tax err", err)
		return nil, err
	}
	return taxes, nil
}
