package services

import (
	"context"
	"pebble/internal/webapp/models"
	"pebble/internal/webapp/models/sqlserver"
	"pebble/internal/webapp/services/sms"
	twilioservice "pebble/internal/webapp/services/sms/twilio-service"
	"pebble/internal/webapp/types"
	"pebble/pkg/auth"
)

type msgSvc struct {
	provider sms.IProvider
	store    models.IStore
}

func MsgSvc() *msgSvc {
	return &msgSvc{
		provider: twilioservice.NewTwilioProvider(),
		store:    sqlserver.NewStore(),
	}
}

func (s *msgSvc) PurchaseNumber(ctx context.Context, req sms.PurchaseNumberReq) (*sms.PhoneNumber, error) {
	return s.provider.PurchaseNumber(ctx, req)
}

func (s *msgSvc) ReleaseNumber(ctx context.Context) error {
	actx := auth.AuthConText(ctx)
	number, err := s.store.PhoneConfigs().GetByTenantAndLocation(actx)
	if err != nil {
		return err
	}

	return s.provider.ReleaseNumber(ctx, number.PhoneNumber)
}

func (s *msgSvc) GetNumberInfo(ctx context.Context, numberID string) (*sms.PhoneNumber, error) {
	return s.provider.GetNumberInfo(ctx, numberID)
}

func (s *msgSvc) SendMessage(ctx context.Context, msg sms.SendMessageParams) error {
	_, err := s.provider.SendMessage(ctx, msg)
	if err != nil {
		return err
	}

	return err
}

func (s *msgSvc) GetMessage(ctx context.Context, messageID string) (*sms.Message, error) {
	return s.provider.GetMessage(ctx, messageID)
}

func (s *msgSvc) SearchAvailabeNumbers(ctx context.Context, req sms.SearchAvailabeNumbersReq) ([]sms.PhoneNumber, error) {
	return s.provider.SearchAvailabeNumbers(ctx, req)
}

func (s *msgSvc) QueryMessagesGroupedByClient(ctx context.Context, params types.QueryMessageParams) ([]types.QueryMessageGroupedByClientResp, error) {
	actx := auth.AuthConText(ctx)
	msgList, err := s.store.MessageRecords().GetMessagesGroupedByClient(actx, params)
	if err != nil {
		return nil, err
	}

	// emailMsgIds := make([]string, 0, len(msgList))
	// phoneMsgIds := make([]string, 0, len(msgList))
	smgMsgIds := make([]string, 0, len(msgList))
	clientIds := make([]string, 0, len(msgList))
	for _, msg := range msgList {
		clientIds = append(clientIds, msg.ClientID)

		if msg.ChannelType == types.MessageChannelTypeSMS {
			smgMsgIds = append(smgMsgIds, msg.MessageID)
		}
		// if msg.ChannelType == types.MessageChannelTypeEmail {
		// 	emailMsgIds = append(emailMsgIds, msg.MessageID)
		// }
		// if msg.ChannelType == types.MessageChannelTypePhone {
		// 	phoneMsgIds = append(phoneMsgIds, msg.MessageID)
		// }
	}

	clients, err := s.store.Clients().GetClientByIds(actx, clientIds)
	if err != nil {
		return nil, err
	}
	clientsMap := make(map[string]types.Client)
	for _, client := range clients {
		clientsMap[client.ClientId] = client
	}

	// only support sms
	smsMsgs, err := s.store.SMSMessages().GetByMessageIds(actx, smgMsgIds)
	if err != nil {
		return nil, err
	}
	smsMsgMap := make(map[string]types.SMSMessage)
	for _, msg := range smsMsgs {
		smsMsgMap[msg.MessageID] = msg
	}

	resp := make([]types.QueryMessageGroupedByClientResp, 0, len(msgList))
	for _, msg := range msgList {
		resp = append(resp, types.QueryMessageGroupedByClientResp{
			MessageRecord:   msg,
			ClientFirstName: clientsMap[msg.ClientID].FirstName,
			ClientLastName:  clientsMap[msg.ClientID].LastName,
			MessageContent:  smsMsgMap[msg.MessageID].Content,
		})
	}

	return resp, nil
}

func (s *msgSvc) QueryClientMessages(ctx context.Context, clientId string, params types.QueryClientMessagesParams) ([]types.MessageRecordDetail, error) {
	actx := auth.AuthConText(ctx)
	msgList, err := s.store.MessageRecords().GetByClientID(actx, clientId)
	if err != nil {
		return nil, err
	}

	// emailMsgIds := make([]string, 0, len(msgList))
	// phoneMsgIds := make([]string, 0, len(msgList))
	smgMsgIds := make([]string, 0, len(msgList))
	clientIds := make([]string, 0, len(msgList))
	for _, msg := range msgList {
		clientIds = append(clientIds, msg.ClientID)

		if msg.ChannelType == types.MessageChannelTypeSMS {
			smgMsgIds = append(smgMsgIds, msg.MessageID)
		}
		// if msg.ChannelType == types.MessageChannelTypeEmail {
		// 	emailMsgIds = append(emailMsgIds, msg.MessageID)
		// }
		// if msg.ChannelType == types.MessageChannelTypePhone {
		// 	phoneMsgIds = append(phoneMsgIds, msg.MessageID)
		// }
	}

	clients, err := s.store.Clients().GetClientByIds(actx, clientIds)
	if err != nil {
		return nil, err
	}
	clientsMap := make(map[string]types.Client)
	for _, client := range clients {
		clientsMap[client.ClientId] = client
	}

	// only support sms
	smsMsgs, err := s.store.SMSMessages().GetByMessageIds(actx, smgMsgIds)
	if err != nil {
		return nil, err
	}
	smsMsgMap := make(map[string]types.SMSMessage)
	for _, msg := range smsMsgs {
		smsMsgMap[msg.MessageID] = msg
	}

	resp := make([]types.MessageRecordDetail, 0, len(msgList))
	for _, msg := range msgList {
		item := types.MessageRecordDetail{
			MessageRecord: msg,
		}
		if msg.ChannelType == types.MessageChannelTypeSMS {
			smsMsg, ok := smsMsgMap[msg.MessageID]
			if ok {
				item.SmsMessage = &smsMsg
			}
		}

		resp = append(resp, item)
	}

	return resp, nil
}
