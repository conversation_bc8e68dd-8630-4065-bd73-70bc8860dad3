package message

import "pebble/internal/webapp/types/message"

// MultilingualNavigationData stores navigation data for different languages
type MultilingualNavigationData struct {
	Categories map[string][]message.NavigationCategory
}

// GetNavigationForLanguage returns navigation data for a specific language
func (m *MultilingualNavigationData) GetNavigationForLanguage(language string) []message.NavigationCategory {
	if nav, exists := m.Categories[language]; exists {
		return nav
	}
	// Fallback to English if requested language is not available
	return m.Categories["en-US"]
}

// GetDefaultNavigationData returns the default multilingual navigation data
func GetDefaultNavigationData() *MultilingualNavigationData {
	return &MultilingualNavigationData{
		Categories: map[string][]message.NavigationCategory{
			"en-US": {
				{
					Category: "Appointment Management",
					Items: []message.NavigationEvent{
						{Name: "Appointment Reminder", TemplateType: message.AppointmentReminder},
						{Name: "Rebook Appointment", TemplateType: message.RebookAppointment},
					},
				},
			},
			"zh-CN": {
				{
					Category: "预约管理",
					Items: []message.NavigationEvent{
						{Name: "预约提醒", TemplateType: message.AppointmentReminder},
						{Name: "重新预约", TemplateType: message.RebookAppointment},
					},
				},
			},
		},
	}
}
