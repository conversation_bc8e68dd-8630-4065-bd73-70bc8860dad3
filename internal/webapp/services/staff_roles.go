package services

import (
	"context"
	"errors"
	"pebble/internal/webapp/models"
	"pebble/internal/webapp/models/sqlserver"
	"pebble/internal/webapp/types"
	"pebble/pkg/auth"
)

type staffRoleSvc struct {
	store models.IStore
}

func StaffRoleSvc() *staffRoleSvc {
	return &staffRoleSvc{
		store: sqlserver.NewStore(),
	}
}

func (s *staffRoleSvc) BindStaffRole(ctx context.Context, req types.BindStaffRoleReq) error {
	actx := auth.AuthConText(ctx)
	memberRoles, err := s.store.TenantMemberRoles().GetTenantMemberRoles(actx, req.AccountId)
	if err != nil {
		return err
	}
	roleMap := make(map[string]bool, len(memberRoles))
	roleIds := make([]string, 0, len(memberRoles))
	for _, memberRole := range memberRoles {
		roleIds = append(roleIds, memberRole.RoleId)
		roleMap[memberRole.RoleId] = true
	}
	roles, err := s.store.Roles().GetRoleByIds(actx, roleIds)
	if err != nil {
		return err
	}
	if len(roles) != len(roleIds) {
		return errors.New("role not found")
	}

	var addRoleIds []string
	var deleteRoleIds []string
	for _, roleId := range req.RoleIds {
		if !roleMap[roleId] {
			addRoleIds = append(addRoleIds, roleId)
			delete(roleMap, roleId)
		}
	}

	for roleId := range roleMap {
		deleteRoleIds = append(deleteRoleIds, roleId)
	}

	err = s.store.TenantMemberRoles().BatchDeleteTenantMemberRoles(auth.AuthConText(ctx), types.DeleteTenantMemberRolesReq{
		AccountId: req.AccountId,
		RoleIds:   deleteRoleIds,
	})
	if err != nil {
		return err
	}

	_, err = s.store.TenantMemberRoles().BatchCreateTenantMemberRoles(auth.AuthConText(ctx), types.CreateTenantMemberRolesReq{
		AccountId: req.AccountId,
		RoleIds:   addRoleIds,
	})
	if err != nil {
		return err
	}

	return nil
}
