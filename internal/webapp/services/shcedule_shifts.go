package services

import (
	"context"
	"pebble/internal/webapp/models"
	"pebble/internal/webapp/models/sqlserver"
	"pebble/internal/webapp/types"
	appointmenttypes "pebble/internal/webapp/types/appointment"
	commontypes "pebble/internal/webapp/types/common"
	"pebble/pkg/auth"
	"pebble/pkg/ulog"
	"pebble/pkg/util"
	"pebble/pkg/utime"
	"sort"
)

type scheduleShiftsSvc struct {
	store models.IStore
}

func ScheduleShiftsSvc() *scheduleShiftsSvc {
	return &scheduleShiftsSvc{
		store: sqlserver.NewStore(),
	}
}

func (s *scheduleShiftsSvc) QueryAvailableScheduleShifts(ctx context.Context, params types.QueryAvailableScheduleShiftsParams) ([]types.QueryAvailableScheduleShiftsReps, error) {
	// return s.store.QueryAvailableScheduleShifts(ctx, params)
	actx := auth.AuthConText(ctx)
	appointments, _, err := s.store.Appointments().List(actx, appointmenttypes.GetAppointmentConditions{
		ScheduledStartDate: &params.Date,
		ScheduledEndDate:   &params.Date,
		StatusList: []commontypes.AppointmentStatusType{
			commontypes.AppointmentStatusUnconfirm,
			commontypes.AppointmentStatusConfirm,
			commontypes.AppointmentStatusWaitListen,
			commontypes.AppointmentStatusArrived,
			commontypes.AppointmentStatusInService,
		},
	})
	if err != nil {
		return nil, err
	}
	appointmentIds := make([]string, 0, len(appointments))
	for _, appointment := range appointments {
		appointmentIds = append(appointmentIds, appointment.AppointmentId)
	}
	allServices, err := s.store.AppointmentServices().GetByAppointmentIds(actx, appointmentIds)
	if err != nil {
		return nil, err
	}
	serviceMap := make(map[string][]appointmenttypes.AppointmentService)
	for _, service := range allServices {
		serviceMap[service.AppointmentId] = append(serviceMap[service.AppointmentId], service)
	}

	members, err := s.store.TenantMembers().GetTenantMembers(actx)
	if err != nil {
		return nil, err
	}

	var accountIds []string
	for _, member := range members {
		accountIds = append(accountIds, member.AccountId)
	}

	schedules, err := StaffScheduleSvc().QueryMultiStaffSchedulesDate(ctx, types.QueryMultiStaffSchedulesDateParams{
		AccountIds: accountIds,
		StartDate:  &params.Date,
		EndDate:    &params.Date,
	})
	if err != nil {
		return nil, err
	}
	schedulesMap := make(map[string]types.QueryMultiStaffSchedulesDateResp)
	for _, v := range schedules {
		schedulesMap[v.AccountID] = v
	}

	// timeStaffMap := make(map[int64][]string)
	resp := make([]types.QueryAvailableScheduleShiftsReps, 0, 48)
	var startTime int64 = 7 * 60 * 60
	var endTime int64 = 22*60*60 + 30*60
	for t := startTime; t <= endTime; t += 15 * 60 {
		// timeStaffMap[i] = make([]string, 0)

		var staffList []string
		var needAssignApptNum int64
		for _, appt := range appointments {
			var needAssign bool
			if appt.ScheduledStartTime <= t && appt.ScheduledEndTime > t {
				services, ok := serviceMap[appt.AppointmentId]
				if !ok {
					continue
				}

				for _, service := range services {
					if service.AccountId != "" {
						staffList = append(staffList, service.AccountId)
					} else {
						needAssign = true
					}
				}
				if needAssign {
					if appt.ClientCount > 1 {
						needAssignApptNum += appt.ClientCount
					} else {
						needAssignApptNum++
					}
				}
			}

		}

		var notWorkingStaffNum int64
		for _, schedules := range schedulesMap {
			if util.InSlice(staffList, schedules.AccountID) {
				continue
			}
			if schedules.IsDayOff || len(schedules.WorkingTimes) == 0 {
				notWorkingStaffNum++
				continue
			}

			if len(schedules.WorkingTimes) > 0 {
				workingFlag := false
				for _, workingTime := range schedules.WorkingTimes {
					if workingTime.StartTime <= t && workingTime.EndTime >= t {
						workingFlag = true
					}
				}
				if !workingFlag {
					notWorkingStaffNum++
				}
			}
		}

		staffList = util.UniqueString(staffList)
		resp = append(resp, types.QueryAvailableScheduleShiftsReps{
			Date:              params.Date,
			Time:              t,
			AvailableStaffNum: int64(len(members)-len(staffList)) - needAssignApptNum - notWorkingStaffNum,
			StaffNum:          int64(len(members)),
		})
	}

	return resp, nil
}
func (s *scheduleShiftsSvc) QueryStaffScheduleShifts(ctx context.Context, params types.QueryStaffScheduleShiftsParams) ([]types.QueryStaffScheduleShiftsReps, error) {
	actx := auth.AuthConText(ctx)
	appointments, err := s.store.Appointments().ListByDate(actx, params.Date)
	if err != nil {
		return nil, err
	}
	appointmentIds := make([]string, 0, len(appointments))
	apptMap := make(map[string]appointmenttypes.Appointment)
	for _, appointment := range appointments {
		appointmentIds = append(appointmentIds, appointment.AppointmentId)
		apptMap[appointment.AppointmentId] = appointment
	}
	allServices, err := s.store.AppointmentServices().GetByAppointmentIds(actx, appointmentIds)
	if err != nil {
		return nil, err
	}

	busyStaffMap := make(map[string]bool)
	availableStaffActualEndMap := make(map[string]int64)
	for _, service := range allServices {
		appointment := apptMap[service.AppointmentId]

		needToDoAppt := !util.InSlice([]commontypes.AppointmentStatusType{
			commontypes.AppointmentStatusCompleted,
			commontypes.AppointmentStatusCancelled,
			commontypes.AppointmentStatusNoShow,
		}, appointment.Status)

		inRange := appointment.ScheduledStartDate <= params.Date &&
			appointment.ScheduledEndDate >= params.Date &&
			appointment.ScheduledStartTime <= params.Time &&
			appointment.ScheduledEndTime > params.Time

		cannotTodo := appointment.ScheduledStartDate <= params.Date &&
			appointment.ScheduledEndDate >= params.Date &&
			appointment.ScheduledStartTime >= params.Time &&
			appointment.ScheduledStartTime < params.Time+params.Duration

		if needToDoAppt && (inRange || cannotTodo) {

			busyStaffMap[service.AccountId] = true
		} else {
			actualEndTime, ok := availableStaffActualEndMap[service.AccountId]
			if !ok || appointment.ActualEndTime > actualEndTime {
				availableStaffActualEndMap[service.AccountId] = appointment.ActualEndTime
			}
		}
	}

	members, err := s.store.TenantMembers().GetTenantMembers(actx)
	if err != nil {
		ulog.Errorln(ctx, "get tenant members err", err)
		return nil, err
	}

	accoutIds := make([]string, 0, len(members))
	for _, member := range members {
		accoutIds = append(accoutIds, member.AccountId)
	}

	schedules, err := StaffScheduleSvc().QueryMultiStaffSchedulesDate(ctx, types.QueryMultiStaffSchedulesDateParams{
		AccountIds: accoutIds,
		StartDate:  &params.Date,
		EndDate:    &params.Date,
	})
	if err != nil {
		return nil, err
	}
	schedulesMap := make(map[string]types.QueryMultiStaffSchedulesDateResp)
	for _, v := range schedules {
		schedulesMap[v.AccountID] = v
	}
	settings, err := StaffScheduleSvc().QueryStaffSchedulesSettings(ctx)
	if err != nil {
		return nil, err
	}

	num, err := utime.DateDiffInDays(params.Date, settings.StartDate)
	if err != nil {
		ulog.Errorln(ctx, "date diff err", err)
		return nil, err
	}

	available := make([]types.QueryStaffScheduleShiftsReps, 0, len(members))
	busy := make([]types.QueryStaffScheduleShiftsReps, 0, len(members))
	index := num % len(members)
	for i := 0; i < len(members); i++ {
		item := types.QueryStaffScheduleShiftsReps{
			AccountId:  members[index].AccountId,
			TenantId:   members[index].TenantId,
			LocationId: members[index].LocationId,
			FirstName:  members[index].FirstName,
			LastName:   members[index].LastName,
			Available:  false,
		}

		schedules := schedulesMap[members[index].AccountId]
		if !schedules.IsDayOff && len(schedules.WorkingTimes) > 0 {
			for _, workingTime := range schedules.WorkingTimes {
				if workingTime.StartTime <= params.Time && workingTime.EndTime >= params.Time {
					item.Available = true
					break
				}
			}
		}

		if busyStaffMap[members[index].AccountId] {
			item.Available = false
		}

		if item.Available {
			available = append(available, item)
		} else {
			busy = append(busy, item)
		}
		index = (index + 1) % len(members)
	}

	sort.Slice(available, func(i, j int) bool {
		return availableStaffActualEndMap[available[i].AccountId] < availableStaffActualEndMap[available[j].AccountId]
	})

	resp := make([]types.QueryStaffScheduleShiftsReps, 0, len(members))
	resp = append(resp, available...)
	resp = append(resp, busy...)

	return resp, nil
}
