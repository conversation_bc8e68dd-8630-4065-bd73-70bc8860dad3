package services

import (
	"context"
	"pebble/internal/webapp/models"
	"pebble/internal/webapp/models/sqlserver"
	"pebble/internal/webapp/types"
	"pebble/pkg/auth"
	"pebble/pkg/util"
)

type roleSvc struct {
	store models.IStore
}

func RoleSvc() *roleSvc {
	return &roleSvc{
		store: sqlserver.NewStore(),
	}
}

func (r *roleSvc) CreateRole(ctx context.Context, req types.CreateRoleDetailReq) (*types.RoleDetails, error) {
	actx := auth.AuthConText(ctx)
	role, err := r.store.Roles().CreateRole(actx, types.CreateRoleReq{Name: req.Name})
	if err != nil {
		return nil, err
	}

	rolePermission, err := r.store.RolePermissions().BatchCreateRolePermissions(auth.AuthConText(ctx), types.BatchCreateRolePermissionReq{
		RoleId:        role.RoleId,
		PermissionIds: req.PermissionIds,
	})
	if err != nil {
		return nil, err
	}

	var permissionIds []string
	for _, permission := range rolePermission {
		permissionIds = append(permissionIds, permission.PermissionId)
	}
	permissions, err := r.store.Permissions().GetPermissionByIds(actx, permissionIds)
	if err != nil {
		return nil, err
	}
	var resp types.RoleDetails
	resp.RoleEntity = role.RoleEntity
	resp.Permissions = types.PermissionToEntity(permissions)

	return &resp, nil
}

func (r *roleSvc) UpdateRole(ctx context.Context, roleId string, req types.UpdateRoleDetailReq) error {
	_, err := r.store.Roles().QueryRole(auth.AuthConText(ctx), roleId)
	if err != nil {
		return err
	}

	err = r.store.Roles().UpdateRole(auth.AuthConText(ctx), roleId, req.UpdateRoleReq)
	if err != nil {
		return err
	}

	rolePermissions, err := r.store.RolePermissions().QueryRolePermissions(auth.AuthConText(ctx), roleId)
	if err != nil {
		return err
	}
	rolePermissionsMap := make(map[string]types.RolePermission)
	for _, rolePermission := range rolePermissions {
		rolePermissionsMap[rolePermission.PermissionId] = rolePermission
	}

	addPermissions := []string{}
	deletePermissions := []string{}
	for _, p := range req.PermissionIds {
		if _, ok := rolePermissionsMap[p]; ok {
			delete(rolePermissionsMap, p)
		} else {
			addPermissions = append(addPermissions, p)
		}
	}
	for _, p := range rolePermissionsMap {
		deletePermissions = append(deletePermissions, p.PermissionId)
	}

	if len(addPermissions) > 0 {
		_, err = r.store.RolePermissions().BatchCreateRolePermissions(auth.AuthConText(ctx), types.BatchCreateRolePermissionReq{
			RoleId:        roleId,
			PermissionIds: addPermissions,
		})
		if err != nil {
			return err
		}
	}

	if len(deletePermissions) > 0 {
		err = r.store.RolePermissions().DeleteRolePermissions(auth.AuthConText(ctx), roleId, deletePermissions)
		if err != nil {
			return err
		}
	}

	return nil
}

func (r *roleSvc) DeleteRole(ctx context.Context, roleId string) error {

	err := r.store.Roles().DeleteRole(auth.AuthConText(ctx), roleId)
	if err != nil {
		return err
	}

	return nil
}

func (r *roleSvc) QueryRoles(ctx context.Context, params types.QueryRolesParams) ([]types.RoleDetails, error) {

	roles, err := r.store.Roles().QueryRoles(auth.AuthConText(ctx), params)
	if err != nil {
		return nil, err
	}
	var roleIds []string
	for _, role := range roles {
		roleIds = append(roleIds, role.RoleId)
	}

	return r.QueryRoleByRoleIds(ctx, roleIds)
}

func (r *roleSvc) QueryRoleByRoleIds(ctx context.Context, roleIds []string) ([]types.RoleDetails, error) {
	actx := auth.AuthConText(ctx)
	roles, err := r.store.Roles().GetRoleByIds(actx, roleIds)
	if err != nil {
		return nil, err
	}

	rolePermissions, err := r.store.RolePermissions().QueryRolePermissionsByRoleIds(actx, roleIds)
	if err != nil {
		return nil, err
	}

	permissionIdToroleIdMap := make(map[string]string)
	var permissionIds []string
	for _, rolepermission := range rolePermissions {
		permissionIds = append(permissionIds, rolepermission.PermissionId)
		permissionIdToroleIdMap[rolepermission.PermissionId] = rolepermission.RoleId
	}

	permissions, err := r.store.Permissions().GetPermissionByIds(actx, permissionIds)
	if err != nil {
		return nil, err
	}
	permissionMap := make(map[string][]types.PermissionEntity)
	for _, permission := range permissions {
		roleId := permissionIdToroleIdMap[permission.PermissionId]
		permissionMap[roleId] = append(permissionMap[roleId], permission.PermissionEntity)
	}

	resp := make([]types.RoleDetails, 0, len(roles))
	for _, role := range roles {

		resp = append(resp, types.RoleDetails{
			RoleEntity:  role.RoleEntity,
			Permissions: util.NoNilSlice(permissionMap[role.RoleId]),
		})
	}

	return resp, nil
}

func (r *roleSvc) QueryRoleByAccountId(ctx context.Context, accountId string) ([]types.RoleDetails, error) {
	roles, err := r.store.TenantMemberRoles().GetTenantMemberRoleByAccountIds(auth.AuthConText(ctx), []string{accountId})
	if err != nil {
		return nil, err
	}

	var roleIds []string
	for _, role := range roles {
		roleIds = append(roleIds, role.RoleId)
	}
	return r.QueryRoleByRoleIds(ctx, roleIds)
}

func (r *roleSvc) QueryRole(ctx context.Context, roleId string) (*types.RoleDetails, error) {

	role, err := r.store.Roles().QueryRole(auth.AuthConText(ctx), roleId)
	if err != nil {
		return nil, err
	}

	rolePermissions, err := r.store.RolePermissions().QueryRolePermissions(auth.AuthConText(ctx), roleId)
	if err != nil {
		return nil, err
	}
	var permissionIds []string
	for _, rolepermission := range rolePermissions {
		permissionIds = append(permissionIds, rolepermission.PermissionId)
	}

	permissions, err := r.store.Permissions().GetPermissionByIds(auth.AuthConText(ctx), permissionIds)
	if err != nil {
		return nil, err
	}

	var resp types.RoleDetails
	resp.RoleEntity = role.RoleEntity
	resp.Permissions = util.NoNilSlice(types.PermissionToEntity(permissions))

	return &resp, nil
}
