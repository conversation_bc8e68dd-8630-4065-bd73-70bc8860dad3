package reminder

import (
	"context"
	"testing"
	"pebble/internal/webapp/types/message"
	"pebble/internal/webapp/types/reminder"
)

func TestGetSupportedTypesWithNames(t *testing.T) {
	ctx := context.Background()
	
	tests := []struct {
		name             string
		language         string
		expectedCount    int
		expectedTypes    []string
		expectedNames    []string
	}{
		{
			name:          "English language",
			language:      "en-US",
			expectedCount: 2,
			expectedTypes: []string{
				string(message.AppointmentReminder),
				string(message.RebookAppointment),
			},
			expectedNames: []string{
				"Appointment Reminder",
				"Rebook Appointment",
			},
		},
		{
			name:          "Chinese language",
			language:      "zh-CN",
			expectedCount: 2,
			expectedTypes: []string{
				string(message.AppointmentReminder),
				string(message.RebookAppointment),
			},
			expectedNames: []string{
				"预约提醒",
				"重新预约",
			},
		},
		{
			name:          "Unsupported language fallback to English",
			language:      "fr-FR",
			expectedCount: 2,
			expectedTypes: []string{
				string(message.AppointmentReminder),
				string(message.RebookAppointment),
			},
			expectedNames: []string{
				"Appointment Reminder",
				"Rebook Appointment",
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := GetSupportedTypesWithNames(ctx, tt.language)
			
			if len(result) != tt.expectedCount {
				t.Errorf("Expected %d types, got %d", tt.expectedCount, len(result))
			}
			
			for i, item := range result {
				if item.Type != tt.expectedTypes[i] {
					t.Errorf("Expected type %s, got %s", tt.expectedTypes[i], item.Type)
				}
				
				if item.Name != tt.expectedNames[i] {
					t.Errorf("Expected name %s, got %s", tt.expectedNames[i], item.Name)
				}
			}
		})
	}
}

func TestMultilingualReminderTypesData_GetTypesForLanguage(t *testing.T) {
	data := reminder.GetDefaultReminderTypesData()
	
	t.Run("Get English types", func(t *testing.T) {
		result := data.GetTypesForLanguage("en-US")
		
		if len(result) != 2 {
			t.Errorf("Expected 2 types, got %d", len(result))
		}
		
		if result[0].Name != "Appointment Reminder" {
			t.Errorf("Expected 'Appointment Reminder', got '%s'", result[0].Name)
		}
		
		if result[1].Name != "Rebook Appointment" {
			t.Errorf("Expected 'Rebook Appointment', got '%s'", result[1].Name)
		}
	})
	
	t.Run("Get Chinese types", func(t *testing.T) {
		result := data.GetTypesForLanguage("zh-CN")
		
		if len(result) != 2 {
			t.Errorf("Expected 2 types, got %d", len(result))
		}
		
		if result[0].Name != "预约提醒" {
			t.Errorf("Expected '预约提醒', got '%s'", result[0].Name)
		}
		
		if result[1].Name != "重新预约" {
			t.Errorf("Expected '重新预约', got '%s'", result[1].Name)
		}
	})
	
	t.Run("Fallback to English for unsupported language", func(t *testing.T) {
		result := data.GetTypesForLanguage("unsupported")
		
		if len(result) != 2 {
			t.Errorf("Expected 2 types, got %d", len(result))
		}
		
		// Should fallback to English
		if result[0].Name != "Appointment Reminder" {
			t.Errorf("Expected 'Appointment Reminder', got '%s'", result[0].Name)
		}
	})
}

func TestReminderTypeItem_Structure(t *testing.T) {
	item := reminder.ReminderTypeItem{
		Type: "test_type",
		Name: "Test Name",
	}
	
	if item.Type != "test_type" {
		t.Errorf("Expected Type 'test_type', got '%s'", item.Type)
	}
	
	if item.Name != "Test Name" {
		t.Errorf("Expected Name 'Test Name', got '%s'", item.Name)
	}
}