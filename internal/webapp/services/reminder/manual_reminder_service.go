package reminder

import (
	"context"
	"pebble/internal/webapp/models"
	"pebble/internal/webapp/models/sqlserver"
	"pebble/internal/webapp/services/sms"
	twilioservice "pebble/internal/webapp/services/sms/twilio-service"
	"pebble/internal/webapp/types/message"
	"pebble/internal/webapp/types/reminder"
	"pebble/pkg/template"
)

// IManualReminderService defines the interface for manual reminder operations
type IManualReminderService interface {
	PreviewReminder(ctx context.Context, req reminder.PreviewRequest) (*reminder.PreviewMessage, error)
	SendReminder(ctx context.Context, req reminder.SendRequest) (*reminder.SendResult, error)
	GetSupportedTypes(ctx context.Context) ([]message.TemplateType, error)
}

// manualReminderService implements IManualReminderService
type manualReminderService struct {
	factory reminder.ReminderFactory
}

// NewManualReminderService creates a new manual reminder service
func NewManualReminderService(store models.IStore, smsVider sms.IProvider, renderer template.TemplateRenderer) IManualReminderService {
	factory := NewReminderFactory(store, smsVider, renderer)
	return &manualReminderService{
		factory: factory,
	}
}

// NewManualReminderServiceWithDefaults creates a new manual reminder service with default dependencies
func NewManualReminderServiceWithDefaults() IManualReminderService {
	store := sqlserver.NewStore()
	smsVider := twilioservice.NewTwilioProvider() // 使用默认的Twilio provider
	renderer := template.NewSimpleTemplateRenderer()  // 使用默认的模板渲染器
	
	return NewManualReminderService(store, smsVider, renderer)
}

// PreviewReminder generates a preview of the reminder message
func (s *manualReminderService) PreviewReminder(ctx context.Context, req reminder.PreviewRequest) (*reminder.PreviewMessage, error) {
	processor, err := s.factory.CreateProcessor(req.TemplateType)
	if err != nil {
		return nil, err
	}
	
	return processor.PreviewMessage(ctx, req)
}

// SendReminder sends the reminder message
func (s *manualReminderService) SendReminder(ctx context.Context, req reminder.SendRequest) (*reminder.SendResult, error) {
	processor, err := s.factory.CreateProcessor(req.TemplateType)
	if err != nil {
		return nil, err
	}
	
	return processor.SendMessage(ctx, req)
}

// GetSupportedTypes returns all supported reminder types
func (s *manualReminderService) GetSupportedTypes(ctx context.Context) ([]message.TemplateType, error) {
	return s.factory.GetSupportedTypes(), nil
}