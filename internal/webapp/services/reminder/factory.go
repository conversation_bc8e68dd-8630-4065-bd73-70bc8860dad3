package reminder

import (
	"fmt"
	"pebble/internal/webapp/models"
	"pebble/internal/webapp/services/sms"
	"pebble/internal/webapp/types/message"
	"pebble/internal/webapp/types/reminder"
	"pebble/pkg/template"
	"pebble/pkg/uerror"
)

// factory implements ReminderFactory interface
type factory struct {
	store    models.IStore
	smsVider sms.IProvider
	renderer template.TemplateRenderer
}

// NewReminderFactory creates a new reminder factory
func NewReminderFactory(store models.IStore, smsVider sms.IProvider, renderer template.TemplateRenderer) reminder.ReminderFactory {
	return &factory{
		store:    store,
		smsVider: smsVider,
		renderer: renderer,
	}
}

// CreateProcessor creates a processor for the given template type
func (f *factory) CreateProcessor(templateType message.TemplateType) (reminder.ReminderProcessor, error) {
	switch templateType {
	case message.AppointmentReminder:
		return NewAppointmentReminderProcessor(f.store, f.smsVider, f.renderer), nil
	case message.RebookAppointment:
		return NewRebookReminderProcessor(f.store, f.smsVider, f.renderer), nil
	default:
		return nil, uerror.New(uerror.ErrCodeInvalidParam, fmt.Sprintf("unsupported template type: %s", templateType))
	}
}

// GetSupportedTypes returns all supported template types
func (f *factory) GetSupportedTypes() []message.TemplateType {
	return []message.TemplateType{
		message.AppointmentReminder,
		message.RebookAppointment,
	}
}