package reminder

import (
	"context"
	"errors"
	"fmt"
	"pebble/internal/webapp/models"
	messageSvc "pebble/internal/webapp/services/message"
	"pebble/internal/webapp/services/sms"
	"pebble/internal/webapp/types/message"
	"pebble/internal/webapp/types/reminder"
	"pebble/pkg/auth"
	"pebble/pkg/template"
	"pebble/pkg/uerror"
	"pebble/pkg/ulog"
	"pebble/pkg/utime"
	"time"

	"go.uber.org/zap"
)

// appointmentReminderProcessor implements ReminderProcessor for appointment reminders
type appointmentReminderProcessor struct {
	store    models.IStore
	smsVider sms.IProvider
	renderer template.TemplateRenderer
}

// NewAppointmentReminderProcessor creates a new appointment reminder processor
func NewAppointmentReminderProcessor(store models.IStore, smsVider sms.IProvider, renderer template.TemplateRenderer) reminder.ReminderProcessor {
	return &appointmentReminderProcessor{
		store:    store,
		smsVider: smsVider,
		renderer: renderer,
	}
}

// GetTemplateType returns the template type this processor handles
func (p *appointmentReminderProcessor) GetTemplateType() message.TemplateType {
	return message.AppointmentReminder
}

// PreviewMessage generates a preview of the reminder message
func (p *appointmentReminderProcessor) PreviewMessage(ctx context.Context, req reminder.PreviewRequest) (*reminder.PreviewMessage, error) {
	actx := auth.AuthConText(ctx)

	if req.AppointmentId == "" {
		return nil, uerror.New(uerror.ErrCodeInvalidParam, "appointment_id is required for appointment reminder")
	}

	// 1. 根据appointment_id查询预约
	appointment, err := p.store.Appointments().Get(actx, req.AppointmentId)
	if err != nil {
		ulog.Error(ctx, "failed to get appointment", zap.String("appointmentId", req.AppointmentId), zap.Error(err))
		return nil, err
	}

	// 2. 查询客户信息
	client, err := p.store.Clients().GetClientById(actx, appointment.ClientId)
	if err != nil {
		ulog.Error(ctx, "failed to get client", zap.String("clientId", appointment.ClientId), zap.Error(err))
		return nil, err
	}

	// 3. 获取模板
	template, err := messageSvc.NewMessageTemplateService().Get(ctx, message.AppointmentReminder)
	if err != nil {
		ulog.Error(ctx, "failed to get message template", zap.Error(err))
		return nil, err
	}

	// 4. 获取location信息（用于时区处理）
	location, err := p.store.Locations().GetLocation(actx)
	if err != nil {
		ulog.Error(ctx, "failed to get location", zap.String("locationId", actx.LocationId), zap.Error(err))
		return nil, err
	}

	// 5. 复用现有逻辑：生成变量和渲染内容
	dateTime, err := utime.ParseDateTime(appointment.ScheduledStartDate, appointment.ScheduledStartTime, location.Timezone)
	if err != nil {
		ulog.Error(ctx, "failed to parse appointment date and time", zap.Error(err))
		return nil, err
	}

	variables := map[string]string{
		"client_name":       fmt.Sprintf("%s %s", client.FirstName, client.LastName),
		"client_full_name":  fmt.Sprintf("%s %s", client.FirstName, client.LastName),
		"client_first_name": client.FirstName,
		"client_last_name":  client.LastName,
		"client_email":      client.Email,
		"client_phone":      client.PhoneNumber,
		"appointment_date":  dateTime.Format(time.DateOnly),
		"appointment_time":  dateTime.Format("3:04 PM"),
	}

	content, err := p.renderer.Render(template.Content, variables)
	if err != nil {
		ulog.Error(ctx, "failed to render template", zap.Error(err))
		return nil, err
	}

	return &reminder.PreviewMessage{
		Content:       content,
		Variables:     variables,
		TemplateType:  message.AppointmentReminder,
		AppointmentId: appointment.AppointmentId,
		ClientPhone:   client.PhoneNumber,
		ClientEmail:   client.Email,
	}, nil
}

// SendMessage sends the reminder message via SMS
func (p *appointmentReminderProcessor) SendMessage(ctx context.Context, req reminder.SendRequest) (*reminder.SendResult, error) {
	actx := auth.AuthConText(ctx)

	if req.AppointmentId == "" {
		return nil, errors.New("appointment_id is required for appointment reminder")
	}

	// 1. 检查是否已发送过（防重复）
	existingLogs, err := p.store.NotificationLogs().GetExistingLogTriggerIDs(actx, []string{req.AppointmentId}, message.AppointmentReminder)
	if err != nil {
		ulog.Error(ctx, "failed to check existing notification logs", zap.Error(err))
		return nil, err
	}

	if _, exists := existingLogs[req.AppointmentId]; exists {
		return nil, errors.New("reminder already sent for this appointment")
	}

	// 2. 发送SMS（复用现有SMS发送逻辑）
	smsParams := sms.SendMessageParams{
		To:         req.Phone,
		Content:    req.Content,
		TemplateID: string(message.AppointmentReminder),
	}

	result, err := p.smsVider.SendMessage(ctx, smsParams)
	if err != nil {
		ulog.Error(ctx, "failed to send SMS", zap.Error(err))
		return nil, err
	}

	// 3. 记录发送日志
	logEntry := message.NotificationLog{
		TenantID:        actx.TenantId,
		LocationID:      actx.LocationId,
		TriggerEventID:  req.AppointmentId,
		TemplateType:    message.AppointmentReminder,
		ChannelType:     "sms",
		MessageRecordID: result.MessageID,
	}

	err = p.store.NotificationLogs().Create(actx, &logEntry)
	if err != nil {
		// 日志失败不影响发送成功
		ulog.Warn(ctx, "failed to create notification log", zap.Error(err))
	}

	return &reminder.SendResult{
		MessageId: result.MessageID,
	}, nil
}
