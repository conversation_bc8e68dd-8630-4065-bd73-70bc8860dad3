package reminder

import (
	"context"
	"errors"
	"fmt"
	"pebble/internal/webapp/models"
	messageSvc "pebble/internal/webapp/services/message"
	"pebble/internal/webapp/services/sms"
	appointmenttypes "pebble/internal/webapp/types/appointment"
	"pebble/internal/webapp/types/message"
	"pebble/internal/webapp/types/reminder"
	"pebble/pkg/auth"
	"pebble/pkg/template"
	"pebble/pkg/uerror"
	"pebble/pkg/ulog"
	"pebble/pkg/utime"
	"time"

	"go.uber.org/zap"
)

// rebookReminderProcessor implements ReminderProcessor for rebook reminders
type rebookReminderProcessor struct {
	store    models.IStore
	smsVider sms.IProvider
	renderer template.TemplateRenderer
}

// NewRebookReminderProcessor creates a new rebook reminder processor
func NewRebookReminderProcessor(store models.IStore, smsVider sms.IProvider, renderer template.TemplateRenderer) reminder.ReminderProcessor {
	return &rebookReminderProcessor{
		store:    store,
		smsVider: smsVider,
		renderer: renderer,
	}
}

// GetTemplateType returns the template type this processor handles
func (p *rebookReminderProcessor) GetTemplateType() message.TemplateType {
	return message.RebookAppointment
}

// GetTriggerEventId generates trigger event ID for rebook reminders
// This matches the logic in rebook_appointment_service.go:83-84
func (p *rebookReminderProcessor) GetTriggerEventId(clientId, appointmentId string) string {
	return fmt.Sprintf("%s_%s", clientId, appointmentId)
}

// PreviewMessage generates a preview of the rebook reminder message
func (p *rebookReminderProcessor) PreviewMessage(ctx context.Context, req reminder.PreviewRequest) (*reminder.PreviewMessage, error) {
	actx := auth.AuthConText(ctx)

	if req.ClientId == "" {
		return nil, uerror.New(uerror.ErrCodeInvalidParam, "client_id is required for rebook reminder")
	}

	// 1. 根据client_id查询客户
	client, err := p.store.Clients().GetClientById(actx, req.ClientId)
	if err != nil {
		ulog.Error(ctx, "failed to get client", zap.String("clientId", req.ClientId), zap.Error(err))
		return nil, err
	}

	// 2. 查询客户的最新预约（使用现有逻辑）
	latestAppt, err := p.getLatestAppointmentForClient(ctx, actx, client.ClientId)
	if err != nil {
		ulog.Error(ctx, "failed to get latest appointment for client", zap.String("clientId", client.ClientId), zap.Error(err))
		return nil, err
	}

	// 3. 获取模板
	template, err := messageSvc.NewMessageTemplateService().Get(ctx, message.RebookAppointment)
	if err != nil {
		ulog.Error(ctx, "failed to get message template", zap.Error(err))
		return nil, err
	}

	// 4. 获取location信息（用于时区处理）
	location, err := p.store.Locations().GetLocation(actx)
	if err != nil {
		ulog.Error(ctx, "failed to get location", zap.String("locationId", actx.LocationId), zap.Error(err))
		return nil, err
	}

	// 5. 生成rebook消息内容（复用现有变量逻辑）
	variables := map[string]string{
		"client_first_name":   client.FirstName,
		"client_last_name":    client.LastName,
		"client_full_name":    client.FirstName + " " + client.LastName,
		"client_email":        client.Email,
		"client_phone_number": client.PhoneNumber,
	}

	// 如果有最新预约信息，也可以包含预约相关变量
	if latestAppt != nil {
		dateTime, err := utime.ParseDateTime(latestAppt.ScheduledStartDate, latestAppt.ScheduledStartTime, location.Timezone)
		if err == nil {
			variables["appointment_date"] = dateTime.Format(time.DateOnly)
			variables["appointment_time"] = dateTime.Format("3:04 PM")
		}
	}

	content, err := p.renderer.Render(template.Content, variables)
	if err != nil {
		ulog.Error(ctx, "failed to render template", zap.Error(err))
		return nil, err
	}

	return &reminder.PreviewMessage{
		Content:      content,
		Variables:    variables,
		TemplateType: message.RebookAppointment,
		ClientPhone:  client.PhoneNumber,
		ClientEmail:  client.Email,
	}, nil
}

// SendMessage sends the rebook reminder message via SMS
func (p *rebookReminderProcessor) SendMessage(ctx context.Context, req reminder.SendRequest) (*reminder.SendResult, error) {
	actx := auth.AuthConText(ctx)

	if req.ClientId == "" {
		return nil, errors.New("client_id is required for rebook reminder")
	}

	// 1. 查询客户的最新预约以生成trigger event ID
	latestAppt, err := p.getLatestAppointmentForClient(ctx, actx, req.ClientId)
	if err != nil {
		ulog.Error(ctx, "failed to get latest appointment for client", zap.String("clientId", req.ClientId), zap.Error(err))
		return nil, err
	}

	if latestAppt == nil {
		return nil, errors.New("no appointment found for client")
	}

	triggerEventID := p.GetTriggerEventId(req.ClientId, latestAppt.AppointmentId)

	// 2. 检查是否已发送过（防重复）
	existingLogs, err := p.store.NotificationLogs().GetExistingLogTriggerIDs(actx, []string{triggerEventID}, message.RebookAppointment)
	if err != nil {
		ulog.Error(ctx, "failed to check existing notification logs", zap.Error(err))
		return nil, err
	}

	if _, exists := existingLogs[triggerEventID]; exists {
		return nil, errors.New("rebook reminder already sent for this client")
	}

	// 3. 发送SMS（复用现有SMS发送逻辑）
	smsParams := sms.SendMessageParams{
		ClientId:   req.ClientId,
		To:         req.Phone,
		Content:    req.Content,
		TemplateID: string(message.RebookAppointment),
	}

	result, err := p.smsVider.SendMessage(ctx, smsParams)
	if err != nil {
		ulog.Error(ctx, "failed to send SMS", zap.Error(err))
		return nil, err
	}

	// 4. 记录发送日志
	logEntry := message.NotificationLog{
		TenantID:        actx.TenantId,
		LocationID:      actx.LocationId,
		TriggerEventID:  triggerEventID,
		TemplateType:    message.RebookAppointment,
		ChannelType:     "sms",
		MessageRecordID: result.MessageID,
	}

	err = p.store.NotificationLogs().Create(actx, &logEntry)
	if err != nil {
		// 日志失败不影响发送成功
		ulog.Warn(ctx, "failed to create notification log", zap.Error(err))
	}

	return &reminder.SendResult{
		MessageId: result.MessageID,
	}, nil
}

// getLatestAppointmentForClient 获取客户的最新预约
// 这个函数复用了现有rebook service的逻辑
func (p *rebookReminderProcessor) getLatestAppointmentForClient(ctx context.Context, actx *auth.Ctx, clientId string) (*appointmenttypes.Appointment, error) {
	// 获取所有客户的最新预约
	latestAppointments, err := p.store.Appointments().ListLatestForEachClient(actx) // todo 增加只查一个 client
	if err != nil {
		return nil, err
	}

	// 查找指定客户的最新预约
	for _, appt := range latestAppointments {
		if appt.ClientId == clientId {
			return &appt, nil
		}
	}

	return nil, uerror.New(uerror.ErrCodeAppointmentNotFound, "no appointment found for client")
}
