package reminder

import (
	"context"
	"testing"
	"pebble/internal/webapp/types/message"
	"pebble/internal/webapp/types/reminder"
)

func TestManualReminderService_GetSupportedTypes(t *testing.T) {
	service := NewManualReminderServiceWithDefaults()
	
	types, err := service.GetSupportedTypes(context.Background())
	if err != nil {
		t.Fatalf("GetSupportedTypes failed: %v", err)
	}
	
	if len(types) == 0 {
		t.Fatal("Expected at least one supported type")
	}
	
	// 检查是否包含期望的类型
	expectedTypes := map[message.TemplateType]bool{
		message.AppointmentReminder: false,
		message.RebookAppointment:   false,
	}
	
	for _, typ := range types {
		if _, exists := expectedTypes[typ]; exists {
			expectedTypes[typ] = true
		}
	}
	
	for typ, found := range expectedTypes {
		if !found {
			t.<PERSON>rrorf("Expected type %s not found in supported types", typ)
		}
	}
}

func TestReminderFactory_CreateProcessor(t *testing.T) {
	factory := NewReminderFactory(nil, nil, nil) // 对于这个测试，我们不需要真实的依赖
	
	tests := []struct {
		templateType message.TemplateType
		expectError  bool
	}{
		{message.AppointmentReminder, false},
		{message.RebookAppointment, false},
		{"invalid_type", true},
	}
	
	for _, tt := range tests {
		t.Run(string(tt.templateType), func(t *testing.T) {
			processor, err := factory.CreateProcessor(tt.templateType)
			
			if tt.expectError {
				if err == nil {
					t.Errorf("Expected error for template type %s, but got none", tt.templateType)
				}
				if processor != nil {
					t.Errorf("Expected nil processor for invalid template type, but got %T", processor)
				}
			} else {
				if err != nil {
					t.Errorf("Unexpected error for template type %s: %v", tt.templateType, err)
				}
				if processor == nil {
					t.Errorf("Expected processor for template type %s, but got nil", tt.templateType)
				}
			}
		})
	}
}

func TestPreviewRequest_Validation(t *testing.T) {
	tests := []struct {
		name string
		req  reminder.PreviewRequest
		desc string
	}{
		{
			name: "appointment_reminder_with_appointment_id",
			req: reminder.PreviewRequest{
				TemplateType:  message.AppointmentReminder,
				AppointmentId: "appt123",
			},
			desc: "Valid appointment reminder request",
		},
		{
			name: "rebook_appointment_with_client_id",
			req: reminder.PreviewRequest{
				TemplateType: message.RebookAppointment,
				ClientId:     "client123",
			},
			desc: "Valid rebook appointment request",
		},
	}
	
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.req.TemplateType == "" {
				t.Errorf("TemplateType should not be empty")
			}
			
			if tt.req.TemplateType == message.AppointmentReminder && tt.req.AppointmentId == "" {
				t.Errorf("AppointmentId should not be empty for appointment reminder")
			}
			
			if tt.req.TemplateType == message.RebookAppointment && tt.req.ClientId == "" {
				t.Errorf("ClientId should not be empty for rebook appointment")
			}
		})
	}
}