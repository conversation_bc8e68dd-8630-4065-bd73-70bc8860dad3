package dashboardservice

import (
	"context"

	appointmentservice "pebble/internal/webapp/services/appointment"
	appointmenttypes "pebble/internal/webapp/types/appointment"
	commontypes "pebble/internal/webapp/types/common"
	dashboardtypes "pebble/internal/webapp/types/dashboard"
)

type dashboardSvc struct {
}

func DashboardSvc() *dashboardSvc {
	return &dashboardSvc{}
}

func (d *dashboardSvc) GetAppointmentsDashboard(ctx context.Context, date string) (*dashboardtypes.DashboardAppointmentsResp, error) {

	appointments, _, err := appointmentservice.AppointmentSvc().List(ctx, appointmenttypes.QueryAppointmentParams{
		StartDate: &date,
		EndDate:   &date,
	})
	if err != nil {
		return nil, err
	}

	resp := &dashboardtypes.DashboardAppointmentsResp{
		All:             make([]appointmenttypes.AppointmentDetail, 0),
		Expected:        make([]appointmenttypes.AppointmentDetail, 0),
		CheckedIn:       make([]appointmenttypes.AppointmentDetail, 0),
		PendingCheckout: make([]appointmenttypes.AppointmentDetail, 0),
		Completed:       make([]appointmenttypes.AppointmentDetail, 0),
	}

	for _, appt := range appointments {
		resp.All = append(resp.All, appt)
		switch appt.Status {
		case commontypes.AppointmentStatusUnconfirm, commontypes.AppointmentStatusConfirm:
			resp.Expected = append(resp.Expected, appt)
		case commontypes.AppointmentStatusArrived, commontypes.AppointmentStatusInService:
			resp.CheckedIn = append(resp.CheckedIn, appt)
		case commontypes.AppointmentStatusCompleted:
			switch appt.PaymentStatus {
			case commontypes.PaymentStatusUnpaid, commontypes.PaymentStatusPartialPaid, commontypes.PaymentStatusFailed:
				resp.PendingCheckout = append(resp.PendingCheckout, appt)
			case commontypes.PaymentStatusPaid, commontypes.PaymentStatusRefund, commontypes.PaymentStatusPartialRefund:
				resp.Completed = append(resp.Completed, appt)
			}
		}
	}

	resp.Summary.AllCount = len(resp.All)
	resp.Summary.ExpectedCount = len(resp.Expected)
	resp.Summary.CheckedInCount = len(resp.CheckedIn)
	resp.Summary.PendingCheckoutCount = len(resp.PendingCheckout)
	resp.Summary.CompletedCount = len(resp.Completed)

	return resp, nil
}
