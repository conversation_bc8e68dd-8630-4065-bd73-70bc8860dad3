package appointmentservice

import (
	"context"
	"pebble/internal/webapp/models"
	"pebble/internal/webapp/models/sqlserver"
	appointmenttypes "pebble/internal/webapp/types/appointment"
	"pebble/pkg/auth"
	"pebble/pkg/udb"
)

type appointmentNoteSvc struct {
	store models.IStore
}

func AppointmentNoteSvc() *appointmentNoteSvc {
	return &appointmentNoteSvc{
		store: sqlserver.NewStore(),
	}
}

// func (a *appointmentNoteSvc) CreateAppointmentNote(ctx context.Context, req appointmenttypes.CreateAppointmentNoteReq) (*appointmenttypes.AppointmentNote, error) {
// 	appointmentNote, err := a.store.AppointmentNote().CreateAppointmentNote(auth.AuthConText(ctx), req)
// 	if err != nil {
// 		return nil, err
// 	}
// 	return appointmentNote, nil
// }

func (a *appointmentNoteSvc) UpdateAppointmentNote(ctx context.Context, appointmentId string, req appointmenttypes.UpdateAppointmentNoteReq) error {
	if req.Note == nil {
		return nil
	}
	actx := auth.AuthConText(ctx)
	_, err := a.store.AppointmentNote().GetByAppointmentId(actx, appointmentId)
	if err != nil && !udb.IsRecordNotFound(err) {
		return err
	}
	if udb.IsRecordNotFound(err) {
		_, err := a.store.AppointmentNote().CreateAppointmentNote(actx, appointmenttypes.CreateAppointmentNoteReq{
			AppointmentId: appointmentId,
			Note:          *req.Note,
		})
		if err != nil {
			return err
		}
		return nil
	}

	return a.store.AppointmentNote().UpdateAppointmentNote(auth.AuthConText(ctx), appointmentId, req)
}

func (a *appointmentNoteSvc) GetByAppointmentId(ctx context.Context, appointmentId string) (*appointmenttypes.AppointmentNote, error) {
	note, err := a.store.AppointmentNote().GetByAppointmentId(auth.AuthConText(ctx), appointmentId)
	if udb.IsRecordNotFound(err) {
		return &appointmenttypes.AppointmentNote{
			AppointmentNoteEntity: appointmenttypes.AppointmentNoteEntity{},
		}, nil
	}
	return note, err
}

// func (a *appointmentNoteSvc) DeleteAppointmentNote(ctx context.Context, appointmentNoteId string) error {
// 	return a.store.AppointmentNote().DeleteAppointmentNote(auth.AuthConText(ctx), appointmentNoteId)
// }
