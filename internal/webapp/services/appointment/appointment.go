package appointmentservice

import (
	"context"
	"encoding/json"
	"pebble/internal/webapp/models"
	"pebble/internal/webapp/models/sqlserver"
	"pebble/internal/webapp/services"
	"pebble/internal/webapp/types"
	appointmenttypes "pebble/internal/webapp/types/appointment"
	commontypes "pebble/internal/webapp/types/common"
	paymenttypes "pebble/internal/webapp/types/payment"
	"pebble/pkg/auth"
	"pebble/pkg/paystream"
	"pebble/pkg/udb"
	"pebble/pkg/uerror"
	"pebble/pkg/util"
	"pebble/pkg/utime"
	"strings"
)

type appointmentSvc struct {
	store models.IStore
}

func AppointmentSvc() *appointmentSvc {
	return &appointmentSvc{
		store: sqlserver.NewStore(),
	}
}

func (a *appointmentSvc) handleWalkIn(ctx context.Context, data appointmenttypes.CreateAppointmentDetailReq) (appointmenttypes.CreateAppointmentDetailReq, error) {
	if data.ClientId != "" {
		return data, nil
	}

	data.Source = commontypes.AppointmentSourceWalkIn
	client, err := services.ClientSvc().CreateWalkIn(ctx)
	if err != nil {
		return data, err
	}
	data.ClientId = client.ClientId
	for i := 0; i < len(data.Services); i++ {
		data.Services[i].TargetId = client.ClientId
	}

	data.Status = commontypes.AppointmentStatusConfirm

	return data, nil
}

func (a *appointmentSvc) Create(ctx context.Context, data appointmenttypes.CreateAppointmentDetailReq) (*appointmenttypes.AppointmentDetail, error) {
	var resp appointmenttypes.AppointmentDetail
	var err error
	data, err = a.handleWalkIn(ctx, data)
	if err != nil {
		return &resp, err
	}
	err = a.store.Transaction(ctx, func(ctx context.Context) error {
		actx := auth.AuthConText(ctx)

		appointment, err := a.store.Appointments().Create(actx, appointmenttypes.AppointmentItem{
			ClientId:           data.ClientId,
			ClientCount:        data.ClientCount,
			ScheduledStartDate: data.ScheduledStartDate,
			ScheduledStartTime: data.ScheduledStartTime,
			ScheduledEndDate:   data.ScheduledEndDate,
			ScheduledEndTime:   data.ScheduledEndTime,
			Source:             data.Source,
			Status:             data.Status,
		})
		if err != nil {
			return err
		}
		resp.Appointment = *appointment

		for i := 0; i < len(data.Services); i++ {
			data.Services[i].ScheduledStartTime = data.ScheduledStartTime
		}

		services, err := a.CreateAppointmentService(ctx, appointment.AppointmentId, data.Services)
		if err != nil {
			return err
		}
		serviceDetails := make([]appointmenttypes.AppointmentSerivceDetail, 0, len(services))
		for _, service := range services {
			serviceDetails = append(serviceDetails, appointmenttypes.AppointmentSerivceDetail{
				AppointmentService: service,
			})
		}
		resp.Services = serviceDetails

		err = AppointmentNoteSvc().UpdateAppointmentNote(ctx, appointment.AppointmentId, appointmenttypes.UpdateAppointmentNoteReq{
			Note: &data.Note,
		})
		if err != nil {
			return err
		}
		resp.Note = data.Note

		return nil
	})

	return &resp, err
}

func (a *appointmentSvc) CreateAppointmentService(ctx context.Context, appointmentId string, items []appointmenttypes.CreateServiceItem) ([]appointmenttypes.AppointmentService, error) {
	if len(items) == 0 {
		return []appointmenttypes.AppointmentService{}, nil
	}

	actx := auth.AuthConText(ctx)

	var serviceIds, accountIds, clientIds []string
	for _, service := range items {
		serviceIds = append(serviceIds, service.ServiceId)
		accountIds = append(accountIds, service.AccountId)
		clientIds = append(clientIds, service.TargetId)
	}
	// clientIds = append(clientIds, data.ClientId)
	serviceIds = util.UniqueString(serviceIds)
	accountIds = util.UniqueString(accountIds)
	clientIds = util.UniqueString(clientIds)

	services, err := a.store.Services().GetByServiceIds(actx, serviceIds)
	if err != nil {
		return nil, err
	}
	var taxIds []string
	servicesMap := make(map[string]types.Service)
	for _, service := range services {
		servicesMap[service.ServiceId] = service
		taxIds = append(taxIds, service.TaxId)
	}

	taxes, err := a.store.Taxes().GetByIds(actx, taxIds)
	if err != nil {
		return nil, err
	}
	taxesMap := make(map[string]types.Tax)
	for _, tax := range taxes {
		taxesMap[tax.TaxId] = tax
	}

	accounts, err := a.store.Accounts().AdminGetAccountByIds(ctx, accountIds)
	if err != nil {
		return nil, err
	}
	accountsMap := make(map[string]types.Account)
	for _, account := range accounts {
		accountsMap[account.AccountId] = account
	}

	clients, err := a.store.Clients().GetClientByIds(actx, clientIds)
	if err != nil {
		return nil, err
	}
	clientsMap := make(map[string]types.Client)
	for _, client := range clients {
		clientsMap[client.ClientId] = client
	}

	createServices := make([]appointmenttypes.AppointmentServiceEntity, 0, len(items))
	for _, service := range items {
		serviceDetail, ok := servicesMap[service.ServiceId]
		if !ok {
			return nil, uerror.ErrServiceNotFound
		}
		account, ok := accountsMap[service.AccountId]
		if !ok && service.AccountId != "" {
			return nil, uerror.ErrAccountNotFound
		}

		client, ok := clientsMap[service.TargetId]
		if !ok {
			return nil, uerror.ErrClientNotFound
		}
		// var targetId string
		// switch service.TargetType {
		// case commontypes.TargetTypeClient:

		// 	targetId = client.ClientId
		// case commontypes.TargetTypeSubClient:
		// case commontypes.TargetTypeWalkIn:
		// 	// targetId = uid.GenerateClientId()
		// }

		tax, ok := taxesMap[serviceDetail.TaxId]
		if !ok && serviceDetail.TaxId != "" {
			return nil, uerror.ErrTaxNotFound
		}
		snapshot := appointmenttypes.AppointmentServiceSnapshot{
			AppointmentServiceSnapshotItem: appointmenttypes.AppointmentServiceSnapshotItem{
				ServiceType: serviceDetail.ServiceType,
				ServiceId:   serviceDetail.ServiceId,
				Name:        serviceDetail.Name,
				Duration:    serviceDetail.Duration,
				TaxRate:     tax.Rate,
				Price:       serviceDetail.Price,
				TaxId:       serviceDetail.TaxId,
			},
		}
		snapshotBytes, err := json.Marshal(snapshot)
		if err != nil {
			return nil, err
		}

		createServices = append(createServices, appointmenttypes.AppointmentServiceEntity{
			AccountId:          account.AccountId,
			AppointmentId:      appointmentId,
			ServiceType:        serviceDetail.ServiceType,
			ServiceId:          serviceDetail.ServiceId,
			TargetType:         service.TargetType,
			TargetId:           client.ClientId,
			ScheduledStartTime: service.ScheduledStartTime,
			Name:               serviceDetail.Name,
			Duration:           serviceDetail.Duration,
			TaxRate:            tax.Rate,
			Price:              serviceDetail.Price,
			OriginTaxId:        serviceDetail.TaxId,
			OriginTaxRate:      tax.Rate,
			OriginPrice:        serviceDetail.Price,
			Snapshot:           string(snapshotBytes),
		})
	}

	serrvices, err := a.store.AppointmentServices().BatchCreate(actx, createServices)
	if err != nil {
		return nil, err
	}

	return serrvices, nil
}

func (a *appointmentSvc) Get(ctx context.Context, appointmentId string) (*appointmenttypes.AppointmentDetail, error) {
	actx := auth.AuthConText(ctx)
	appointment, err := a.store.Appointments().Get(actx, appointmentId)
	if udb.IsRecordNotFound(err) {
		return nil, uerror.ErrAppointmentNotFound
	}
	if err != nil {
		return nil, err
	}

	services, err := a.store.AppointmentServices().Get(actx, appointmentId)
	if err != nil {
		return nil, err
	}
	clientIds := []string{appointment.ClientId}
	accountIds := make([]string, 0, len(services))
	for _, service := range services {
		clientIds = append(clientIds, service.TargetId)
		accountIds = append(accountIds, service.AccountId)
	}

	tips, err := a.store.AppointmentTip().GetByAppointmentIds(auth.AuthConText(ctx), []string{appointmentId})
	if err != nil {
		return nil, err
	}
	for _, tip := range tips {
		clientIds = append(clientIds, tip.ClientId)
		accountIds = append(accountIds, tip.AccountId)
	}
	clientIds = util.UniqueString(clientIds)
	accountIds = util.UniqueString(accountIds)

	clients, err := a.store.Clients().GetClientByIds(actx, clientIds)
	if err != nil {
		return nil, err
	}
	clientsMap := make(map[string]types.Client)
	for _, client := range clients {
		clientsMap[client.ClientId] = client
	}
	accounts, err := a.store.TenantMembers().GetTenantMemberByAccountIds(actx, accountIds)
	if err != nil {
		return nil, err
	}
	accountsMap := make(map[string]types.TenantMember)
	for _, account := range accounts {
		accountsMap[account.AccountId] = account
	}

	serviceDetails := make([]appointmenttypes.AppointmentSerivceDetail, 0, len(services))
	for _, service := range services {
		item := appointmenttypes.AppointmentSerivceDetail{
			AppointmentService: service,
			TargetFirstName:    clientsMap[service.TargetId].FirstName,
			TargetLastName:     clientsMap[service.TargetId].LastName,
			AccountFirstName:   accountsMap[service.AccountId].FirstName,
			AccountLastName:    accountsMap[service.AccountId].LastName,
		}
		serviceDetails = append(serviceDetails, item)
	}

	payments, err := AppointmentPaymentSvc().QueryPaymentByAppointmentId(ctx, appointmentId)
	if err != nil {
		return nil, err
	}

	note, err := AppointmentNoteSvc().GetByAppointmentId(ctx, appointmentId)
	if err != nil {
		return nil, err
	}

	tipDetails := make([]appointmenttypes.AppointmentTipDetail, 0, len(tips))
	for _, tip := range tips {
		item := appointmenttypes.AppointmentTipDetail{
			AppointmentTip:   tip,
			ClientFirstName:  clientsMap[tip.ClientId].FirstName,
			ClientLastName:   clientsMap[tip.ClientId].LastName,
			AccountFirstName: accountsMap[tip.AccountId].FirstName,
			AccountLastName:  accountsMap[tip.AccountId].LastName,
		}
		tipDetails = append(tipDetails, item)
	}

	return &appointmenttypes.AppointmentDetail{
		Appointment:     *appointment,
		Client:          clientsMap[appointment.ClientId],
		ClientFirstName: clientsMap[appointment.ClientId].FirstName,
		ClientLastName:  clientsMap[appointment.ClientId].LastName,
		Services:        serviceDetails,
		PaymentRecords:  util.NoNilSlice(payments),
		Note:            note.Note,
		Tips:            util.NoNilSlice(tipDetails),
	}, nil
}

func (a *appointmentSvc) List(ctx context.Context, params appointmenttypes.QueryAppointmentParams) ([]appointmenttypes.AppointmentDetail, int64, error) {
	actx := auth.AuthConText(ctx)

	conditions := appointmenttypes.GetAppointmentConditions{
		ScheduledStartDate: params.StartDate,
		ScheduledEndDate:   params.EndDate,
		StatusList: []commontypes.AppointmentStatusType{
			commontypes.AppointmentStatusUnconfirm,
			commontypes.AppointmentStatusConfirm,
			commontypes.AppointmentStatusWaitListen,
			commontypes.AppointmentStatusArrived,
			commontypes.AppointmentStatusInService,
			commontypes.AppointmentStatusCompleted,
		},
	}

	if params.Keyword != nil && *params.Keyword != "" {
		clients, _, err := services.ClientSvc().QueryAllClients(ctx, types.QueryClientParams{
			Keyword: params.Keyword,
		})
		if err != nil {
			return nil, 0, err
		}
		if len(clients) == 0 {
			return []appointmenttypes.AppointmentDetail{}, 0, nil
		}

		clientIds := make([]string, 0, len(clients))
		for _, client := range clients {
			clientIds = append(clientIds, client.ClientId)
		}
		conditions.ClientIds = util.UniqueString(clientIds)
	}

	appointments, total, err := a.store.Appointments().List(actx, conditions)
	if err != nil {
		return nil, 0, err
	}

	resp, err := a.ToAppointmentDetail(ctx, appointments)
	if err != nil {
		return nil, 0, err
	}

	return resp, total, nil
}

func (a *appointmentSvc) ToAppointmentDetail(ctx context.Context, appointments []appointmenttypes.Appointment) ([]appointmenttypes.AppointmentDetail, error) {

	actx := auth.AuthConText(ctx)
	clientIds := make([]string, 0, len(appointments))
	appointmentIds := make([]string, 0, len(appointments))
	for _, appointment := range appointments {
		appointmentIds = append(appointmentIds, appointment.AppointmentId)
		clientIds = append(clientIds, appointment.ClientId)
	}

	allServices, err := a.store.AppointmentServices().GetByAppointmentIds(auth.AuthConText(ctx), appointmentIds)
	if err != nil {
		return nil, err
	}

	accountIds := make([]string, 0, len(allServices))
	for _, service := range allServices {
		clientIds = append(clientIds, service.TargetId)
		accountIds = append(accountIds, service.AccountId)
	}

	tips, err := a.store.AppointmentTip().GetByAppointmentIds(actx, appointmentIds)
	if err != nil {
		return nil, err
	}
	for _, tip := range tips {
		clientIds = append(clientIds, tip.ClientId)
		accountIds = append(accountIds, tip.AccountId)
	}

	accountIds = util.UniqueString(accountIds)
	clientIds = util.UniqueString(clientIds)
	clients, err := a.store.Clients().GetClientByIds(actx, clientIds)
	if err != nil {
		return nil, err
	}
	clientsMap := make(map[string]types.Client)
	for _, client := range clients {
		clientsMap[client.ClientId] = client
	}
	accounts, err := a.store.TenantMembers().GetTenantMemberByAccountIds(actx, accountIds)
	if err != nil {
		return nil, err
	}
	accountsMap := make(map[string]types.TenantMember)
	for _, account := range accounts {
		accountsMap[account.AccountId] = account
	}

	serviceMap := make(map[string][]appointmenttypes.AppointmentSerivceDetail)
	for _, service := range allServices {
		item := appointmenttypes.AppointmentSerivceDetail{
			AppointmentService: service,
			TargetFirstName:    clientsMap[service.TargetId].FirstName,
			TargetLastName:     clientsMap[service.TargetId].LastName,
			AccountFirstName:   accountsMap[service.AccountId].FirstName,
			AccountLastName:    accountsMap[service.AccountId].LastName,
		}
		serviceMap[service.AppointmentId] = append(serviceMap[service.AppointmentId], item)
	}

	paymentRecordMap, err := AppointmentPaymentSvc().QueryPaymentByAppointmentIds(ctx, appointmentIds)
	if err != nil {
		return nil, err
	}

	notes, err := a.store.AppointmentNote().GetByAppointmentIds(actx, appointmentIds)
	if err != nil {
		return nil, err
	}
	noteMap := make(map[string]string)
	for _, note := range notes {
		noteMap[note.AppointmentId] = note.Note
	}

	tipsMap := make(map[string][]appointmenttypes.AppointmentTipDetail)
	for _, tip := range tips {
		tipsMap[tip.AppointmentId] = append(tipsMap[tip.AppointmentId], appointmenttypes.AppointmentTipDetail{
			AppointmentTip:   tip,
			ClientFirstName:  clientsMap[tip.ClientId].FirstName,
			ClientLastName:   clientsMap[tip.ClientId].LastName,
			AccountFirstName: accountsMap[tip.AccountId].FirstName,
			AccountLastName:  accountsMap[tip.AccountId].LastName,
		})
	}

	resp := make([]appointmenttypes.AppointmentDetail, 0, len(appointments))
	for _, appointment := range appointments {
		client := clientsMap[appointment.ClientId]
		resp = append(resp, appointmenttypes.AppointmentDetail{
			Appointment:     appointment,
			Client:          client,
			ClientFirstName: client.FirstName,
			ClientLastName:  client.LastName,
			Services:        util.NoNilSlice(serviceMap[appointment.AppointmentId]),
			PaymentRecords:  util.NoNilSlice(paymentRecordMap[appointment.AppointmentId]),
			Note:            noteMap[appointment.AppointmentId],
			Tips:            util.NoNilSlice(tipsMap[appointment.AppointmentId]),
		})
	}

	return resp, nil
}

func (a *appointmentSvc) QueryAppointmentCalender(ctx context.Context, params appointmenttypes.QueryAppointmentCalenderParams) ([]appointmenttypes.QueryAppointmentCalenderReps, error) {
	actx := auth.AuthConText(ctx)
	appointments, _, err := a.List(ctx, appointmenttypes.QueryAppointmentParams{
		StartDate: params.StartDate,
		EndDate:   params.EndDate,
	})
	if err != nil {
		return nil, err
	}

	appointmentMap := make(map[string][]appointmenttypes.CalenderAppointment)
	for _, appointment := range appointments {
		appointmentSreviceMap := make(map[string][]appointmenttypes.CalenderAppointmentService)
		for _, service := range appointment.Services {
			appointmentSreviceMap[service.AccountId] = append(appointmentSreviceMap[service.AccountId], appointmenttypes.CalenderAppointmentService{
				ServiceId:          service.ServiceId,
				ServiceType:        service.ServiceType,
				TargetType:         service.TargetType,
				TargetId:           service.TargetId,
				TargetFirstName:    service.TargetFirstName,
				TargetLastName:     service.TargetLastName,
				Name:               service.Name,
				Duration:           service.Duration,
				ScheduledStartTime: service.ScheduledStartTime,
			})
		}

		for accountId, services := range appointmentSreviceMap {
			item := appointmenttypes.CalenderAppointment{
				AppointmentEntity: appointment.AppointmentEntity,
				ClientFirstName:   appointment.ClientFirstName,
				ClientLastName:    appointment.ClientLastName,
				Client:            appointment.Client,
				Services:          services,
				Note:              appointment.Note,
				MultipleStaff:     len(appointmentSreviceMap) > 1,
			}
			if len(services) > 0 && services[0].ScheduledStartTime > 0 {
				item.ScheduledStartTime = services[0].ScheduledStartTime
				var duration int64
				for _, service := range services {
					duration += service.Duration
				}
				endDate, endTime, _ := utime.AddOffsetToDateString(appointment.ScheduledStartDate, duration)
				item.ScheduledEndTime = endTime
				item.ScheduledEndDate = endDate
			}

			appointmentMap[accountId] = append(appointmentMap[accountId], item)
		}
	}

	staffs, err := services.StaffSvc().QueryStaffs(ctx, types.QueryTenantMemberParams{})
	if err != nil {
		return nil, err
	}

	accoutIds := make([]string, 0, len(staffs))
	for _, staff := range staffs {
		accoutIds = append(accoutIds, staff.AccountId)
	}
	schedules, err := services.StaffScheduleSvc().QueryMultiStaffSchedulesDate(ctx, types.QueryMultiStaffSchedulesDateParams{
		AccountIds: accoutIds,
		StartDate:  params.StartDate,
		EndDate:    params.EndDate,
	})
	if err != nil {
		return nil, err
	}
	schedulesMap := make(map[string][]types.QueryMultiStaffSchedulesDateResp)
	for _, v := range schedules {
		schedulesMap[v.AccountID] = append(schedulesMap[v.AccountID], v)
	}

	var date string
	var sec int64
	if params.CurrentDate == nil || params.CurrentTime == nil {
		loc, err := services.LocationSvc().QueryLocation(ctx)
		if err != nil {
			return nil, err
		}
		date, sec, err = utime.GetCurrentDateAndSeconds(loc.Timezone)
		if err != nil {
			return nil, err
		}
	} else {
		date = *params.CurrentDate
		sec = *params.CurrentTime
	}

	shifs, err := services.ScheduleShiftsSvc().QueryStaffScheduleShifts(ctx, types.QueryStaffScheduleShiftsParams{
		Date: date,
		Time: sec,
	})
	if err != nil {
		return nil, err
	}
	shifsIndexMap := make(map[string]int)
	for index, shift := range shifs {
		shifsIndexMap[shift.AccountId] = index + 1
	}

	resp := make([]appointmenttypes.QueryAppointmentCalenderReps, 0, len(staffs))
	for _, staff := range staffs {
		appt, ok := appointmentMap[staff.AccountId]
		if !ok {
			appt = []appointmenttypes.CalenderAppointment{}
		} else {
			delete(appointmentMap, staff.AccountId)
		}

		schedule, ok := schedulesMap[staff.AccountId]
		if !ok {
			schedule = []types.QueryMultiStaffSchedulesDateResp{}
		}
		schedulesMap := make(map[string]types.QueryMultiStaffSchedulesDateResp)
		for _, item := range schedule {
			schedulesMap[item.Date] = item
		}

		resp = append(resp, appointmenttypes.QueryAppointmentCalenderReps{
			AccountId:    staff.AccountId,
			Index:        int64(shifsIndexMap[staff.AccountId]),
			Appointments: appt,
			Color:        staff.Color,
			FirstName:    staff.FirstName,
			LastName:     staff.LastName,
			LocationId:   staff.LocationId,
			TenantId:     staff.TenantId,
			Schedules:    schedulesMap,
		})
	}

	unspecifiedAppt := make([]appointmenttypes.CalenderAppointment, 0, len(appointmentMap))
	for _, appt := range appointmentMap {
		unspecifiedAppt = append(unspecifiedAppt, appt...)
	}

	resp = append([]appointmenttypes.QueryAppointmentCalenderReps{{
		AccountId:    "",
		Appointments: unspecifiedAppt,
		FirstName:    "Unassigned",
		LastName:     "",
		LocationId:   actx.LocationId,
		TenantId:     actx.TenantId,
		Schedules:    map[string]types.QueryMultiStaffSchedulesDateResp{},
	}}, resp...)

	return resp, nil
}

func (a *appointmentSvc) Update(ctx context.Context, appointmentId string, data appointmenttypes.UpdateAppointmentReq) error {
	actx := auth.AuthConText(ctx)
	_, err := a.store.Appointments().Get(actx, appointmentId)
	if udb.IsRecordNotFound(err) {
		return uerror.ErrAppointmentNotFound
	}
	if err != nil {
		return err
	}

	err = a.store.Appointments().Update(actx, appointmentId, data)
	if err != nil {
		return err
	}

	return nil
}

func (a *appointmentSvc) Delete(ctx context.Context, appointmentId string) error {
	actx := auth.AuthConText(ctx)
	err := a.store.Appointments().Delete(actx, appointmentId)
	if err != nil {
		return err
	}

	err = a.store.AppointmentServices().Delete(actx, appointmentId)
	if err != nil {
		return err
	}

	return nil
}

func (a *appointmentSvc) UpdateServices(ctx context.Context, appointmentId, apptServiceId string, data appointmenttypes.UpdateAppointmentServiceReq) error {
	actx := auth.AuthConText(ctx)
	_, err := a.store.Appointments().Get(actx, appointmentId)
	if udb.IsRecordNotFound(err) {
		return uerror.ErrAppointmentNotFound
	}
	if err != nil {
		return err
	}

	err = a.store.AppointmentServices().Update(actx, appointmentId, apptServiceId, data)
	if err != nil {
		return err
	}

	return nil
}

func (a *appointmentSvc) UpdateFullAppointment(ctx context.Context, appointmentId string, data appointmenttypes.UpdateFullAppointmentReq) error {
	err := a.Update(ctx, appointmentId, data.UpdateAppointmentReq)
	if err != nil {
		return err
	}

	err = AppointmentNoteSvc().UpdateAppointmentNote(ctx, appointmentId, appointmenttypes.UpdateAppointmentNoteReq{
		Note: data.Note,
	})
	if err != nil {
		return err
	}

	return a.BatchUpdateServices(ctx, appointmentId, appointmenttypes.BatchUpdateAppointmentServiceReq{
		Items: data.Services,
	})

}

func (a *appointmentSvc) BatchUpdateServices(ctx context.Context, appointmentId string, data appointmenttypes.BatchUpdateAppointmentServiceReq) error {
	actx := auth.AuthConText(ctx)
	_, err := a.store.Appointments().Get(actx, appointmentId)
	if udb.IsRecordNotFound(err) {
		return uerror.ErrAppointmentNotFound
	}
	if err != nil {
		return err
	}

	services, err := a.store.AppointmentServices().Get(actx, appointmentId)
	if err != nil {
		return err
	}
	servicesMap := make(map[string]appointmenttypes.AppointmentService)
	for _, service := range services {
		servicesMap[service.ApptServiceId] = service
	}

	err = a.store.Transaction(ctx, func(ctx context.Context) error {
		createItems := []appointmenttypes.CreateServiceItem{}
		for _, item := range data.Items {
			_, ok := servicesMap[item.ApptServiceId]
			if !ok && item.ServiceId != "" {
				createItems = append(createItems, appointmenttypes.CreateServiceItem{
					AccountId:   item.AccountId,
					ServiceId:   item.ServiceId,
					ServiceType: item.ServiceType,
					TargetType:  item.TargetType,
					TargetId:    item.TargetId,
				})
			} else if ok && item.ApptServiceId != "" {
				err = a.store.AppointmentServices().Update(actx, appointmentId, item.ApptServiceId, item.UpdateAppointmentServiceReq)
				if err != nil {
					return err
				}
				delete(servicesMap, item.ApptServiceId)
			}

		}

		var deleteApptServiceIds []string
		for _, item := range servicesMap {
			deleteApptServiceIds = append(deleteApptServiceIds, item.ApptServiceId)
		}
		err := a.store.AppointmentServices().BatchDelete(actx, appointmentId, deleteApptServiceIds)
		if err != nil {
			return err
		}

		_, err = a.CreateAppointmentService(ctx, appointmentId, createItems)
		if err != nil {
			return err
		}

		return nil
	})

	return err
}

func (a *appointmentSvc) InitAppointmentWalkInClients(ctx context.Context, appointmentId string) ([]string, error) {
	actx := auth.AuthConText(ctx)
	appt, err := a.store.Appointments().Get(actx, appointmentId)
	if err != nil {
		return nil, err
	}
	if appt.ClientCount <= 1 {
		return []string{appt.ClientId}, nil
	}

	appointmentGuests, err := a.store.AppointmentGuest().Get(actx, appointmentId)
	if err != nil {
		return nil, err
	}

	if int64(len(appointmentGuests)) == appt.ClientCount-1 {
		var clientIds []string
		for _, guest := range appointmentGuests {
			clientIds = append(clientIds, guest.ClientId)
		}
		clientIds = append([]string{appt.ClientId}, clientIds...)
		return clientIds, nil
	}

	if int64(len(appointmentGuests)) < appt.ClientCount-1 {
		walkinCount := appt.ClientCount - int64(len(appointmentGuests)) - 1
		clients, err := services.ClientSvc().CreateWalkInClients(ctx, int(walkinCount))
		if err != nil {
			return nil, err
		}
		items := make([]appointmenttypes.AppointmentGuestEntity, 0, len(clients))
		for _, client := range clients {
			items = append(items, appointmenttypes.AppointmentGuestEntity{
				AppointmentId: appointmentId,
				ClientId:      client.ClientId,
			})
		}

		createdGuests, err := a.store.AppointmentGuest().BatchCreate(actx, items)
		if err != nil {
			return nil, err
		}
		appointmentGuests = append(appointmentGuests, createdGuests...)
	} else if len(appointmentGuests) > int(appt.ClientCount-1) {
		appointmentGuests = appointmentGuests[:appt.ClientCount-1]
	}

	clientIds := make([]string, 0, len(appointmentGuests))
	for _, guest := range appointmentGuests {
		clientIds = append(clientIds, guest.ClientId)
	}
	clientIds = util.UniqueString(clientIds)
	clientIds = append([]string{appt.ClientId}, clientIds...)

	apptServices, err := a.store.AppointmentServices().Get(actx, appointmentId)
	if err != nil {
		return nil, err
	}

	items := make([]appointmenttypes.AssignmentAppointmentServiceItem, 0, len(apptServices))
	for i, service := range apptServices {
		items = append(items, appointmenttypes.AssignmentAppointmentServiceItem{
			ApptServiceId: service.ApptServiceId,
			TargetId:      clientIds[i%len(clientIds)],
		})
	}
	err = AppointmentSvc().AssignmentServices(actx, appointmentId, appointmenttypes.AssignmentAppointmentServiceReq{
		Items: items,
	})
	if err != nil {
		return nil, err
	}

	return clientIds, nil
}

func (a *appointmentSvc) CreateBills(ctx context.Context, appointmentId, paymentMethodName string) (appointmenttypes.Bills, error) {
	appointment, err := a.Get(ctx, appointmentId)
	if err != nil {
		return appointmenttypes.Bills{}, err
	}

	location, err := a.store.Locations().GetLocation(auth.AuthConText(ctx))
	if err != nil {
		return appointmenttypes.Bills{}, err
	}

	balance, err := a.Bills(ctx, appointmenttypes.BillReq{
		CurrencySymbol:    location.CurrencySymbol,
		Appointment:       *appointment,
		PaymentMethodName: paymentMethodName,
	})
	if err != nil {
		return appointmenttypes.Bills{}, err
	}
	return balance, nil
}

func (a *appointmentSvc) CreateAABills(ctx context.Context, appointmentId, paymentMethodName string) (*appointmenttypes.AABills, error) {
	actx := auth.AuthConText(ctx)

	_, err := a.InitAppointmentWalkInClients(ctx, appointmentId)
	if err != nil {
		return nil, err
	}

	appointment, err := a.Get(ctx, appointmentId)
	if err != nil {
		return nil, err
	}

	location, err := a.store.Locations().GetLocation(actx)
	if err != nil {
		return nil, err
	}

	bills, err := a.AABills(ctx, appointmenttypes.BillReq{
		CurrencySymbol:    location.CurrencySymbol,
		Appointment:       *appointment,
		PaymentMethodName: paymentMethodName,
	})
	if err != nil {
		return nil, err
	}

	return bills, nil
}

func (a *appointmentSvc) Bills(ctx context.Context, req appointmenttypes.BillReq) (appointmenttypes.Bills, error) {
	actx := auth.AuthConText(ctx)
	var resp appointmenttypes.Bills
	resp.ClientId = req.Appointment.ClientId
	resp.ClientFirstName = req.Appointment.ClientFirstName
	resp.ClientLastName = req.Appointment.ClientLastName
	// tip

	resp.CurrencySymbol = req.CurrencySymbol

	resp.Tips = req.Appointment.Tips
	for _, tip := range req.Appointment.Tips {
		resp.AmountTip += tip.AmountTip
		resp.AmountTotal += tip.AmountTip
	}

	// service
	var serviceBalances []appointmenttypes.ServiceBills
	for _, service := range req.Appointment.Services {
		tax := util.CalculateTax(service.Price, service.TaxRate)
		serviceBalances = append(serviceBalances, appointmenttypes.ServiceBills{
			Name:             service.Name,
			Duration:         service.Duration,
			Price:            service.Price,
			OriginPrice:      service.OriginPrice,
			TaxRate:          service.TaxRate,
			Tax:              tax,
			OriginTaxRate:    service.OriginTaxRate,
			ApptServiceId:    service.ApptServiceId,
			ServiceId:        service.ServiceId,
			AccountId:        service.AccountId,
			AccountFirstName: service.AccountFirstName,
			AccountLastName:  service.AccountLastName,
			TargetType:       service.TargetType,
			TargetId:         service.TargetId,
			TargetFirstName:  service.TargetFirstName,
			TargetLastName:   service.TargetLastName,
		})
		resp.AmountTax += tax
		resp.AmountSubtotal += service.Price
		resp.AmountTotal += service.Price + tax // todo tax
	}
	resp.Services = util.NoNilSlice(serviceBalances)

	for _, payment := range req.Appointment.PaymentRecords {
		resp.AmountPaid += payment.AmountTotal
		for _, item := range payment.Items {
			if item.OrderItemType == commontypes.OrdertypeTip {
				resp.AmountTipPaid += item.AmountTotal
			}
		}
	}

	resp.AmountUnpaid = resp.AmountTotal - resp.AmountPaid
	resp.AmountTipUnpaid = resp.AmountTip - resp.AmountTipPaid
	resp.PaymentRecords = util.NoNilSlice(req.Appointment.PaymentRecords)
	resp.FeePayer = commontypes.FeePayerCustomer

	var feeConf paystream.FeeConfig
	switch strings.ToLower(req.PaymentMethodName) {
	case commontypes.PaymentMethodStripe:
		setting, err := a.store.StripeSettings().Get(actx)
		if err != nil {
			return resp, err
		}
		feeConf.ApplicationFeePercent = setting.ApplicationFeePercent
		feeConf.PaymentBaseFee = setting.OnlineBaseFee
		feeConf.PaymentFeePercent = setting.OnlineFeePercent
		feeConf.FeePayer = int8(setting.FeePayer) // todo
		resp.FeePayer = setting.FeePayer

	case commontypes.PaymentMethodStripeTerminal:
		setting, err := a.store.StripeSettings().Get(actx)
		if err != nil {
			return resp, err
		}
		feeConf.ApplicationFeePercent = setting.ApplicationFeePercent
		feeConf.PaymentBaseFee = setting.CardBaseFee
		feeConf.PaymentFeePercent = setting.CardFeePercent
		feeConf.FeePayer = int8(setting.FeePayer)
		resp.FeePayer = setting.FeePayer
	}

	fee := paystream.CalcFee(resp.AmountUnpaid, feeConf)
	resp.Fee = fee
	resp.AmountFee = fee.PaymentFee
	resp.AmountUnpaid = fee.Amount
	resp.AmountTotal = resp.AmountPaid + resp.AmountUnpaid
	tipFee := paystream.CalcFee(resp.AmountTipUnpaid, feeConf)
	resp.AmountTipUnpaid = tipFee.Amount
	resp.AmountTip = resp.AmountTipPaid + resp.AmountTipUnpaid

	if resp.AmountUnpaid == resp.AmountTotal {
		resp.PaymentStatus = commontypes.PaymentStatusUnpaid
	} else if resp.AmountUnpaid > 0 {
		resp.PaymentStatus = commontypes.PaymentStatusPartialPaid
	} else {
		resp.PaymentStatus = commontypes.PaymentStatusPaid
	}

	return resp, nil
}

func (a *appointmentSvc) AABills(ctx context.Context, req appointmenttypes.BillReq) (*appointmenttypes.AABills, error) {
	totalBill, err := a.Bills(ctx, req)
	if err != nil {
		return nil, err
	}

	clientIds, err := a.InitAppointmentWalkInClients(ctx, req.Appointment.AppointmentId)
	if err != nil {
		return nil, err
	}

	serviceMap := make(map[string][]appointmenttypes.AppointmentSerivceDetail)
	for _, s := range req.Appointment.Services {
		serviceMap[s.TargetId] = append(serviceMap[s.TargetId], s)
	}

	tipsMap := make(map[string][]appointmenttypes.AppointmentTipDetail)
	for _, t := range req.Appointment.Tips {
		tipsMap[t.ClientId] = append(tipsMap[t.ClientId], t)
	}

	paymentMap := make(map[string][]paymenttypes.PaymentRecordDetails)
	for _, p := range req.Appointment.PaymentRecords {
		if p.TargetId == "" {
			// for old data, assign to main client
			paymentMap[req.Appointment.ClientId] = append(paymentMap[req.Appointment.ClientId], p)
			continue
		}
		paymentMap[p.TargetId] = append(paymentMap[p.TargetId], p)
	}

	var items []appointmenttypes.Bills
	for i, clientId := range clientIds {
		services := serviceMap[clientId]

		var clientFirstName string = "walk"
		var clientLastName string = "in"
		if len(services) > 0 {
			clientFirstName = services[0].TargetFirstName
			clientLastName = services[0].TargetLastName
		}

		if clientFirstName == "walk" && clientLastName == "in" && i > 0 {
			clientLastName = clientLastName + "(" + string(rune('A'+i-1)) + ")"
		}

		tempDetail := appointmenttypes.AppointmentDetail{
			Appointment: appointmenttypes.Appointment{
				AppointmentEntity: appointmenttypes.AppointmentEntity{
					ClientId: clientId,
				},
			},
			ClientFirstName: clientFirstName,
			ClientLastName:  clientLastName,
			Services:        services,
			Tips:            util.NoNilSlice(tipsMap[clientId]),
			PaymentRecords:  util.NoNilSlice(paymentMap[clientId]),
		}

		bill, err := a.Bills(ctx, appointmenttypes.BillReq{
			PaymentMethodName: req.PaymentMethodName,
			Appointment:       tempDetail,
			CurrencySymbol:    req.CurrencySymbol,
		})
		if err != nil {
			return nil, err
		}
		items = append(items, bill)
	}

	resp := appointmenttypes.AABills{
		CurrencySymbol:  req.CurrencySymbol,
		AmountTip:       totalBill.AmountTip,
		AmountTax:       totalBill.AmountTax,
		AmountFee:       totalBill.AmountFee,
		AmountTotal:     totalBill.AmountTotal,
		AmountSubtotal:  totalBill.AmountSubtotal,
		AmountRefund:    totalBill.AmountRefund,
		AmountPaid:      totalBill.AmountPaid,
		AmountUnpaid:    totalBill.AmountUnpaid,
		AmountTipUnpaid: totalBill.AmountTipUnpaid,
		AmountTipPaid:   totalBill.AmountTipPaid,
		FeePayer:        totalBill.FeePayer,
		PaymentStatus:   totalBill.PaymentStatus,
		Items:           items,
	}

	return &resp, nil
}

func (a *appointmentSvc) UpdateAppointmentServiceAccount(ctx context.Context, appointmentId string, accountId string) error {
	return a.store.AppointmentServices().UpdateAccountIdByAppointmentId(auth.AuthConText(ctx), appointmentId, accountId)
}

func (a *appointmentSvc) UpdateAppointmentServiceAccountAndScheduledStartTime(ctx context.Context, appointmentId string, data appointmenttypes.UpdateAccountAndTime) error {
	return a.store.AppointmentServices().UpdateAccountAndScheduledStartTime(auth.AuthConText(ctx), appointmentId, data)
}

func (a *appointmentSvc) DragAppointment(ctx context.Context, appointmentId string, data appointmenttypes.DragAppointmentReq) error {
	err := a.store.AppointmentServices().UpdateAccountAndScheduledStartTime(auth.AuthConText(ctx), appointmentId, appointmenttypes.UpdateAccountAndTime{
		SrcAccountId:       data.SrcAccountId,
		DstAccountId:       data.DstAccountId,
		ScheduledStartTime: data.ScheduledStartTime,
	})
	if err != nil {
		return err
	}

	err = a.store.Appointments().Update(auth.AuthConText(ctx), appointmentId, appointmenttypes.UpdateAppointmentReq{
		ScheduledStartDate: &data.ScheduledStartDate,
		ScheduledStartTime: &data.ScheduledStartTime,
		ScheduledEndDate:   &data.ScheduledEndDate,
		ScheduledEndTime:   &data.ScheduledEndTime,
	})
	if err != nil {
		return err
	}

	return err
}

func (a *appointmentSvc) AssignmentServices(actx *auth.Ctx, appointmentId string, data appointmenttypes.AssignmentAppointmentServiceReq) error {
	return a.store.AppointmentServices().BatchUpdateTarget(actx, appointmentId, data)
}
