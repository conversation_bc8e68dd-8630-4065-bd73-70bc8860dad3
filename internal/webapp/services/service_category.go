package services

import (
	"context"
	"pebble/internal/webapp/models"
	"pebble/internal/webapp/models/sqlserver"
	"pebble/internal/webapp/types"
	"pebble/pkg/auth"
	"pebble/pkg/ulog"
)

type serviceCategorySvc struct {
	store models.IStore
}

func ServiceCategorySvc() *serviceCategorySvc {
	return &serviceCategorySvc{
		store: sqlserver.NewStore(),
	}
}

func (s *serviceCategorySvc) Create(ctx context.Context, req types.CreateServiceCategoryReq) (*types.ServiceCategory, error) {
	actx := auth.AuthConText(ctx)
	resp, err := s.store.ServiceCategories().Create(actx, req)
	if err != nil {
		ulog.Errorln(ctx, "create service category err", err)
		return nil, err
	}

	return resp, nil
}

func (s *serviceCategorySvc) Get(ctx context.Context, serviceCategoryId string) (*types.ServiceCategory, error) {
	actx := auth.AuthConText(ctx)
	resp, err := s.store.ServiceCategories().Get(actx, serviceCategoryId)
	if err != nil {
		ulog.Errorln(ctx, "get service category err", err)
		return nil, err
	}

	return resp, nil
}

func (s *serviceCategorySvc) List(ctx context.Context) ([]types.ServiceCategory, error) {
	actx := auth.AuthConText(ctx)
	resp, err := s.store.ServiceCategories().List(actx)
	if err != nil {
		ulog.Errorln(ctx, "list service category err", err)
		return nil, err
	}

	return resp, nil
}

func (s *serviceCategorySvc) Update(ctx context.Context, serviceCategoryId string, req types.UpdateServiceCategoryReq) error {
	actx := auth.AuthConText(ctx)
	err := s.store.ServiceCategories().Update(actx, serviceCategoryId, req)
	if err != nil {
		ulog.Errorln(ctx, "update service category err", err)
		return err
	}

	return nil
}

func (s *serviceCategorySvc) Delete(ctx context.Context, serviceCategoryId string) error {
	actx := auth.AuthConText(ctx)
	err := s.store.ServiceCategories().Delete(actx, serviceCategoryId)
	if err != nil {
		ulog.Errorln(ctx, "delete service category err", err)
		return err
	}

	return nil
}
