package stripe

import (
	"context"
	"pebble/internal/webapp/models"
	"pebble/internal/webapp/models/sqlserver"
	"pebble/internal/webapp/services"
	"pebble/internal/webapp/types"
	commontypes "pebble/internal/webapp/types/common"
	"pebble/internal/webapp/types/stripe"
	"pebble/pkg/auth"
	"pebble/pkg/paystream"
	"pebble/pkg/udb"
	"pebble/pkg/ulog"
)

type settingSvc struct {
	store models.IStore
}

func SettingSvc() *settingSvc {
	return &settingSvc{
		store: sqlserver.NewStore(),
	}
}

func (s *settingSvc) Get(ctx context.Context) (*stripe.QueryStripeSettingRes, error) {
	actx := auth.AuthConText(ctx)

	// location, err := services.LocationSvc().QueryLocation(ctx)
	// if err != nil {
	// 	return nil, err
	// }
	tenant, err := services.TenantSvc().QueryTenant(ctx)
	if err != nil {
		return nil, err
	}

	setting, err := s.store.StripeSettings().Get(actx)
	if err != nil && !udb.IsRecordNotFound(err) {
		return nil, err
	}

	if udb.IsRecordNotFound(err) {
		return &stripe.QueryStripeSettingRes{
			StripeSettingEntity: stripe.StripeSettingEntity{
				TenantId:   actx.TenantId,
				LocationId: actx.LocationId,
			},
		}, nil
	}

	stripeStatus, err := paystream.GetStripeConnectStatus(tenant.Id)
	if err != nil {
		ulog.Errorln(ctx, "GetStripeConnectStatus error:", err)
		return nil, err
	}

	return &stripe.QueryStripeSettingRes{
		StripeSettingEntity: setting.StripeSettingEntity,
		Connected:           stripeStatus.PaymentsEnabled == 1 && stripeStatus.PayoutsEnabled == 1,
		PayoutsEnabled:      stripeStatus.PaymentsEnabled == 1,
		ChargesEnabled:      stripeStatus.PayoutsEnabled == 1,
	}, nil
}

func (s *settingSvc) Create(ctx context.Context, data stripe.StripeSettingEntity) (*stripe.StripeSetting, error) {
	return s.store.StripeSettings().Create(auth.AuthConText(ctx), data)
}

func (s *settingSvc) Update(ctx context.Context, data stripe.UpdateStripeSettingReq) error {
	return s.store.StripeSettings().Update(auth.AuthConText(ctx), data)
}

func (s *settingSvc) ConnectPaystream(ctx context.Context, tenant types.Tenant) (*stripe.StripeSetting, error) {
	actx := auth.AuthConText(ctx)

	balance, err := paystream.CreateStripeBalance(paystream.CreateStripeBalanceReq{
		Email:        tenant.Email,
		Country:      tenant.Country,
		Currency:     tenant.Currency,
		BusinessName: tenant.Name,
		BusinessId:   tenant.Id,
	})
	if err != nil {
		return nil, err
	}

	setting, err := s.Create(ctx, stripe.StripeSettingEntity{
		TenantId:        actx.TenantId,
		LocationId:      actx.LocationId,
		StripeAccountId: balance.StripeUserId,
		FeePayer:        commontypes.FeePayerCustomer,
	})
	if err != nil {
		return nil, err
	}

	return setting, nil
}

func (s *settingSvc) GetConnectLink(ctx context.Context) (*stripe.GetConnectLinkRes, error) {
	tenant, err := services.TenantSvc().QueryTenant(ctx)
	if err != nil {
		return nil, err
	}

	setting, err := s.Get(ctx)
	if err != nil {
		return nil, err
	}
	if setting.StripeAccountId == "" {
		_, err = s.ConnectPaystream(ctx, tenant.Tenant)
		if err != nil {
			return nil, err
		}
	}

	ret, err := paystream.GetStripeConnectLink(tenant.Id)
	if err != nil {
		ulog.Errorln(ctx, "GetStripeConnectLink error:", err)
		return nil, err
	}

	return &stripe.GetConnectLinkRes{
		Link: ret.URL,
	}, nil
}
