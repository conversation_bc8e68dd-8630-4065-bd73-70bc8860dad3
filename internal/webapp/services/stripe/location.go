package stripe

import (
	"context"
	"pebble/internal/webapp/models"
	"pebble/internal/webapp/models/sqlserver"
	"pebble/internal/webapp/types/stripe"
	"pebble/pkg/auth"
	"pebble/pkg/paystream"
	"pebble/pkg/ulog"
)

type locationSvc struct {
	store models.IStore
}

func LocationSvc() *locationSvc {
	return &locationSvc{
		store: sqlserver.NewStore(),
	}
}

func (s *locationSvc) List(ctx context.Context) ([]stripe.StripeLocations, error) {
	return s.store.StripeLocations().List(auth.AuthConText(ctx))
}

func (s *locationSvc) Create(ctx context.Context, data stripe.StripeLocationsEntity) (*stripe.StripeLocations, error) {
	stripeSetting, err := SettingSvc().Get(ctx)
	if err != nil {
		return nil, err
	}
	location, err := paystream.CreateLocation(paystream.CreateLocationReq{
		StripeAccount: stripeSetting.StripeAccountId,
		Name:          data.Name,
		Address1:      data.Address1,
		City:          data.City,
		State:         data.State,
		Postcode:      data.Zipcode,
		Country:       data.Country,
	})
	if err != nil {
		ulog.Errorln(ctx, "create location error", err)
		return nil, err
	}
	ulog.Infoln(ctx, "location: ", location)

	data.StripeLocationId = location.Id
	data.StripeAccountId = stripeSetting.StripeAccountId

	return s.store.StripeLocations().Create(auth.AuthConText(ctx), data)
}

func (s *locationSvc) GetByStripeLocationId(ctx context.Context, stripeLocationId string) (*stripe.StripeLocations, error) {
	return s.store.StripeLocations().GetByStripeLocationId(auth.AuthConText(ctx), stripeLocationId)
}

func (s *locationSvc) GetByStripeLocationIds(ctx context.Context, stripeLocationIds []string) ([]stripe.StripeLocations, error) {
	return s.store.StripeLocations().GetByStripeLocationIds(auth.AuthConText(ctx), stripeLocationIds)
}
