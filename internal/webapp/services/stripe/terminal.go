package stripe

import (
	"context"
	"errors"
	"pebble/internal/webapp/models"
	"pebble/internal/webapp/models/sqlserver"
	"pebble/internal/webapp/services"
	appointmentservice "pebble/internal/webapp/services/appointment"
	appointmenttypes "pebble/internal/webapp/types/appointment"
	commontypes "pebble/internal/webapp/types/common"
	stripetypes "pebble/internal/webapp/types/stripe"
	"pebble/pkg/auth"
	"pebble/pkg/paystream"
	"pebble/pkg/udb"
	"pebble/pkg/uerror"
	"pebble/pkg/ulog"

	"github.com/stripe/stripe-go/v80"
)

type terminalSvc struct {
	store models.IStore
}

func TerminalSvc() *terminalSvc {
	return &terminalSvc{
		store: sqlserver.NewStore(),
	}
}

func (s *terminalSvc) ReaderSucceededEvent(ctx context.Context, event stripe.Event) error {
	// todo
	return nil
}

func (s *terminalSvc) ReaderFailedEvent(ctx context.Context, event stripe.Event) error {
	// todo
	return nil
}

func (s *terminalSvc) RegisterStripeReader(ctx context.Context, req stripetypes.RegisterStripeReaderReq) error {
	stripeLocation, err := LocationSvc().GetByStripeLocationId(ctx, req.StripeLocationId)
	if err != nil {
		return err
	}

	setting, err := SettingSvc().Get(ctx)
	if err != nil {
		return err
	}

	ret, err := paystream.RegisterReader(paystream.RegisterReaderReq{
		StripeAccount:    setting.StripeAccountId,
		LocationId:       stripeLocation.StripeLocationId,
		RegistrationCode: req.RegistrationCode,
		Name:             req.Name,
	})
	if err != nil {
		ulog.Errorln(ctx, "RegisterReader error:", err)
		return err
	}

	actx := auth.AuthConText(ctx)

	_, err = s.store.StripeTerminals().Create(actx, stripetypes.StripeTerminalEntity{
		Name:               req.Name,
		StripeLocationId:   req.StripeLocationId,
		StripeLocationName: stripeLocation.Name,
		Code:               req.RegistrationCode,
		DeviceType:         ret.DeviceType,
		SerialNumber:       ret.SerialNumber,
		ReaderId:           ret.ReaderId,
	})
	if err != nil {
		return err
	}

	return nil
}

func (s *terminalSvc) QueryStripeReaders(ctx context.Context, params stripetypes.QueryStripeReadersParams) ([]stripetypes.StripeTerminal, error) {
	actx := auth.AuthConText(ctx)

	terminals, err := s.store.StripeTerminals().List(actx)
	if err != nil {
		return nil, err
	}
	var stripeLocationIds []string
	for _, terminal := range terminals {
		stripeLocationIds = append(stripeLocationIds, terminal.StripeLocationId)
	}

	stripeLocations, err := LocationSvc().GetByStripeLocationIds(ctx, stripeLocationIds)
	if err != nil {
		return nil, err
	}
	stripeLocationsMap := make(map[string]stripetypes.StripeLocations)
	for _, location := range stripeLocations {
		stripeLocationsMap[location.StripeLocationId] = location
	}

	stripeSetting, err := SettingSvc().Get(ctx)
	if err != nil {
		return nil, err
	}

	readerList, err := paystream.GetReaderList(stripeSetting.StripeAccountId)
	if err != nil {
		ulog.Errorln(ctx, "GetReaderList error:", err)
		return nil, err
	}
	readerStatusMap := make(map[string]string)
	for _, reader := range readerList.Data {
		readerStatusMap[reader.ID] = string(reader.Status)
	}

	onlineReaders := make([]stripetypes.StripeTerminal, 0, len(terminals))
	offlineReaders := make([]stripetypes.StripeTerminal, 0, len(terminals))
	for i, item := range terminals {
		location := stripeLocationsMap[item.StripeLocationId]
		terminals[i].StripeLocationName = location.Name

		terminals[i].OnlineStatus = stripetypes.OnlineStatusOffline
		if readerStatusMap[item.ReaderId] == "online" {
			terminals[i].OnlineStatus = stripetypes.OnlineStatusOnline
			onlineReaders = append(onlineReaders, terminals[i])
		} else {
			offlineReaders = append(offlineReaders, terminals[i])
		}
	}

	if params.OnlineStatus != stripetypes.OnlineStatusOffline {
		onlineReaders = append(onlineReaders, offlineReaders...)
	}

	return onlineReaders, nil
}

func (s *terminalSvc) StripeReaderIntent(ctx context.Context, req stripetypes.StripeReaderIntentReq) error {
	actx := auth.AuthConText(ctx)
	location, err := services.LocationSvc().QueryLocation(ctx)
	if err != nil {
		return err
	}

	var balance appointmenttypes.Bills
	switch req.OrderType {
	case commontypes.OrderTypeAppointment:
		appointment, err := appointmentservice.AppointmentSvc().Get(ctx, req.OrderId)
		if udb.IsRecordNotFound(err) {
			return uerror.ErrAppointmentNotFound
		}
		if err != nil {
			return err
		}
		if req.TargetId != "" {
			bills, err := appointmentservice.AppointmentSvc().AABills(ctx, appointmenttypes.BillReq{
				CurrencySymbol:    location.CurrencySymbol,
				Appointment:       *appointment,
				PaymentMethodName: commontypes.PaymentMethodStripeTerminal,
			})
			if err != nil {
				return err
			}
			for _, bill := range bills.Items {
				if bill.ClientId == req.TargetId {
					balance = bill
					break
				}
			}
		} else {
			bills, err := appointmentservice.AppointmentSvc().Bills(ctx, appointmenttypes.BillReq{
				CurrencySymbol:    location.CurrencySymbol,
				Appointment:       *appointment,
				PaymentMethodName: commontypes.PaymentMethodStripeTerminal,
			})
			if err != nil {
				return err
			}
			balance = bills
		}
	}

	if balance.AmountUnpaid <= 0 {
		return nil
	}
	if balance.AmountUnpaid > req.Amount {
		return errors.New("balance is not enough")
	}
	if balance.AmountFee > req.Fee {
		return errors.New("fee is not match")
	}
	if balance.AmountTax > req.Tax {
		return errors.New("tax is not match")
	}
	if balance.AmountTip > req.Tip {
		return errors.New("tip is not match")
	}

	setting, err := SettingSvc().Get(ctx)
	if err != nil {
		return err
	}

	intent, err := paystream.CreatePaymentIntentWithAccount(paystream.CreatePaymentIntentWithAccountReq{
		StripeUserId:   setting.StripeAccountId,
		Amount:         req.Amount,
		TerminalType:   paystream.TerminalType,
		ApplicationFee: balance.Fee.ApplicationFee,
		Currency:       location.Currency,
		// StripeCustomerId: "",
	})
	if err != nil {
		ulog.Errorln(ctx, "Get error:", err)
		return err
	}

	_, err = s.store.StripePaymentIntents().Create(actx, stripetypes.StripePaymentIntentEntity{
		StripePaymentIntentId: intent.ID,
		StripeAccountId:       setting.StripeAccountId,
		Amount:                req.Amount,
		AmountFee:             balance.AmountFee,
		Currency:              string(intent.Currency),
		TenantId:              actx.TenantId,
		LocationId:            actx.LocationId,
		OrderId:               req.OrderId,
		OrderType:             req.OrderType,
		ClientId:              req.TargetId,
		AccountId:             actx.AccountId,
		StripeCustomerId:      "",
		Status:                stripetypes.PaymentIntentStatusPending,
		Metadata: stripetypes.Metadata{
			Amount:                balance.Fee.Amount,
			OriginalAmount:        balance.Fee.OriginalAmount,
			ApplicationFee:        balance.Fee.ApplicationFee,
			ApplicationFeePercent: balance.Fee.ApplicationFeePercent,
			PaymentFee:            balance.Fee.PaymentFee,
			PaymentFeePercent:     balance.Fee.ApplicationFeePercent,
			PaymentBaseFee:        balance.Fee.PaymentBaseFee,
		},
	})
	if err != nil {
		return err
	}

	_, err = paystream.ProcessPaymentIntent(paystream.ProcessPaymentIntentReq{
		ReaderId:        req.ReaderID,
		PaymentIntentId: intent.ID,
		StripeAccount:   setting.StripeAccountId,
	})
	if err != nil {
		ulog.Errorln(ctx, "ProcessPaymentIntent error:", err)
		return err
	}

	return nil
}

func (s *terminalSvc) CancelStripeReaderAction(ctx context.Context, req stripetypes.CancelStripeReaderActionReq) error {
	setting, err := SettingSvc().Get(ctx)
	if err != nil {
		return err
	}

	err = paystream.CancelReaderAction(paystream.CancelReaderActionReq{
		ReaderId:      req.ReaderID,
		StripeAccount: setting.StripeAccountId,
	})
	if err != nil {
		ulog.Errorln(ctx, "CancelReaderAction error:", err)
		return err
	}

	return nil
}
