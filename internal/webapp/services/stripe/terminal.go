package stripe

import (
	"context"
	"errors"
	"pebble/internal/webapp/models"
	"pebble/internal/webapp/models/sqlserver"
	"pebble/internal/webapp/services"
	appointmentservice "pebble/internal/webapp/services/appointment"
	"pebble/internal/webapp/services/paymentservice"
	appointmenttypes "pebble/internal/webapp/types/appointment"
	commontypes "pebble/internal/webapp/types/common"
	paymenttypes "pebble/internal/webapp/types/payment"
	stripetypes "pebble/internal/webapp/types/stripe"
	"pebble/internal/webapp/websocket"
	"pebble/pkg/auth"
	"pebble/pkg/paystream"
	"pebble/pkg/udb"
	"pebble/pkg/uerror"
	"pebble/pkg/ulog"
	"pebble/pkg/util"

	"github.com/stripe/stripe-go/v80"
)

type terminalSvc struct {
	store models.IStore
}

func TerminalSvc() *terminalSvc {
	return &terminalSvc{
		store: sqlserver.NewStore(),
	}
}

func (s *terminalSvc) ReaderSucceededEvent(ctx context.Context, event stripe.Event) error {
	var data stripe.TerminalReader
	err := util.DeepCopy(event.Data.Raw, &data)
	if err != nil {
		return err
	}
	var intentId string
	if data.Action == nil || data.Action.ProcessPaymentIntent == nil || data.Action.ProcessPaymentIntent.PaymentIntent == nil {
		return nil
	}

	intentId = data.Action.ProcessPaymentIntent.PaymentIntent.ID
	intent, err := s.store.StripePaymentIntents().AdminGetByIntentId(ctx, intentId)
	if err != nil {
		return err
	}
	// if intent.Status == stripetypes.PaymentIntentStatusSucceeded {
	// 	return nil
	// }

	ctx = auth.ContextWithExt(ctx, auth.Ext{
		TenantId:   intent.TenantId,
		LocationId: intent.LocationId,
		AccountId:  intent.AccountId,
	})
	actx := auth.AuthConText(ctx)
	setting, err := s.store.StripeSettings().Get(actx)
	if err != nil {
		return err
	}

	paymentIntent, err := paystream.GetPaymentIntent(paystream.GetPaymentIntentReq{
		StripePaymentIntentId: intentId,
		StripeAccount:         setting.StripeAccountId,
	})
	if err != nil {
		return err
	}
	ulog.Infoln(ctx, "GetPaymentIntent ret:", paymentIntent)

	err = s.store.StripePaymentIntents().AdminUpdateIntent(ctx, intentId, stripetypes.UpdateStripePaymentIntentReq{
		Status:    util.Ptr(stripetypes.PaymentIntentStatusSucceeded),
		Amount:    util.Ptr(paymentIntent.Amount),
		AmountTip: util.Ptr(paymentIntent.TipAmount),
	})
	if err != nil {
		return err
	}

	err = paymentservice.PaymentSvc().Payments(ctx, paymenttypes.PaymentsReq{
		TargetId:  intent.Metadata.TargetId,
		MethodId:  intent.Metadata.PaymentMethodId,
		OrderId:   intent.OrderId,
		OrderType: intent.OrderType,
		Amount:    paymentIntent.Amount,
		Tip:       intent.Metadata.AmountTip + paymentIntent.TipAmount, // todo 处理多出来的 tip
		Fee:       intent.Metadata.PaymentFee,
		Tax:       intent.Metadata.AmountTax,
		FeePayer:  intent.Metadata.FeePayer,
	})
	if err != nil {
		return err
	}

	appt, err := s.store.Appointments().Get(actx, intent.OrderId)
	if err != nil {
		return err
	}
	client, err := s.store.Clients().GetClientById(actx, appt.ClientId)
	if err != nil {
		return err
	}
	tentent, err := s.store.Locations().GetLocation(actx)
	if err != nil {
		return err
	}
	location, err := s.store.Locations().GetLocation(actx)
	if err != nil {
		return err
	}

	account, err := services.StaffSvc().QueryStaff(ctx, actx.AccountId)
	if err != nil {
		return err
	}

	err = paystream.UpdateStripeBalanceAndPaymentLogs(paystream.UpdateStripeBalanceAndPaymentLogsReq{
		Amount:                paymentIntent.Amount,
		PaymentId:             intent.StripePaymentIntentId,
		OrderId:               appt.Id,
		OrderType:             int8(intent.OrderType),
		CustomerId:            client.Id,
		ApplicationFee:        intent.Metadata.ApplicationFee,
		ApplicationFeePercent: intent.Metadata.ApplicationFeePercent,
		PaymentFee:            intent.Metadata.PaymentFee,
		PaymentFeePercent:     intent.Metadata.PaymentFeePercent,
		PaymentBaseFee:        intent.Metadata.PaymentBaseFee,
		StripeUserId:          setting.StripeAccountId,
		BusinessId:            tentent.Id,
		LocationId:            location.Id,
		LastAccountId:         account.Id,
	})
	if err != nil {
		ulog.Errorln(ctx, "UpdateStripeBalanceAndPaymentLogs error:", err)
		return err
	}

	msg := struct {
		TenantId      string                          `json:"tenant_id"`
		LocationId    string                          `json:"location_id"`
		ClientId      string                          `json:"client_id"`
		TargetId      string                          `json:"target_id"`
		OrderId       string                          `json:"order_id"`
		OrderType     commontypes.OrderType           `json:"order_type"`
		Amount        int64                           `json:"amount"`
		Tip           int64                           `json:"tip"`
		Fee           int64                           `json:"fee"`
		Tax           int64                           `json:"tax"`
		FeePayer      commontypes.FeePayer            `json:"fee_payer"`
		PaymentStatus commontypes.PaymentRecordStatus `json:"payment_status"`
	}{
		TenantId:      intent.TenantId,
		LocationId:    intent.LocationId,
		ClientId:      intent.ClientId,
		TargetId:      intent.Metadata.TargetId,
		OrderId:       intent.OrderId,
		OrderType:     intent.OrderType,
		Amount:        paymentIntent.Amount,
		Tip:           intent.Metadata.AmountTip + paymentIntent.TipAmount,
		Fee:           intent.Metadata.PaymentFee,
		Tax:           intent.Metadata.AmountTax,
		FeePayer:      intent.Metadata.FeePayer,
		PaymentStatus: commontypes.PaymentStatusPaid,
	}

	err = websocket.GetWebSocketPusher().PushStriepTerminalSucceeded(intent.TenantId, intent.LocationId, intent.AccountId, msg)
	if err != nil {
		return err
	}

	return nil
}

func (s *terminalSvc) ReaderFailedEvent(ctx context.Context, event stripe.Event) error {
	var data stripe.TerminalReader
	err := util.DeepCopy(event.Data.Raw, &data)
	if err != nil {
		return err
	}
	var intentId string
	if data.Action == nil || data.Action.ProcessPaymentIntent == nil || data.Action.ProcessPaymentIntent.PaymentIntent == nil {
		return nil
	}

	intentId = data.Action.ProcessPaymentIntent.PaymentIntent.ID
	intent, err := s.store.StripePaymentIntents().AdminGetByIntentId(ctx, intentId)
	if err != nil {
		return err
	}

	err = s.store.StripePaymentIntents().AdminUpdateIntent(ctx, intentId, stripetypes.UpdateStripePaymentIntentReq{
		Status: util.Ptr(stripetypes.PaymentIntentStatusFailed),
	})
	if err != nil {
		return err
	}

	var errorCode string
	if data.Action != nil &&
		data.Action.ProcessPaymentIntent != nil &&
		data.Action.ProcessPaymentIntent.PaymentIntent != nil &&
		data.Action.ProcessPaymentIntent.PaymentIntent.LastPaymentError != nil {
		errorCode = string(data.Action.ProcessPaymentIntent.PaymentIntent.LastPaymentError.Code)
	}
	var errMessage string
	if data.Action != nil &&
		data.Action.ProcessPaymentIntent != nil &&
		data.Action.ProcessPaymentIntent.PaymentIntent.LastPaymentError != nil {
		errMessage = data.Action.ProcessPaymentIntent.PaymentIntent.LastPaymentError.Msg
	}

	msg := struct {
		TenantId      string                          `json:"tenant_id"`
		LocationId    string                          `json:"location_id"`
		ClientId      string                          `json:"client_id"`
		TargetId      string                          `json:"target_id"`
		OrderId       string                          `json:"order_id"`
		OrderType     commontypes.OrderType           `json:"order_type"`
		Amount        int64                           `json:"amount"`
		Tip           int64                           `json:"tip"`
		Fee           int64                           `json:"fee"`
		Tax           int64                           `json:"tax"`
		FeePayer      commontypes.FeePayer            `json:"fee_payer"`
		PaymentStatus commontypes.PaymentRecordStatus `json:"payment_status"`
		ErrorCode     string                          `json:"error_code"`
		ErrorMessage  string                          `json:"error_message"`
	}{
		TenantId:      intent.TenantId,
		LocationId:    intent.LocationId,
		ClientId:      intent.ClientId,
		TargetId:      intent.Metadata.TargetId,
		OrderId:       intent.OrderId,
		OrderType:     intent.OrderType,
		Amount:        intent.Amount,
		Tip:           intent.Metadata.AmountTip,
		Fee:           intent.Metadata.PaymentFee,
		Tax:           intent.Metadata.AmountTax,
		FeePayer:      intent.Metadata.FeePayer,
		PaymentStatus: commontypes.PaymentStatusFailed,
		ErrorCode:     errorCode,
		ErrorMessage:  errMessage,
	}

	err = websocket.GetWebSocketPusher().PushStriepTerminalSucceeded(intent.TenantId, intent.LocationId, intent.AccountId, msg)
	if err != nil {
		return err
	}

	return nil
}

func (s *terminalSvc) RegisterStripeReader(ctx context.Context, req stripetypes.RegisterStripeReaderReq) error {
	stripeLocation, err := LocationSvc().GetByStripeLocationId(ctx, req.StripeLocationId)
	if err != nil {
		return err
	}

	setting, err := SettingSvc().Get(ctx)
	if err != nil {
		return err
	}

	ret, err := paystream.RegisterReader(paystream.RegisterReaderReq{
		StripeAccount:    setting.StripeAccountId,
		LocationId:       stripeLocation.StripeLocationId,
		RegistrationCode: req.RegistrationCode,
		Name:             req.Name,
	})
	if err != nil {
		ulog.Errorln(ctx, "RegisterReader error:", err)
		return err
	}

	actx := auth.AuthConText(ctx)

	_, err = s.store.StripeTerminals().Create(actx, stripetypes.StripeTerminalEntity{
		Name:               req.Name,
		StripeLocationId:   req.StripeLocationId,
		StripeLocationName: stripeLocation.Name,
		Code:               req.RegistrationCode,
		DeviceType:         ret.DeviceType,
		SerialNumber:       ret.SerialNumber,
		ReaderId:           ret.ReaderId,
	})
	if err != nil {
		return err
	}

	return nil
}

func (s *terminalSvc) QueryStripeReaders(ctx context.Context, params stripetypes.QueryStripeReadersParams) ([]stripetypes.StripeTerminal, error) {
	actx := auth.AuthConText(ctx)

	terminals, err := s.store.StripeTerminals().List(actx)
	if err != nil {
		return nil, err
	}
	var stripeLocationIds []string
	for _, terminal := range terminals {
		stripeLocationIds = append(stripeLocationIds, terminal.StripeLocationId)
	}

	stripeLocations, err := LocationSvc().GetByStripeLocationIds(ctx, stripeLocationIds)
	if err != nil {
		return nil, err
	}
	stripeLocationsMap := make(map[string]stripetypes.StripeLocations)
	for _, location := range stripeLocations {
		stripeLocationsMap[location.StripeLocationId] = location
	}

	stripeSetting, err := SettingSvc().Get(ctx)
	if err != nil {
		return nil, err
	}

	readerList, err := paystream.GetReaderList(stripeSetting.StripeAccountId)
	if err != nil {
		ulog.Errorln(ctx, "GetReaderList error:", err)
		return nil, err
	}
	readerStatusMap := make(map[string]string)
	for _, reader := range readerList.Data {
		readerStatusMap[reader.ID] = string(reader.Status)
	}

	onlineReaders := make([]stripetypes.StripeTerminal, 0, len(terminals))
	offlineReaders := make([]stripetypes.StripeTerminal, 0, len(terminals))
	for i, item := range terminals {
		location := stripeLocationsMap[item.StripeLocationId]
		terminals[i].StripeLocationName = location.Name

		terminals[i].OnlineStatus = stripetypes.OnlineStatusOffline
		if readerStatusMap[item.ReaderId] == "online" {
			terminals[i].OnlineStatus = stripetypes.OnlineStatusOnline
			onlineReaders = append(onlineReaders, terminals[i])
		} else {
			offlineReaders = append(offlineReaders, terminals[i])
		}
	}

	if params.OnlineStatus != stripetypes.OnlineStatusOffline {
		onlineReaders = append(onlineReaders, offlineReaders...)
	}

	return onlineReaders, nil
}

func (s *terminalSvc) StripeReaderIntent(ctx context.Context, req stripetypes.StripeReaderIntentReq) error {
	actx := auth.AuthConText(ctx)

	method, err := services.PaymentMethodSvc().GetByName(ctx, commontypes.PaymentMethodStripeTerminal)
	if err != nil {
		return err
	}
	location, err := services.LocationSvc().QueryLocation(ctx)
	if err != nil {
		return err
	}

	var balance appointmenttypes.Bills
	switch req.OrderType {
	case commontypes.OrderTypeAppointment:
		appointment, err := appointmentservice.AppointmentSvc().Get(ctx, req.OrderId)
		if udb.IsRecordNotFound(err) {
			return uerror.ErrAppointmentNotFound
		}
		if err != nil {
			return err
		}
		if req.TargetId != "" {
			bills, err := appointmentservice.AppointmentSvc().AABills(ctx, appointmenttypes.BillReq{
				CurrencySymbol:    location.CurrencySymbol,
				Appointment:       *appointment,
				PaymentMethodName: commontypes.PaymentMethodStripeTerminal,
			})
			if err != nil {
				return err
			}
			for _, bill := range bills.Items {
				if bill.ClientId == req.TargetId {
					balance = bill
					break
				}
			}
		} else {
			bills, err := appointmentservice.AppointmentSvc().Bills(ctx, appointmenttypes.BillReq{
				CurrencySymbol:    location.CurrencySymbol,
				Appointment:       *appointment,
				PaymentMethodName: commontypes.PaymentMethodStripeTerminal,
			})
			if err != nil {
				return err
			}
			balance = bills
		}
	}

	if balance.AmountUnpaid <= 0 {
		return nil
	}
	if balance.AmountUnpaid > req.Amount {
		return errors.New("balance is not enough")
	}
	if balance.AmountFee > req.Fee {
		return errors.New("fee is not match")
	}
	if balance.AmountTax > req.Tax {
		return errors.New("tax is not match")
	}
	if balance.AmountTip > req.Tip {
		return errors.New("tip is not match")
	}

	setting, err := SettingSvc().Get(ctx)
	if err != nil {
		return err
	}

	intent, err := paystream.CreatePaymentIntentWithAccount(paystream.CreatePaymentIntentWithAccountReq{
		StripeUserId:   setting.StripeAccountId,
		Amount:         req.Amount,
		TerminalType:   paystream.TerminalType,
		ApplicationFee: balance.Fee.ApplicationFee,
		Currency:       location.Currency,
		// StripeCustomerId: "",
	})
	if err != nil {
		ulog.Errorln(ctx, "Get error:", err)
		return err
	}

	_, err = s.store.StripePaymentIntents().Create(actx, stripetypes.StripePaymentIntentEntity{
		StripePaymentIntentId: intent.ID,
		StripeAccountId:       setting.StripeAccountId,
		Amount:                req.Amount,
		AmountFee:             balance.AmountFee,
		Currency:              string(intent.Currency),
		TenantId:              actx.TenantId,
		LocationId:            actx.LocationId,
		OrderId:               req.OrderId,
		OrderType:             req.OrderType,
		ClientId:              req.TargetId,
		AccountId:             actx.AccountId,
		StripeCustomerId:      "",
		Status:                stripetypes.PaymentIntentStatusPending,
		Metadata: stripetypes.Metadata{
			PaymentMethodId: method.MethodId,
			TargetId:        req.TargetId,
			BaseBills:       balance.BaseBills,
			Fee:             balance.Fee,
		},
	})
	if err != nil {
		return err
	}

	_, err = paystream.ProcessPaymentIntent(paystream.ProcessPaymentIntentReq{
		ReaderId:        req.ReaderID,
		PaymentIntentId: intent.ID,
		StripeAccount:   setting.StripeAccountId,
	})
	if err != nil {
		ulog.Errorln(ctx, "ProcessPaymentIntent error:", err)
		return err
	}

	return nil
}

func (s *terminalSvc) CancelStripeReaderAction(ctx context.Context, req stripetypes.CancelStripeReaderActionReq) error {
	setting, err := SettingSvc().Get(ctx)
	if err != nil {
		return err
	}

	err = paystream.CancelReaderAction(paystream.CancelReaderActionReq{
		ReaderId:      req.ReaderID,
		StripeAccount: setting.StripeAccountId,
	})
	if err != nil {
		ulog.Errorln(ctx, "CancelReaderAction error:", err)
		return err
	}

	return nil
}
