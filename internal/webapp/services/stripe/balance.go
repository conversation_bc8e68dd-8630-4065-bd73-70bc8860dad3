package stripe

import (
	"context"
	"errors"
	"pebble/internal/webapp/models"
	"pebble/internal/webapp/models/sqlserver"
	"pebble/internal/webapp/services"
	stripetypes "pebble/internal/webapp/types/stripe"
	"pebble/pkg/auth"
	"pebble/pkg/paystream"
	"pebble/pkg/ulog"
)

type balanceSvc struct {
	store models.IStore
}

func BalanceSvc() *balanceSvc {
	return &balanceSvc{
		store: sqlserver.NewStore(),
	}
}

func (s *balanceSvc) GetBalance(ctx context.Context) (*paystream.StripeBalancesEntity, error) {
	tentent, err := services.TenantSvc().QueryTenant(ctx)
	if err != nil {
		return nil, err
	}

	balance, err := paystream.GetStripeBalanceByBusinessId(tentent.Id)
	if err != nil {
		ulog.Errorln(ctx, "GetStripeBalanceByBusinessId error:", err)
		return nil, err
	}

	return &balance.StripeBalancesEntity, nil
}

func (s *balanceSvc) GetBalancePayoutLogs(ctx context.Context, req stripetypes.GetStripeBalancePayoutLogsReq) ([]stripetypes.GetStripePayoutLogsRes, int64, error) {
	tentent, err := services.TenantSvc().QueryTenant(ctx)
	if err != nil {
		ulog.Errorln(ctx, "query tenant error:", err)
		return nil, 0, err
	}

	location, err := services.LocationSvc().QueryLocation(ctx)
	if err != nil {
		return nil, 0, err
	}

	logs, total, err := paystream.GetStripeBalancePayoutLogs(paystream.GetStripeBalancePayoutLogsReq{
		BusinessId: tentent.Id,
		Page:       req.Page,
		PageSize:   req.PageSize,
	})
	if err != nil {
		return nil, 0, err
	}

	res := make([]stripetypes.GetStripePayoutLogsRes, len(logs))
	for i, item := range logs {
		res[i].GetStripeBalancePayoutLogsRes = item
		res[i].CurrencySymbol = location.CurrencySymbol
	}

	return res, total, nil
}

func (s *balanceSvc) GetBalancePaymentLogs(ctx context.Context, req stripetypes.GetStripeBalancePaymentLogsReq) ([]stripetypes.GetStripeBalancePaymentLogsRes, int64, error) {
	tentent, err := services.TenantSvc().QueryTenant(ctx)
	if err != nil {
		return nil, 0, err
	}
	location, err := services.LocationSvc().QueryLocation(ctx)
	if err != nil {
		return nil, 0, err
	}

	logs, total, err := paystream.GetStripeBalancePaymentLogs(paystream.GetStripeBalancePaymentLogsReq{
		BusinessId: tentent.Id,
		Page:       req.Page,
		PageSize:   req.PageSize,
	})
	if err != nil {
		return nil, 0, err
	}

	res := make([]stripetypes.GetStripeBalancePaymentLogsRes, len(logs))
	for i, item := range logs {
		res[i].GetStripeBalancePaymentLogsRes = item
		res[i].CurrencySymbol = location.CurrencySymbol
	}

	return res, total, nil
}

func (s *balanceSvc) StripeBalancePayout(ctx context.Context, req stripetypes.StripeBalancePayoutReq) error {
	tentent, err := services.TenantSvc().QueryTenant(ctx)
	if err != nil {
		return err
	}
	location, err := services.LocationSvc().QueryLocation(ctx)
	if err != nil {
		return err
	}

	if req.Amount <= 0 {
		ulog.Error(ctx, "amount must be greater than 0")
		return errors.New("amount must be greater than 0")
	}

	actx := auth.AuthConText(ctx)

	account, err := services.StaffSvc().QueryStaff(ctx, actx.AccountId)
	if err != nil {
		return err
	}

	setting, err := SettingSvc().Get(ctx)
	if err != nil {
		return err
	}

	err = paystream.StripeBalancePayout(paystream.StripeBalancePayoutReq{
		Amount:        req.Amount,
		BusinessId:    tentent.Id,
		LocationId:    location.Id,
		LastAccountId: account.Id,
		Currency:      location.Currency,
		StripeUserId:  setting.StripeAccountId,
	})
	if err != nil {
		return err
	}

	return nil
}

func (s *balanceSvc) UpdateStripePayoutSetting(ctx context.Context, req stripetypes.UpdateStripePayoutSettingReq) error {
	setting, err := SettingSvc().Get(ctx)
	if err != nil {
		return err
	}

	tentent, err := services.TenantSvc().QueryTenant(ctx)
	if err != nil {
		return err
	}

	updateData := paystream.PayoutSchduleParams{
		Method:       req.Method,
		MethodParams: req.MethodParams,
		Timezone:     tentent.Timezone,
		PayoutFee:    setting.PayoutFee,
	}
	err = paystream.UpdatePayoutSchedule(tentent.Id, updateData)
	if err != nil {
		return err
	}

	return nil
}

func (s *balanceSvc) QueryStripePayoutSetting(ctx context.Context) (*stripetypes.QueryStripePayoutSettingRes, error) {
	tentent, err := services.TenantSvc().QueryTenant(ctx)
	if err != nil {
		return nil, err
	}

	item, err := paystream.GetPayoutSchedule(tentent.Id)
	if err != nil {
		return nil, err
	}

	return &stripetypes.QueryStripePayoutSettingRes{
		Method:       item.Method,
		MethodParams: item.MethodParams,
	}, nil
}
