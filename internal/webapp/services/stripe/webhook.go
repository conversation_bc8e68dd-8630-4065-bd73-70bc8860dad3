package stripe

import (
	"context"
	"pebble/internal/webapp/models"
	"pebble/internal/webapp/models/sqlserver"
	"pebble/pkg/ulog"
	"pebble/pkg/util"

	"github.com/stripe/stripe-go/v80"
)

type webhookSvc struct {
	store models.IStore
}

func WebhookSvc() *webhookSvc {
	return &webhookSvc{
		store: sqlserver.NewStore(),
	}
}

func (s *webhookSvc) HandleWebhook(ctx context.Context, data map[string]interface{}) error {
	var event stripe.Event
	err := util.DeepCopy(data, &event)
	if err != nil {
		return err
	}
	ulog.Infoln(ctx, "event_type:", event.Type)

	switch event.Type {
	case "payment_method.detached":
	case "customer.deleted":
	case "terminal.reader.action_succeeded":
		err = TerminalSvc().ReaderSucceededEvent(ctx, event)
	case "terminal.reader.action_failed":
		err = TerminalSvc().ReaderFailedEvent(ctx, event)
	default:
		ulog.Infoln(ctx, "event:", event)
	}
	if err != nil {
		ulog.Errorln(ctx, "handle webhook error:", err)
		return err
	}

	return nil
}
