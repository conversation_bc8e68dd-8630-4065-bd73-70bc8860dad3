package services

import (
	"context"
	"pebble/internal/webapp/models"
	"pebble/internal/webapp/models/sqlserver"
	tagservices "pebble/internal/webapp/services/tag"
	"pebble/internal/webapp/types"
	"pebble/pkg/auth"
	"pebble/pkg/ubcrypt"
	"pebble/pkg/udb"
	"pebble/pkg/uerror"
	"pebble/pkg/ulog"
	"pebble/pkg/util"

	"go.uber.org/zap"
)

type clientSvc struct {
	store models.IStore
}

func ClientSvc() *clientSvc {
	return &clientSvc{
		store: sqlserver.NewStore(),
	}
}

func (c *clientSvc) CreateWalkIn(ctx context.Context) (*types.Client, error) {
	client, err := c.store.Clients().CreateClient(auth.AuthConText(ctx), types.ClientEntity{
		FirstName: "walk",
		LastName:  "in",
		Status:    types.ClientStatusWalkIn,
		Source:    types.SourceWalkIn,
	})
	if err != nil {
		ulog.Errorln(ctx, "create walk in client err", err)
		return nil, err
	}

	return client, nil
}

func (c *clientSvc) CreateWalkInClients(ctx context.Context, count int) ([]types.Client, error) {
	clients := make([]types.ClientEntity, count)
	for i := 0; i < count; i++ {
		clients[i] = types.ClientEntity{
			FirstName: "walk",
			LastName:  "in",
			Status:    types.ClientStatusWalkIn,
			Source:    types.SourceWalkIn,
		}
	}

	return c.store.Clients().BatchCreateClients(auth.AuthConText(ctx), clients)
}

func (c *clientSvc) CreateClient(ctx context.Context, data types.CreateClientReq) (*types.Client, error) {
	req := types.ClientEntity{
		FirstName:   data.FirstName,
		LastName:    data.LastName,
		Email:       data.Email,
		PhoneNumber: data.PhoneNumber,
		Avatar:      data.Avatar,
		Gender:      data.Gender,
		Birthday:    data.Birthday,
		Status:      data.Status,
		Source:      data.Source,
	}
	if data.Password != "" {
		var err error
		req.PasswordHash, err = ubcrypt.HashPassword(data.Password)
		if err != nil {
			ulog.Errorln(ctx, "HashPassword error", err)
			return nil, err
		}
	}

	client, err := c.store.Clients().CreateClient(auth.AuthConText(ctx), req)
	if err != nil {
		ulog.Error(ctx, "CreateClient error", zap.Any("data", data), zap.Error(err))
		return nil, err
	}

	return client, nil
}

func (c *clientSvc) QueryClientById(ctx context.Context, clientId string) (*types.ClientDetails, error) {
	client, err := c.store.Clients().GetClientById(auth.AuthConText(ctx), clientId)
	if udb.IsRecordNotFound(err) {
		return nil, uerror.ErrClientNotFound
	}
	if err != nil {
		ulog.Error(ctx, "QueryClientById error", zap.Any("client_id", clientId), zap.Error(err))
		return nil, err
	}

	tags, err := tagservices.ClientTagSvc().QueryClientActiveTags(ctx, clientId)
	if err != nil {
		ulog.Error(ctx, "QueryClientActiveTags error", zap.Any("client_id", clientId), zap.Error(err))
		return nil, err
	}

	resp := &types.ClientDetails{
		Client: *client,
		Tags:   tags,
	}

	return resp, nil
}

func (c *clientSvc) QueryAllClients(ctx context.Context, params types.QueryClientParams) ([]types.ClientDetails, int64, error) {
	if params.Status == nil {
		params.Status = util.Ptr(types.ClientStatusActive)
	}
	list, total, err := c.store.Clients().GetClients(auth.AuthConText(ctx), params)
	if err != nil {
		ulog.Error(ctx, "QueryAllClients error", zap.Any("params", params), zap.Error(err))
		return nil, 0, err
	}

	var clientIds []string
	for _, client := range list {
		clientIds = append(clientIds, client.ClientId)
	}

	clientTagMap, err := tagservices.ClientTagSvc().QueryClientActiveTagsByClietIds(ctx, clientIds)
	if err != nil {
		ulog.Error(ctx, "QueryClientActiveTagsByClietIds error", zap.Any("clientIds", clientIds), zap.Error(err))
		return nil, 0, err
	}

	resp := make([]types.ClientDetails, 0, len(list))
	for _, client := range list {
		resp = append(resp, types.ClientDetails{
			Client: client,
			Tags:   util.NoNilSlice(clientTagMap[client.ClientId]),
		})
	}

	return resp, total, nil
}

func (c *clientSvc) UpdateClient(ctx context.Context, clientId string, data types.UpdateClientReq) error {
	err := c.store.Clients().UpdateClient(auth.AuthConText(ctx), clientId, data)
	if err != nil {
		ulog.Error(ctx, "UpdateClient error", zap.Any("client_id", clientId), zap.Any("data", data), zap.Error(err))
	}
	return err
}

func (c *clientSvc) DeleteClient(ctx context.Context, clientId string) error {
	err := c.store.Clients().DeleteClient(auth.AuthConText(ctx), clientId)
	if err != nil {
		ulog.Error(ctx, "DeleteClient error", zap.Any("client_id", clientId), zap.Error(err))
	}
	return err
}
