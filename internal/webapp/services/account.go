package services

import (
	"context"
	"pebble/internal/webapp/models"
	"pebble/internal/webapp/models/sqlserver"
	"pebble/internal/webapp/types"
	"pebble/pkg/auth"
	"pebble/pkg/ubcrypt"
	"pebble/pkg/udb"
	"pebble/pkg/uerror"
	"pebble/pkg/ulog"

	"go.uber.org/zap"
)

type accountSvc struct {
	store models.IStore
}

func AccountSvc() *accountSvc {
	return &accountSvc{
		store: sqlserver.NewStore(),
	}
}

func (a *accountSvc) QueryAccountAndCheckPassword(ctx context.Context, email, password string) (types.Account, error) {
	account, err := a.store.Accounts().AdminGetAccountByEmail(ctx, email)
	if udb.IsRecordNotFound(err) {
		return account, uerror.ErrAccountNotFound
	}
	if err != nil {
		ulog.Errorln(ctx, "query account err", zap.String("email", email))
		return account, err
	}
	if !ubcrypt.CheckPasswordHash(password, account.PasswordHash) {
		return account, uerror.ErrIncorrectPassword
	}

	return account, nil
}

func (a *accountSvc) AdminCreateAccount(ctx context.Context, data types.CreateAccountReq) (types.Account, error) {
	passwordHash, err := ubcrypt.HashPassword(data.Password)
	if err != nil {
		return types.Account{}, err
	}

	return a.store.Accounts().AdminCreateAccount(ctx, types.AccountEntity{
		PhoneNumber: data.PhoneNumber,
		// FirstName:    data.FirstName,
		// LastName:     data.LastName,
		Email:        data.Email,
		Source:       data.Source,
		PasswordHash: passwordHash,
	})
}

func (a *accountSvc) QueryAccountDetail(ctx context.Context) (types.AccountDetail, error) {
	var resp types.AccountDetail
	account, err := a.store.Accounts().GetAccount(auth.AuthConText(ctx))
	if err != nil {
		return types.AccountDetail{}, err
	}
	location, err := a.store.Locations().GetLocation(auth.AuthConText(ctx))
	if err != nil {
		return types.AccountDetail{}, err
	}
	tenant, err := a.store.Tenants().GetTenant(auth.AuthConText(ctx))
	if err != nil {
		return types.AccountDetail{}, err
	}
	member, err := a.store.TenantMembers().GetTenantMemberByAccountId(auth.AuthConText(ctx), account.AccountId)
	if err != nil {
		return types.AccountDetail{}, err
	}

	resp.Tenant = tenant.TenantEntity
	resp.Location = location.LocationEntity
	resp.TenantMemberEntity = member.TenantMemberEntity

	return resp, nil
}

func (a *accountSvc) UpdateAccount(ctx context.Context, data types.UpdateAccountReq) error {
	err := a.store.Accounts().UpdateAccount(auth.AuthConText(ctx), data)
	if err != nil {
		return err
	}
	return nil
}
