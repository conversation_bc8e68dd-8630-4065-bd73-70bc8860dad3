package services

import (
	"context"
	"errors"
	"pebble/internal/webapp/models"
	"pebble/internal/webapp/models/sqlserver"
	"pebble/internal/webapp/types"
	commontypes "pebble/internal/webapp/types/common"
	"pebble/pkg/auth"
	"pebble/pkg/udb"
	"pebble/pkg/uerror"
	"pebble/pkg/ulog"
	"pebble/pkg/util"
)

type staffSvc struct {
	store models.IStore
}

func StaffSvc() *staffSvc {
	return &staffSvc{
		store: sqlserver.NewStore(),
	}
}

func (s *staffSvc) InviteStaff(ctx context.Context, data types.InviteTenantMemberReq) (*types.TenantMember, error) {
	account, err := s.store.Accounts().AdminGetAccountByEmail(ctx, data.Email)
	if err != nil && !udb.IsRecordNotFound(err) {
		return nil, err
	}
	if udb.IsRecordNotFound(err) {
		if data.Password == "" {
			data.Password = "123456" // default password
		}

		account, err = AccountSvc().AdminCreateAccount(ctx, types.CreateAccountReq{
			// FirstName:   data.FirstName,
			// LastName:    data.LastName,
			Email:       data.Email,
			PhoneNumber: data.PhoneNumber,
			Password:    data.Password,
			Source:      types.InviteStaffSrouce,
		})
		if err != nil {
			ulog.Errorln(ctx, "create account err", err)
			return nil, err
		}
	}

	actx := auth.AuthConText(ctx)
	member, err := s.store.TenantMembers().CreateTenantMember(actx, types.TenantMemberEntity{
		AccountId:   account.AccountId,
		TenantId:    actx.TenantId,
		LocationId:  actx.LocationId,
		IsOwner:     false,
		Status:      commontypes.StatusActive,
		FirstName:   data.FirstName,
		LastName:    data.LastName,
		Email:       data.Email,
		PhoneNumber: data.PhoneNumber,
		Color:       data.Color,
	})
	if err != nil {
		ulog.Errorln(ctx, "create tenant member err", err)
		return nil, err
	}

	return &member, nil
}

func (s *staffSvc) QueryStaffsDetail(ctx context.Context, params types.QueryTenantMemberParams) ([]types.TenantMemberDetail, error) {
	staffs, err := s.QueryStaffs(ctx, params)
	if err != nil {
		return nil, err
	}

	resp := make([]types.TenantMemberDetail, 0, len(staffs))
	for _, staff := range staffs {
		roles, err := RoleSvc().QueryRoleByAccountId(ctx, staff.AccountId) // todo 待优化
		if err != nil {
			return nil, err
		}
		resp = append(resp, types.TenantMemberDetail{
			TenantMemberEntity: staff.TenantMemberEntity,
			Roles:              roles,
		})
	}

	return resp, nil
}

func (s *staffSvc) QueryStaffs(ctx context.Context, params types.QueryTenantMemberParams) ([]types.TenantMember, error) {
	actx := auth.AuthConText(ctx)

	members, err := s.store.TenantMembers().GetTenantMembers(actx)
	if err != nil {
		ulog.Errorln(ctx, "get tenant members err", err)
		return nil, err
	}

	// var accountIds []string
	// for _, member := range members {
	// 	accountIds = append(accountIds, member.AccountId)
	// }

	// accounts, err := s.store.Accounts().AdminGetAccountByIds(ctx, accountIds)
	// if err != nil {
	// 	ulog.Errorln(ctx, "get accounts err", err)
	// 	return nil, err
	// }

	// accountMap := make(map[string]types.Account)
	// for _, account := range accounts {
	// 	accountMap[account.AccountId] = account
	// }

	// var staffs []types.Staff
	// for _, member := range members {
	// 	staffs = append(staffs, types.Staff{
	// 		TenantId:    member.TenantId,
	// 		LocationId:  member.LocationId,
	// 		AccountId:   member.AccountId,
	// 		FirstName:   member.FirstName,
	// 		LastName:    member.LastName,
	// 		Email:       member.Email,
	// 		PhoneNumber: member.PhoneNumber,
	// 		IsOwner:     member.IsOwner,
	// 		Status:      member.Status,
	// 	})
	// }

	return members, nil
}

func (s *staffSvc) RemoveStaff(ctx context.Context, accountId string) error {
	actx := auth.AuthConText(ctx)

	member, err := s.store.TenantMembers().GetTenantMemberByAccountId(actx, accountId)
	if err != nil && !udb.IsRecordNotFound(err) {
		ulog.Errorln(ctx, "get tenant member err", err)
		return err
	}
	if udb.IsRecordNotFound(err) {
		return nil
	}
	if member.IsOwner {
		return uerror.New(uerror.ErrCodeCommon, "cannot remove owner")
	}

	err = s.store.TenantMembers().DeleteTenantMemberByAccountId(actx, accountId)
	if err != nil {
		ulog.Errorln(ctx, "delete tenant member err", err)
	}

	return err
}

func (s *staffSvc) QueryStaffDetail(ctx context.Context, accountId string) (*types.TenantMemberDetail, error) {
	member, err := s.QueryStaff(ctx, accountId)
	if err != nil {
		return nil, err
	}

	roles, err := RoleSvc().QueryRoleByAccountId(ctx, accountId)
	if err != nil {
		return nil, err
	}

	return &types.TenantMemberDetail{
		TenantMemberEntity: member.TenantMemberEntity,
		Roles:              roles,
	}, nil
}

func (s *staffSvc) QueryStaff(ctx context.Context, accountId string) (*types.TenantMember, error) {
	actx := auth.AuthConText(ctx)

	member, err := s.store.TenantMembers().GetTenantMemberByAccountId(actx, accountId)
	if err != nil && !udb.IsRecordNotFound(err) {
		ulog.Errorln(ctx, "get tenant member by account id err", err)
		return nil, err
	}
	if udb.IsRecordNotFound(err) {
		return nil, uerror.New(uerror.ErrCodeCommon, "staff not found")
	}

	// account, err := s.store.Accounts().AdminGetAccountById(ctx, accountId)
	// if err != nil {
	// 	ulog.Errorln(ctx, "get account err", err)
	// 	return nil, err
	// }

	return member, nil
}

func (s *staffSvc) QueryStaffByAccountIds(ctx context.Context, accountIds []string) ([]types.TenantMember, error) {
	actx := auth.AuthConText(ctx)

	members, err := s.store.TenantMembers().GetTenantMemberByAccountIds(actx, accountIds)
	if err != nil {
		ulog.Errorln(ctx, "get tenant members err", err)
		return nil, err
	}

	// staffIds := make([]string, 0, len(members))
	// for _, member := range members {
	// 	staffIds = append(staffIds, member.AccountId)
	// }

	// accounts, err := s.store.Accounts().AdminGetAccountByIds(ctx, staffIds)
	// if err != nil {
	// 	ulog.Errorln(ctx, "get accounts err", err)
	// 	return nil, err
	// }

	// accountMap := make(map[string]types.Account)
	// for _, account := range accounts {
	// 	accountMap[account.AccountId] = account
	// }

	// var staffs []types.Staff
	// for _, member := range members {
	// 	if account, ok := accountMap[member.AccountId]; ok {
	// 		staffs = append(staffs, types.Staff{
	// 			TenantId:    member.TenantId,
	// 			LocationId:  member.LocationId,
	// 			AccountId:   account.AccountId,
	// 			FirstName:   account.FirstName,
	// 			LastName:    account.LastName,
	// 			Email:       account.Email,
	// 			PhoneNumber: account.PhoneNumber,
	// 			IsOwner:     member.IsOwner,
	// 			Status:      member.Status,
	// 		})
	// 	}
	// }

	return members, nil
}

func (s *staffSvc) UpdateStaff(ctx context.Context, accountId string, data types.UpdateTenantMemberReq) error {
	actx := auth.AuthConText(ctx)

	_, err := s.store.TenantMembers().GetTenantMemberByAccountId(actx, accountId)
	if err != nil && !udb.IsRecordNotFound(err) {
		ulog.Errorln(ctx, "get tenant member err", err)
		return err
	}
	if udb.IsRecordNotFound(err) {
		return errors.New("tenant member not found")
	}

	return s.store.TenantMembers().UpdateTenantMember(actx, accountId, data)
}

func (s *staffSvc) SortStaffs(ctx context.Context, data types.SortTenantMemberReq) error {
	actx := auth.AuthConText(ctx)

	accountIds := make([]string, 0, len(data.Items))
	for _, item := range data.Items {
		accountIds = append(accountIds, item.AccountId)
	}
	// check if all account ids are valid
	members, err := s.store.TenantMembers().GetTenantMemberByAccountIds(actx, accountIds)
	if err != nil {
		ulog.Errorln(ctx, "get tenant member err", err)
		return err
	}
	if len(members) != len(data.Items) {
		return errors.New("invalid account ids")
	}

	// update index
	for i, item := range data.Items {
		err := s.store.TenantMembers().UpdateTenantMember(actx, item.AccountId, types.UpdateTenantMemberReq{
			SortIndex: util.Ptr(uint64(i)),
		})
		if err != nil {
			ulog.Errorln(ctx, "update tenant member err", err)
			return err
		}
	}

	return nil
}
