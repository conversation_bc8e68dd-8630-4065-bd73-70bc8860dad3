package services

import (
	"context"
	"pebble/internal/webapp/models"
	"pebble/internal/webapp/models/sqlserver"
	"pebble/internal/webapp/types"
	"pebble/pkg/auth"
)

type holidaySvc struct {
	store models.IStore
}

func HolidaySvc() *holidaySvc {
	return &holidaySvc{
		store: sqlserver.NewStore(),
	}
}

func (h *holidaySvc) QueryHolidays(ctx context.Context) ([]types.Holiday, error) {
	return h.store.Holidays().List(auth.AuthConText(ctx))
}

// func (h *holidaySvc) CreateHoliday(ctx context.Context, req types.CreateHolidayReq) (*types.Holiday, error) {
// 	return h.store.Holidays().Create(auth.AuthConText(ctx), req)
// }

func (h *holidaySvc) BatchCreateHolidays(ctx context.Context, req []types.CreateHolidayReq) ([]types.Holiday, error) {
	return h.store.Holidays().BatchCreate(auth.AuthConText(ctx), req)
}

func (h *holidaySvc) UpdateHoliday(ctx context.Context, holidayId string, req types.UpdateHolidayReq) error {
	return h.store.Holidays().Update(auth.AuthConText(ctx), holidayId, req)
}

func (h *holidaySvc) DeleteHoliday(ctx context.Context, holidayId string) error {
	return h.store.Holidays().Delete(auth.AuthConText(ctx), holidayId)
}
