package services

import (
	"context"
	"pebble/internal/webapp/models"
	"pebble/internal/webapp/models/sqlserver"
	"pebble/internal/webapp/types"
	commontypes "pebble/internal/webapp/types/common"
	"pebble/pkg/auth"
	"pebble/pkg/udb"
	"pebble/pkg/uerror"
	"pebble/pkg/ulog"

	"go.uber.org/zap"
)

type tenantSvc struct {
	store models.IStore
}

func TenantSvc() *tenantSvc {
	return &tenantSvc{
		store: sqlserver.NewStore(),
	}
}

func (t *tenantSvc) CreateTenantAndLocation(ctx context.Context, data types.CreateTenantAndLocationReq) (*types.CreateTenantAndLocationResp, error) {
	_, err := t.store.Tenants().AdminGetTenantByEmail(ctx, data.Email)
	if err == nil {
		return nil, uerror.ErrEmailInUse
	}
	if !udb.IsRecordNotFound(err) {
		ulog.Errorln(ctx, "query account err", zap.String("email", data.Email))
		return nil, err
	}

	tenant, err := t.store.Tenants().AdminCreateTenant(ctx, types.TenantEntity{
		Email:    data.Email,
		Name:     data.LocationName,
		Status:   commontypes.StatusActive,
		OwnerId:  data.OwnerId,
		Timezone: data.Timezone,
		Currency: data.Currency,
		Country:  data.Country,
	})
	if err != nil {
		ulog.Errorln(ctx, "create tenant error:", err)
		return nil, err
	}

	location, err := t.store.Locations().AdminCreateLocation(ctx, types.LocationEntity{
		TenantId: tenant.TenantId,
		Name:     tenant.Name,
		Status:   commontypes.StatusActive,
		Timezone: tenant.Timezone,
		Currency: tenant.Currency,
		Country:  tenant.Country,
	})
	if err != nil {
		ulog.Errorln(ctx, "create location error:", err)
		return nil, err
	}

	member, err := t.store.TenantMembers().AdminCreateTenantMember(ctx, types.TenantMemberEntity{
		AccountId:   data.OwnerId,
		TenantId:    tenant.TenantId,
		LocationId:  location.LocationId,
		IsOwner:     true,
		Status:      commontypes.StatusActive,
		FirstName:   data.FirstName,
		LastName:    data.LastName,
		Email:       data.Email,
		PhoneNumber: data.PhoneNumber,
	})
	if err != nil {
		ulog.Errorln(ctx, "create tenant member error:", err)
		return nil, err
	}

	ulog.Info(ctx, "register tenant", zap.Any("tenant", tenant), zap.Any("location", location), zap.Any("member", member))

	return &types.CreateTenantAndLocationResp{
		Tenant: tenant,
		Locations: []types.Location{
			*location,
		},
		TenantMember: []types.TenantMember{
			member,
		},
	}, nil
}

func (t *tenantSvc) QueryTenant(ctx context.Context) (*types.QueryTenantResp, error) {
	tenant, err := t.store.Tenants().GetTenant(auth.AuthConText(ctx))
	if err != nil {
		ulog.Errorln(ctx, "query tenant error:", err)
		return nil, err
	}
	locations, err := t.store.Locations().GetLocationByTenant(auth.AuthConText(ctx))
	if err != nil {
		ulog.Errorln(ctx, "query locations error:", err)
		return nil, err
	}

	return &types.QueryTenantResp{
		Tenant:    tenant,
		Locations: locations,
	}, nil
}

func (t *tenantSvc) UpdateTenant(ctx context.Context, req types.UpdateTenantReq) error {
	err := t.store.Tenants().UpdateTenant(auth.AuthConText(ctx), req)
	if err != nil {
		ulog.Errorln(ctx, "update tenant error:", err)
		return err
	}
	return nil
}
