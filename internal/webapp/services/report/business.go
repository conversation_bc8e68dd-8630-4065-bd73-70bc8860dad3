package report

import (
	"context"
	"fmt"
	"pebble/internal/webapp/models"
	"pebble/internal/webapp/models/sqlserver"
	commontypes "pebble/internal/webapp/types/common"
	paymenttypes "pebble/internal/webapp/types/payment"
	reporttypes "pebble/internal/webapp/types/report"
	"pebble/pkg/auth"
	"pebble/pkg/util"
	"strings"
)

type businessGenerator struct {
	store models.IStore
}

func NewBusinessGenerator() *businessGenerator {
	return &businessGenerator{store: sqlserver.NewStore()}
}

func (g *businessGenerator) Generate(ctx context.Context, query reporttypes.ReportQuery) (*reporttypes.GenericReportResp, error) {
	// 1. Get raw data from the data layer.
	rawData, rows, err := g.GetData(ctx, query)
	if err != nil {
		return nil, err
	}

	// 2. Define the metadata for this report.
	metadata := g.GetMetadata()
	// 3. Assemble the final response.
	return &reporttypes.GenericReportResp{
		Metadata: metadata,
		Data:     rawData,
		DataRows: rows,
	}, nil
}

func (p *businessGenerator) GetMetadata() *reporttypes.TableMetadata {
	return &reporttypes.TableMetadata{
		Type: reporttypes.ReportTypeTable,
		Columns: []reporttypes.ReportColumn{
			{Key: "appointment_count", Name: "Appointment Count", Type: reporttypes.ColumnTypeInteger},
			{Key: "serviced_customer_count", Name: "Serviced Customer Count", Type: reporttypes.ColumnTypeInteger},
			{Key: "total_revenue", Name: "Total Revenue", Type: reporttypes.ColumnTypeCurrency},
			{Key: "service_revenue", Name: "Service Revenue", Type: reporttypes.ColumnTypeCurrency},
			{Key: "gift_card_revenue", Name: "Gift Card Revenue", Type: reporttypes.ColumnTypeCurrency},
			{Key: "cash_revenue", Name: "Cash Revenue", Type: reporttypes.ColumnTypeCurrency},
			{Key: "total_tip", Name: "Total Tip", Type: reporttypes.ColumnTypeCurrency},
			{Key: "total_tax", Name: "Total Tax", Type: reporttypes.ColumnTypeCurrency},
		},
		RowKey: "index",
	}
}

func (g *businessGenerator) GetData(ctx context.Context, query reporttypes.ReportQuery) ([]map[string]interface{}, int64, error) {
	actx := auth.AuthConText(ctx)

	location, err := g.store.Locations().GetLocation(actx)
	if err != nil {
		return nil, 0, err
	}

	methods, err := g.store.PaymentMethods().List(actx, paymenttypes.QueryPaymentMethodParams{
		Status: util.Ptr(commontypes.PaymentMethodStatusEnabled),
	})
	if err != nil {
		return nil, 0, err
	}

	records, _, err := g.store.PaymentRecords().List(actx, paymenttypes.QueryPaymentsParams{
		StatusList: []commontypes.PaymentRecordStatus{
			commontypes.PaymentStatusPaid,
			commontypes.PaymentStatusPartialRefund,
			commontypes.PaymentStatusRefund,
		},
		StartDate: &query.StartDate,
		EndDate:   &query.EndDate,
		Timezone:  location.Timezone,
	})
	if err != nil {
		return nil, 0, err
	}

	items, _, err := g.store.PaymentItemRecords().List(actx, paymenttypes.QueryPaymentItemParams{
		StatusList: []commontypes.PaymentRecordStatus{
			commontypes.PaymentStatusPaid,
			commontypes.PaymentStatusPartialRefund,
			commontypes.PaymentStatusRefund,
		},
		StartDate: &query.StartDate,
		EndDate:   &query.EndDate,
		Timezone:  location.Timezone,
	})
	if err != nil {
		return nil, 0, err
	}

	summary := g.Calculate(ctx, reporttypes.CalculateBusinessSummaryReq{
		Location:           *location,
		PaymentMethods:     methods,
		PaymentRecords:     records,
		PaymentItemRecords: items,
	})

	resp := make(map[string]interface{})
	err = util.DeepCopy(summary, &resp)
	resp["index"] = "1"

	return []map[string]interface{}{resp}, 1, err
}

func (p *businessGenerator) Calculate(ctx context.Context, data reporttypes.CalculateBusinessSummaryReq) reporttypes.BusinessSummary {
	var resp reporttypes.BusinessSummary
	apptCount := make(map[string]int64)
	var otherMethodRevenue int64
	for _, record := range data.PaymentRecords {
		revenue := record.AmountTotal - record.AmountTax - record.AmountTip - record.AmountFee - record.AmountRefund
		resp.TotalRevenueNum += revenue
		if record.OrderType == commontypes.OrderTypeAppointment {
			apptCount[record.OrderId] += 1
		}

		methodName := strings.ToLower(record.MethodName)
		switch methodName {
		case "cash":
			resp.CashRevenueNum += revenue
		case "gift card":
			resp.GiftCardRevenueNum += revenue
		default:
			otherMethodRevenue += revenue
		}
		resp.TotalTipNum += record.AmountTip
		resp.TotalTaxNum += record.AmountTax
	}
	resp.ApptointmentCount = int64(len(apptCount))

	paymentClientKey := func(paymentId, clientId string) string {
		return fmt.Sprintf("%s_%s", paymentId, clientId)
	}

	servicedCustomerList := make([]string, 0, len(data.PaymentItemRecords))
	for _, item := range data.PaymentItemRecords {
		if item.Status != commontypes.PaymentStatusPaid {
			continue
		}
		if item.OrderItemType == commontypes.OrderTypeService {
			servicedCustomerList = append(servicedCustomerList, paymentClientKey(item.PaymentId, item.TargetId))
			resp.ServiceRevenueNum += item.AmountSubtotal
		}

	}
	resp.TotalRevenue = util.WithCurrencySymbol(data.Location.CurrencySymbol, resp.TotalRevenueNum)
	resp.TotalTip = util.WithCurrencySymbol(data.Location.CurrencySymbol, resp.TotalTipNum)
	resp.TotalTax = util.WithCurrencySymbol(data.Location.CurrencySymbol, resp.TotalTaxNum)
	resp.GiftCardRevenue = util.WithCurrencySymbol(data.Location.CurrencySymbol, resp.GiftCardRevenueNum)
	resp.ServicedCustomerCount = int64(len(servicedCustomerList))
	resp.CashRevenue = util.WithCurrencySymbol(data.Location.CurrencySymbol, resp.CashRevenueNum)
	resp.ServiceRevenue = util.WithCurrencySymbol(data.Location.CurrencySymbol, resp.ServiceRevenueNum)

	return resp
}
