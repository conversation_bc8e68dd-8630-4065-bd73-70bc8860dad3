package reportservice

import (
	"context"
	"fmt"
	"pebble/internal/webapp/models"
	"pebble/internal/webapp/models/sqlserver"
	commontypes "pebble/internal/webapp/types/common"
	paymenttypes "pebble/internal/webapp/types/payment"
	reporttypes "pebble/internal/webapp/types/report"
	"pebble/pkg/auth"
	"pebble/pkg/localization"
	"pebble/pkg/util"
	"pebble/pkg/utime"
	"sort"
)

type dailySummaryGenerator struct {
	store models.IStore
}

func NewDailySummaryGenerator() *dailySummaryGenerator {
	return &dailySummaryGenerator{store: sqlserver.NewStore()}
}

// dailySummaryAggregator encapsulates the logic for aggregating daily summary data.
type dailySummaryAggregator struct {
	summary             *reporttypes.BusinessSummary
	dailyTotals         map[string]*reporttypes.BusinessSummary
	dailyClientIDs      map[string]map[string]struct{}
	dailyApptIDs        map[string]map[string]struct{}
	totalClientIDs      map[string]struct{}
	totalApptIDs        map[string]struct{}
	paymentMethods      []paymenttypes.PaymentMethods
	dailyMethodTotals   map[string]map[string]int64 // map[date]map[method_id]amount
	summaryMethodTotals map[string]int64            // map[method_id]amount
	timezone            string
	currency            string
}

func newDailySummaryAggregator(timezone, currency string, methods []paymenttypes.PaymentMethods) *dailySummaryAggregator {
	a := &dailySummaryAggregator{
		summary:             &reporttypes.BusinessSummary{},
		dailyTotals:         make(map[string]*reporttypes.BusinessSummary),
		dailyClientIDs:      make(map[string]map[string]struct{}),
		dailyApptIDs:        make(map[string]map[string]struct{}),
		totalClientIDs:      make(map[string]struct{}),
		totalApptIDs:        make(map[string]struct{}),
		paymentMethods:      methods,
		dailyMethodTotals:   make(map[string]map[string]int64),
		summaryMethodTotals: make(map[string]int64),
		timezone:            timezone,
		currency:            currency,
	}
	for _, m := range methods {
		a.summaryMethodTotals[m.MethodId] = 0
	}
	return a
}

// Add processes a single payment record and updates the aggregation.
func (a *dailySummaryAggregator) Add(r paymenttypes.PaymentRecords) {
	if r.CaptureAt == nil {
		return
	}

	recordDateStr := utime.ConvertToTimezone(*r.CaptureAt, a.timezone).Format("01/02/2006")

	// Initialize daily entries if not present
	if _, ok := a.dailyTotals[recordDateStr]; !ok {
		a.dailyTotals[recordDateStr] = &reporttypes.BusinessSummary{}
		a.dailyClientIDs[recordDateStr] = make(map[string]struct{})
		a.dailyApptIDs[recordDateStr] = make(map[string]struct{})
		a.dailyMethodTotals[recordDateStr] = make(map[string]int64)
		for _, m := range a.paymentMethods {
			a.dailyMethodTotals[recordDateStr][m.MethodId] = 0
		}
	}

	paymentClientKey := func(paymentId, clientId string) string {
		return fmt.Sprintf("%s_%s", paymentId, clientId)
	}

	// Aggregate financial data for the day
	d := a.dailyTotals[recordDateStr]
	d.TotalRevenueNum += r.AmountTotal
	d.ServiceRevenueNum += r.AmountSubtotal
	d.TotalTipNum += r.AmountTip
	d.TotalTaxNum += r.AmountTax

	// Aggregate unique client and appointment IDs for the day
	a.dailyClientIDs[recordDateStr][paymentClientKey(r.PaymentId, r.ClientId)] = struct{}{}
	a.dailyApptIDs[recordDateStr][r.OrderId] = struct{}{}

	// Aggregate for the grand summary
	a.summary.TotalRevenueNum += r.AmountTotal
	a.summary.ServiceRevenueNum += r.AmountSubtotal
	a.summary.TotalTipNum += r.AmountTip
	a.summary.TotalTaxNum += r.AmountTax
	a.totalClientIDs[paymentClientKey(r.PaymentId, r.ClientId)] = struct{}{}
	a.totalApptIDs[r.OrderId] = struct{}{}

	// Aggregate payment method totals
	if r.MethodId != "" {
		revenue := r.AmountTotal - r.AmountTax - r.AmountTip - r.AmountFee - r.AmountRefund
		a.dailyMethodTotals[recordDateStr][r.MethodId] += revenue
		a.summaryMethodTotals[r.MethodId] += revenue
	}
}

// Result finalizes the aggregation and returns the formatted report data.
func (a *dailySummaryAggregator) Result() []map[string]interface{} {
	a.summary.ServicedCustomerCount = int64(len(a.totalClientIDs))
	a.summary.ApptointmentCount = int64(len(a.totalApptIDs))

	resp := make([]map[string]interface{}, 0, len(a.dailyTotals)+1)

	// Add the summary row first
	summaryMap := a.convertSummaryToMap(a.summary, a.summaryMethodTotals)
	summaryMap["date"] = "Summary"
	resp = append(resp, summaryMap)

	// Get sorted dates to ensure chronological order (descending)
	sortedDates := make([]string, 0, len(a.dailyTotals))
	for dateStr := range a.dailyTotals {
		sortedDates = append(sortedDates, dateStr)
	}
	sort.Sort(sort.Reverse(sort.StringSlice(sortedDates)))

	// Add daily rows
	for _, dateStr := range sortedDates {
		dailySummary := a.dailyTotals[dateStr]
		dailySummary.ServicedCustomerCount = int64(len(a.dailyClientIDs[dateStr]))
		dailySummary.ApptointmentCount = int64(len(a.dailyApptIDs[dateStr]))

		dailyMap := a.convertSummaryToMap(dailySummary, a.dailyMethodTotals[dateStr])
		dailyMap["date"] = dateStr
		resp = append(resp, dailyMap)
	}

	return resp
}

func (g *dailySummaryGenerator) Generate(ctx context.Context, query reporttypes.ReportQuery) (*reporttypes.GenericReportResp, error) {
	actx := auth.AuthConText(ctx)
	// Get payment methods to build dynamic columns
	methods, err := g.store.PaymentMethods().List(actx, paymenttypes.QueryPaymentMethodParams{
		Status: util.Ptr(commontypes.PaymentMethodStatusEnabled),
	})
	if err != nil {
		return nil, err
	}

	rawData, err := g.GetData(ctx, query, methods)
	if err != nil {
		return nil, err
	}

	metadata := g.GetMetadata(methods)

	return &reporttypes.GenericReportResp{
		Metadata: metadata,
		Data:     rawData,
		DataRows: int64(len(rawData)),
	}, nil
}

func (g *dailySummaryGenerator) GetMetadata(methods []paymenttypes.PaymentMethods) *reporttypes.TableMetadata {
	columns := []reporttypes.ReportColumn{
		{Key: "date", Name: "Date", Type: reporttypes.ColumnTypeString},
		{Key: "appointment_count", Name: "Appointments", Type: reporttypes.ColumnTypeInteger},
		{Key: "serviced_customer_count", Name: "Serviced Customers", Type: reporttypes.ColumnTypeInteger},
		{Key: "total_revenue", Name: "Total Revenue", Type: reporttypes.ColumnTypeCurrency},
		{Key: "service_revenue", Name: "Service Revenue", Type: reporttypes.ColumnTypeCurrency},
		{Key: "total_tip", Name: "Total Tip", Type: reporttypes.ColumnTypeCurrency},
		{Key: "total_tax", Name: "Total Tax", Type: reporttypes.ColumnTypeCurrency},
	}
	for _, method := range methods {
		columns = append(columns, reporttypes.ReportColumn{
			Key:  method.MethodId,
			Name: method.Name,
			Type: reporttypes.ColumnTypeCurrency,
		})
	}

	return &reporttypes.TableMetadata{
		Type:    reporttypes.ReportTypeTable,
		Columns: columns,
		RowKey:  "date",
	}
}

func (g *dailySummaryGenerator) GetData(ctx context.Context, query reporttypes.ReportQuery, methods []paymenttypes.PaymentMethods) ([]map[string]interface{}, error) {
	actx := auth.AuthConText(ctx)

	location, err := g.store.Locations().GetLocation(actx)
	if err != nil {
		return nil, err
	}

	records, _, err := g.store.PaymentRecords().List(actx, paymenttypes.QueryPaymentsParams{
		StatusList: []commontypes.PaymentRecordStatus{
			commontypes.PaymentStatusPaid,
			commontypes.PaymentStatusPartialRefund,
		},
		StartDate: &query.StartDate,
		EndDate:   &query.EndDate,
		Timezone:  location.Timezone,
	})
	if err != nil {
		return nil, err
	}

	aggregator := newDailySummaryAggregator(location.Timezone, location.Currency, methods)
	for _, r := range records {
		aggregator.Add(r)
	}

	return aggregator.Result(), nil
}

// Helper to convert BusinessSummary to map[string]interface{}
func (a *dailySummaryAggregator) convertSummaryToMap(summary *reporttypes.BusinessSummary, methodTotals map[string]int64) map[string]interface{} {
	symbol := localization.GetCurrencySymbol(a.currency)
	dataMap := map[string]interface{}{
		"appointment_count":       summary.ApptointmentCount,
		"serviced_customer_count": summary.ServicedCustomerCount,
		"total_revenue":           util.WithCurrencySymbol(symbol, summary.TotalRevenueNum),
		"service_revenue":         util.WithCurrencySymbol(symbol, summary.ServiceRevenueNum),
		"total_tip":               util.WithCurrencySymbol(symbol, summary.TotalTipNum),
		"total_tax":               util.WithCurrencySymbol(symbol, summary.TotalTaxNum),
	}

	for methodId, total := range methodTotals {
		dataMap[methodId] = util.WithCurrencySymbol(symbol, total)
	}

	return dataMap
}
