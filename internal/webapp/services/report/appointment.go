package report

import (
	"context"
	"fmt"
	"pebble/internal/webapp/models"
	"pebble/internal/webapp/models/sqlserver"
	"pebble/internal/webapp/types"
	appointmenttypes "pebble/internal/webapp/types/appointment"
	commontypes "pebble/internal/webapp/types/common"
	paymenttypes "pebble/internal/webapp/types/payment"
	reporttypes "pebble/internal/webapp/types/report"
	"pebble/pkg/auth"
	"pebble/pkg/localization"
	"pebble/pkg/util"
	"pebble/pkg/utime"
	"strings"
)

type allAppintmentGenerator struct {
	store models.IStore
}

func NewAllAppintmentGeneratorGenerator() *allAppintmentGenerator {
	return &allAppintmentGenerator{store: sqlserver.NewStore()}
}

func (g *allAppintmentGenerator) Generate(ctx context.Context, query reporttypes.ReportQuery) (*reporttypes.GenericReportResp, error) {
	// 1. Get raw data from the data layer.
	if query.Page == nil {
		query.Page = util.Ptr(1)
	}
	if query.PageSize == nil {
		query.PageSize = util.Ptr(20)
	}

	rawData, rows, err := g.GetData(ctx, query)
	if err != nil {
		return nil, err
	}

	// 2. Define the metadata for this report.
	metadata := g.GetMetadata()
	metadata.Pagination = util.Ptr(reporttypes.Pagination{
		Page:     *query.Page,
		PageSize: *query.PageSize,
		Total:    rows,
	})
	// 3. Assemble the final response.
	return &reporttypes.GenericReportResp{
		Metadata: metadata,
		Data:     rawData,
		DataRows: rows,
	}, nil
}

func (p *allAppintmentGenerator) GetMetadata() *reporttypes.TableMetadata {
	return &reporttypes.TableMetadata{
		Type: reporttypes.ReportTypeTable,
		Columns: []reporttypes.ReportColumn{
			{Key: "show_id", Name: "ID", Type: reporttypes.ColumnTypeString},
			{Key: "capture_date", Name: "Date", Type: reporttypes.ColumnTypeDate},
			{Key: "client_name", Name: "Client Name", Type: reporttypes.ColumnTypeString},
			{Key: "staff_name", Name: "Staff Name", Type: reporttypes.ColumnTypeString},
			{Key: "services", Name: "Services", Type: reporttypes.ColumnTypeString},
			{Key: "total", Name: "Total", Type: reporttypes.ColumnTypeCurrency},
			{Key: "subtotal", Name: "subtotal", Type: reporttypes.ColumnTypeCurrency},
			{Key: "fee", Name: "Fee", Type: reporttypes.ColumnTypeCurrency},
			{Key: "tip", Name: "Tip", Type: reporttypes.ColumnTypeCurrency},
			{Key: "status", Name: "Status", Type: reporttypes.ColumnTypeString},
			{Key: "payment_method", Name: "Payment Method", Type: reporttypes.ColumnTypeString},
		},
		RowKey: "show_id",
	}
}

func (g *allAppintmentGenerator) GetData(ctx context.Context, query reporttypes.ReportQuery) ([]map[string]interface{}, int64, error) {
	actx := auth.AuthConText(ctx)

	location, err := g.store.Locations().GetLocation(actx)
	if err != nil {
		return nil, 0, err
	}
	records, total, err := g.store.PaymentRecords().List(actx, paymenttypes.QueryPaymentsParams{
		StatusList: []commontypes.PaymentRecordStatus{
			commontypes.PaymentStatusPaid,
			commontypes.PaymentStatusPartialRefund,
			commontypes.PaymentStatusRefund,
		},
		StartDate: &query.StartDate,
		EndDate:   &query.EndDate,
		Timezone:  location.Timezone,
		Page:      query.Page,
		PageSize:  query.PageSize,
	})
	if err != nil {
		return nil, 0, err
	}

	clientIds := make([]string, 0, len(records))
	for _, r := range records {
		clientIds = append(clientIds, r.ClientId)
	}
	clients, err := g.store.Clients().GetClientByIds(actx, util.UniqueString(clientIds))
	if err != nil {
		return nil, 0, err
	}
	clientMap := make(map[string]types.Client, len(clients))
	for _, c := range clients {
		clientMap[c.ClientId] = c
	}

	apptIds := make([]string, 0, len(records))
	for _, r := range records {
		apptIds = append(apptIds, r.OrderId)
	}
	apptIds = util.UniqueString(apptIds)
	appts, err := g.store.Appointments().GetByAppointmentIds(actx, apptIds)
	if err != nil {
		return nil, 0, err
	}
	showIdMap := make(map[string]string)
	for _, appt := range appts {
		showIdMap[appt.AppointmentId] = appt.ShowId
	}

	apptServices, err := g.store.AppointmentServices().GetByAppointmentIds(actx, apptIds)
	if err != nil {
		return nil, 0, err
	}
	apptServiceMap := make(map[string][]appointmenttypes.AppointmentService)
	staffIds := make([]string, 0)
	for _, as := range apptServices {
		apptServiceMap[as.AppointmentId] = append(apptServiceMap[as.AppointmentId], as)
		staffIds = append(staffIds, as.AccountId)
	}

	staffs, err := g.store.TenantMembers().GetTenantMemberByAccountIds(actx, util.UniqueString(staffIds))
	if err != nil {
		return nil, 0, err
	}
	staffMap := make(map[string]types.TenantMember, len(staffs))
	for _, s := range staffs {
		staffMap[s.AccountId] = s
	}

	resp := make([]map[string]interface{}, 0, len(records))
	for _, r := range records {
		services := apptServiceMap[r.OrderId]
		serviceNames := make([]string, 0, len(services))
		staffNames := make([]string, 0, len(services))
		for _, s := range services {
			serviceNames = append(serviceNames, s.Name)
			if staff, ok := staffMap[s.AccountId]; ok {
				staffNames = append(staffNames, fmt.Sprintf("%s %s", staff.FirstName, staff.LastName))
			}
		}

		clientName := ""
		if client, ok := clientMap[r.ClientId]; ok {
			clientName = fmt.Sprintf("%s %s", client.FirstName, client.LastName)
		}

		var captureDate string
		if r.CaptureAt != nil {
			captureDate = utime.ConvertToTimezone(*r.CaptureAt, location.Timezone).Format("01/02/2006 03:04 PM")
		}
		resp = append(resp, map[string]interface{}{
			"show_id":        showIdMap[r.OrderId],
			"capture_date":   captureDate,
			"client_name":    clientName,
			"staff_name":     strings.Join(util.UniqueString(staffNames), ", "),
			"services":       strings.Join(serviceNames, ", "),
			"total":          util.WithCurrencySymbol(localization.GetCurrencySymbol(r.Currency), r.AmountTotal),
			"subtotal":       util.WithCurrencySymbol(localization.GetCurrencySymbol(r.Currency), r.AmountSubtotal),
			"fee":            util.WithCurrencySymbol(localization.GetCurrencySymbol(r.Currency), r.AmountFee),
			"tip":            util.WithCurrencySymbol(localization.GetCurrencySymbol(r.Currency), r.AmountTip),
			"status":         r.Status.String(),
			"payment_method": r.MethodName,
		})
	}

	return resp, total, nil
}
