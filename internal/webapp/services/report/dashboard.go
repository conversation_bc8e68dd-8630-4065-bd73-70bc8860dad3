package report

import (
	"context"
	"pebble/internal/webapp/models"
	"pebble/internal/webapp/models/sqlserver"
	commontypes "pebble/internal/webapp/types/common"
	paymenttypes "pebble/internal/webapp/types/payment"
	reporttypes "pebble/internal/webapp/types/report"
	"pebble/pkg/auth"
	"pebble/pkg/util"
)

type DashboardReportSvc struct {
	store models.IStore
}

func NewDashboardReportSvc() *DashboardReportSvc {
	return &DashboardReportSvc{
		store: sqlserver.NewStore(),
	}
}

func (d *DashboardReportSvc) GetDashboardReport(ctx context.Context, query reporttypes.ReportQuery) (*reporttypes.DashboardReportResp, error) {
	actx := auth.AuthConText(ctx)
	location, err := d.store.Locations().GetLocation(actx)
	if err != nil {
		return nil, err
	}

	staffs, err := d.store.TenantMembers().GetTenantMembers(actx)
	if err != nil {
		return nil, err
	}

	methods, err := d.store.PaymentMethods().List(actx, paymenttypes.QueryPaymentMethodParams{
		Status: util.Ptr(commontypes.PaymentMethodStatusEnabled),
	})
	if err != nil {
		return nil, err
	}

	records, _, err := d.store.PaymentRecords().List(actx, paymenttypes.QueryPaymentsParams{
		StatusList: []commontypes.PaymentRecordStatus{
			commontypes.PaymentStatusPaid,
			commontypes.PaymentStatusPartialRefund,
			commontypes.PaymentStatusRefund,
		},
		StartDate: &query.StartDate,
		EndDate:   &query.EndDate,
		Timezone:  location.Timezone,
	})
	if err != nil {
		return nil, err
	}

	items, _, err := d.store.PaymentItemRecords().List(actx, paymenttypes.QueryPaymentItemParams{
		StatusList: []commontypes.PaymentRecordStatus{
			commontypes.PaymentStatusPaid,
			commontypes.PaymentStatusPartialRefund,
			commontypes.PaymentStatusRefund,
		},
		StartDate: &query.StartDate,
		EndDate:   &query.EndDate,
		Timezone:  location.Timezone,
	})
	if err != nil {
		return nil, err
	}

	var resp reporttypes.DashboardReportResp
	resp.BusinessSummary = NewBusinessGenerator().Calculate(ctx, reporttypes.CalculateBusinessSummaryReq{
		Location:           *location,
		PaymentMethods:     methods,
		PaymentRecords:     records,
		PaymentItemRecords: items,
	})

	resp.StaffSummary = NewStaffPerformanceGenerator().Calculate(ctx, reporttypes.CalculateStaffSummaryReq{
		Location:           *location,
		Staffs:             staffs,
		PaymentItemRecords: items,
	})

	resp.MethodSummary = NewPaymentMethodGenerator().Calculate(ctx, reporttypes.CalculateMethodSummaryReq{
		Location:       *location,
		PaymentMethods: methods,
		PaymentRecords: records,
	})

	_, unpaidNum, err := NewUnpaidAppintmentGeneratorGenerator().GetData(ctx, query)
	if err != nil {
		return nil, err
	}
	resp.UnpaidApptointmentCount = unpaidNum

	return &resp, nil
}
