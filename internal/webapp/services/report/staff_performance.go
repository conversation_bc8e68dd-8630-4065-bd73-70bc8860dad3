package reportservice

import (
	"context"
	"fmt"
	"pebble/internal/webapp/models"
	"pebble/internal/webapp/models/sqlserver"
	commontypes "pebble/internal/webapp/types/common"
	paymenttypes "pebble/internal/webapp/types/payment"
	reporttypes "pebble/internal/webapp/types/report"
	"pebble/pkg/auth"
	"pebble/pkg/util"
)

type StaffPerformanceGenerator struct {
	store models.IStore
}

func NewStaffPerformanceGenerator() *StaffPerformanceGenerator {
	return &StaffPerformanceGenerator{store: sqlserver.NewStore()}
}

func (g *StaffPerformanceGenerator) Generate(ctx context.Context, query reporttypes.ReportQuery) (*reporttypes.GenericReportResp, error) {
	// 1. Get raw data from the data layer.
	rawData, rows, err := g.GetData(ctx, query)
	if err != nil {
		return nil, err
	}

	// 2. Define the metadata for this report.
	metadata := g.GetMetadata()
	// 3. Assemble the final response.
	return &reporttypes.GenericReportResp{
		Metadata: metadata,
		Data:     rawData,
		DataRows: rows,
	}, nil
}

func (g *StaffPerformanceGenerator) GetMetadata() *reporttypes.TableMetadata {
	return &reporttypes.TableMetadata{
		Type: reporttypes.ReportTypeTable,
		Columns: []reporttypes.ReportColumn{
			{Key: "account_name", Name: "Staff Name", Type: reporttypes.ColumnTypeString},
			{Key: "revenue", Name: "Revenue", Type: reporttypes.ColumnTypeCurrency},
			{Key: "tip", Name: "Tips", Type: reporttypes.ColumnTypeCurrency},
			{Key: "service_count", Name: "Service Count", Type: reporttypes.ColumnTypeInteger},
			{Key: "client_count", Name: "Client Count", Type: reporttypes.ColumnTypeInteger},
		},
		RowKey: "account_id",
	}
}

func (g *StaffPerformanceGenerator) GetData(ctx context.Context, query reporttypes.ReportQuery) ([]map[string]interface{}, int64, error) {
	actx := auth.AuthConText(ctx)

	location, err := g.store.Locations().GetLocation(actx)
	if err != nil {
		return nil, 0, err
	}

	staffs, err := g.store.TenantMembers().GetTenantMembers(actx)
	if err != nil {
		return nil, 0, err
	}

	items, _, err := g.store.PaymentItemRecords().List(actx, paymenttypes.QueryPaymentItemParams{
		StatusList: []commontypes.PaymentRecordStatus{
			commontypes.PaymentStatusPaid,
			commontypes.PaymentStatusPartialRefund,
			commontypes.PaymentStatusRefund,
		},
		StartDate: &query.StartDate,
		EndDate:   &query.EndDate,
		Timezone:  location.Timezone,
	})
	if err != nil {
		return nil, 0, err
	}

	staffSummary := g.Calculate(ctx, reporttypes.CalculateStaffSummaryReq{
		Location:           *location,
		Staffs:             staffs,
		PaymentItemRecords: items,
	})

	resp := make([]map[string]interface{}, 0, len(staffSummary))
	err = util.DeepCopy(staffSummary, &resp)

	return resp, int64(len(staffs)), err
}

func (g *StaffPerformanceGenerator) Calculate(ctx context.Context, data reporttypes.CalculateStaffSummaryReq) []reporttypes.StaffSummary {
	paymentClientKey := func(paymentId, clientId string) string {
		return fmt.Sprintf("%s_%s", paymentId, clientId)
	}
	revenueMap := make(map[string]int64)
	tipsMap := make(map[string]int64)
	serviceCount := make(map[string]int64)
	clientCount := make(map[string][]string)
	for _, item := range data.PaymentItemRecords {
		if item.Status != commontypes.PaymentStatusPaid {
			continue
		}

		if item.OrderItemType == commontypes.OrdertypeTip {
			tipsMap[item.AccountId] += item.AmountTotal
		}
		if item.OrderItemType == commontypes.OrderTypeService {
			revenueMap[item.AccountId] += item.AmountSubtotal
			serviceCount[item.AccountId] += 1
			clientCount[item.AccountId] = append(clientCount[item.AccountId], paymentClientKey(item.PaymentId, item.ClientId))
		}
	}

	staffSummary := make([]reporttypes.StaffSummary, 0, len(data.Staffs))
	for _, staff := range data.Staffs {
		clientCount := util.UniqueString(clientCount[staff.AccountId])
		item := reporttypes.StaffSummary{
			AccountId:    staff.AccountId,
			AccountName:  staff.FirstName + " " + staff.LastName,
			Revenue:      util.WithCurrencySymbol(data.Location.CurrencySymbol, revenueMap[staff.AccountId]),
			RevenueNum:   revenueMap[staff.AccountId],
			Tip:          util.WithCurrencySymbol(data.Location.CurrencySymbol, tipsMap[staff.AccountId]),
			TipNum:       tipsMap[staff.AccountId],
			ServiceCount: serviceCount[staff.AccountId],
			ClientCount:  int64(len(clientCount)),
		}
		staffSummary = append(staffSummary, item)
	}
	return staffSummary
}
