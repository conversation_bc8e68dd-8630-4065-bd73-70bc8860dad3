package report

import (
	"context"
	"errors"
	"pebble/internal/webapp/models"
	"pebble/internal/webapp/models/sqlserver"
	appointmentservice "pebble/internal/webapp/services/appointment"
	appointmenttypes "pebble/internal/webapp/types/appointment"
	commontypes "pebble/internal/webapp/types/common"
	reporttypes "pebble/internal/webapp/types/report"
	"pebble/pkg/auth"
	"pebble/pkg/util"
	"pebble/pkg/utime"
	"strings"
)

type unpaidAppintmentGenerator struct {
	store models.IStore
}

func NewUnpaidAppintmentGeneratorGenerator() *unpaidAppintmentGenerator {
	return &unpaidAppintmentGenerator{store: sqlserver.NewStore()}
}

func (g *unpaidAppintmentGenerator) Generate(ctx context.Context, query reporttypes.ReportQuery) (*reporttypes.GenericReportResp, error) {
	// 1. Get raw data from the data layer.

	appts, rows, err := g.GetData(ctx, query)
	if err != nil {
		return nil, err
	}

	rawData, err := g.Results(ctx, appts)
	if err != nil {
		return nil, err
	}

	// 2. Define the metadata for this report.
	metadata := g.GetMetadata()
	metadata.Pagination = util.Ptr(reporttypes.Pagination{
		Page:     *query.Page,
		PageSize: *query.PageSize,
		Total:    rows,
	})
	// 3. Assemble the final response.
	return &reporttypes.GenericReportResp{
		Metadata: metadata,
		Data:     rawData,
		DataRows: rows,
	}, nil
}

func (p *unpaidAppintmentGenerator) GetMetadata() *reporttypes.TableMetadata {
	return &reporttypes.TableMetadata{
		Type: reporttypes.ReportTypeTable,
		Columns: []reporttypes.ReportColumn{
			{Key: "show_id", Name: "ID", Type: reporttypes.ColumnTypeString},
			{Key: "show_date", Name: "Date", Type: reporttypes.ColumnTypeDate},
			{Key: "client_name", Name: "Client Name", Type: reporttypes.ColumnTypeString},
			{Key: "staff_name", Name: "Staff Name", Type: reporttypes.ColumnTypeString},
			{Key: "services", Name: "Services", Type: reporttypes.ColumnTypeString},
			{Key: "total", Name: "Total", Type: reporttypes.ColumnTypeCurrency},
			{Key: "subtotal", Name: "Subtotal", Type: reporttypes.ColumnTypeCurrency},
			{Key: "fee", Name: "Fee", Type: reporttypes.ColumnTypeCurrency},
			{Key: "tip", Name: "Tip", Type: reporttypes.ColumnTypeCurrency},
			{Key: "status", Name: "Appointment Status", Type: reporttypes.ColumnTypeString},
			{Key: "payment_status", Name: "Payment Status", Type: reporttypes.ColumnTypeString},
		},
		RowKey: "show_id",
	}
}

func (g *unpaidAppintmentGenerator) GetData(ctx context.Context, query reporttypes.ReportQuery) ([]appointmenttypes.Appointment, int64, error) {
	actx := auth.AuthConText(ctx)

	if query.CurrentDate == nil || query.CurrentTime == nil {
		return nil, 0, errors.New("current_date and current_time are required")
	}

	appts, total, err := g.store.Appointments().List(actx, appointmenttypes.GetAppointmentConditions{
		ScheduledStartDate: &query.StartDate,
		ScheduledEndDate:   &query.EndDate,
		StatusList: []commontypes.AppointmentStatusType{
			commontypes.AppointmentStatusUnconfirm,
			commontypes.AppointmentStatusWaitListen,
			commontypes.AppointmentStatusConfirm,
			commontypes.AppointmentStatusArrived,
			commontypes.AppointmentStatusInService,
		},
		PaymentStatusList: []commontypes.PaymentRecordStatus{
			commontypes.PaymentStatusUnpaid,
			commontypes.PaymentStatusPartialPaid,
		},
		TimelineType: util.Ptr(commontypes.TimelineTypeHistory),
		CurrentDate:  query.CurrentDate,
		CurrentTime:  query.CurrentTime,
	})
	if err != nil {
		return nil, 0, err
	}

	return appts, total, nil
}

func (g *unpaidAppintmentGenerator) Results(ctx context.Context, appts []appointmenttypes.Appointment) ([]map[string]interface{}, error) {
	apptDetails, err := appointmentservice.AppointmentSvc().ToAppointmentDetail(ctx, appts)
	if err != nil {
		return nil, err
	}

	location, err := g.store.Locations().GetLocation(auth.AuthConText(ctx))
	if err != nil {
		return nil, err
	}

	billMap := make(map[string]appointmenttypes.Bills)
	for _, appt := range apptDetails {
		b, _ := appointmentservice.AppointmentSvc().Bills(ctx, appointmenttypes.BillReq{
			CurrencySymbol:    location.CurrencySymbol,
			Appointment:       appt,
			PaymentMethodName: "",
		})
		billMap[appt.AppointmentId] = b
	}

	resp := make([]map[string]interface{}, 0, len(apptDetails))
	for _, appt := range apptDetails {
		biil, ok := billMap[appt.AppointmentId]
		if !ok {
			continue
		}

		staffNames := make([]string, 0, len(appt.Services))
		serviceNames := make([]string, 0, len(appt.Services))
		for _, service := range appt.Services {
			staffNames = append(staffNames, service.AccountFirstName+" "+service.AccountLastName)
			serviceNames = append(serviceNames, service.Name)
		}

		startData, err := utime.ParseDate(appt.ScheduledStartDate, location.Timezone)
		if err != nil {
			return nil, err
		}
		resp = append(resp, map[string]interface{}{
			"show_id":        appt.ShowId,
			"show_date":      startData.Format("01/02/2006 03:04 PM"),
			"client_name":    appt.ClientFirstName + " " + appt.ClientLastName,
			"staff_name":     strings.Join(util.UniqueString(staffNames), ", "),
			"services":       strings.Join(serviceNames, ", "),
			"total":          util.WithCurrencySymbol(location.CurrencySymbol, biil.AmountTotal),
			"subtotal":       util.WithCurrencySymbol(location.CurrencySymbol, biil.AmountSubtotal),
			"fee":            util.WithCurrencySymbol(location.CurrencySymbol, biil.AmountFee),
			"tip":            util.WithCurrencySymbol(location.CurrencySymbol, biil.AmountTip),
			"status":         appt.Status.String(),
			"payment_status": appt.PaymentStatus.String(),
		})
	}

	return resp, nil
}
