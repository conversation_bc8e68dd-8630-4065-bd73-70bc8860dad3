package report

import reporttypes "pebble/internal/webapp/types/report"

// MultilingualNavigationData stores navigation data for different languages
type MultilingualNavigationData struct {
	NavigationData map[string]reporttypes.NavigationResp
}

// GetNavigationForLanguage returns navigation data for a specific language
func (m *MultilingualNavigationData) GetNavigationForLanguage(language string) reporttypes.NavigationResp {
	if nav, exists := m.NavigationData[language]; exists {
		return nav
	}
	// Fallback to English if requested language is not available
	return m.NavigationData["en-US"]
}

// GetDefaultMultilingualNavigationData returns the default multilingual navigation data
func GetDefaultMultilingualNavigationData() *MultilingualNavigationData {
	return &MultilingualNavigationData{
		NavigationData: map[string]reporttypes.NavigationResp{
			"en-US": {
				Categories: []reporttypes.NavigationCategory{
					{
						GroupName: "Sale",
						Items: []reporttypes.NavigationItem{
							{Key: "sale-daily", Name: "Daily", Description: "Daily sales report"},
							{Key: "sale-payment-methods", Name: "Payment Methods", Description: "Payment methods breakdown"},
							{Key: "sale-all", Name: "All Sales", Description: "Complete sales overview"},
							{Key: "sale-unpaid-appointment", Name: "Unpaid Appointment", Description: "Unpaid appointment report"},
						},
					},
					{
						GroupName: "Staff",
						Items: []reporttypes.NavigationItem{
							{Key: "staff-performance", Name: "Staff Performance", Description: "Staff performance analytics"},
						},
					},
				},
			},
			"zh-CN": {
				Categories: []reporttypes.NavigationCategory{
					{
						GroupName: "销售",
						Items: []reporttypes.NavigationItem{
							{Key: "sale-daily", Name: "每日销售", Description: "每日销售报告"},
							{Key: "sale-payment-methods", Name: "支付方式", Description: "支付方式分析"},
							{Key: "sale-all", Name: "全部销售", Description: "完整销售概览"},
							{Key: "sale-unpaid-appointment", Name: "未付款预约", Description: "未付款预约报告"},
						},
					},
					{
						GroupName: "员工",
						Items: []reporttypes.NavigationItem{
							{Key: "staff-performance", Name: "员工绩效", Description: "员工绩效分析"},
						},
					},
				},
			},
		},
	}
}
