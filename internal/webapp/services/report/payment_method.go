package reportservice

import (
	"context"
	"pebble/internal/webapp/models"
	"pebble/internal/webapp/models/sqlserver"
	commontypes "pebble/internal/webapp/types/common"
	paymenttypes "pebble/internal/webapp/types/payment"
	reporttypes "pebble/internal/webapp/types/report"
	"pebble/pkg/auth"
	"pebble/pkg/util"
)

type paymentMethodGenerator struct {
	store models.IStore
}

func NewPaymentMethodGenerator() *paymentMethodGenerator {
	return &paymentMethodGenerator{store: sqlserver.NewStore()}
}

func (g *paymentMethodGenerator) Generate(ctx context.Context, query reporttypes.ReportQuery) (*reporttypes.GenericReportResp, error) {
	// 1. Get raw data from the data layer.
	rawData, rows, err := g.GetData(ctx, query)
	if err != nil {
		return nil, err
	}

	// 2. Define the metadata for this report.
	metadata := g.GetMetadata()
	// 3. Assemble the final response.
	return &reporttypes.GenericReportResp{
		Metadata: metadata,
		Data:     rawData,
		DataRows: rows,
	}, nil
}

func (p *paymentMethodGenerator) GetMetadata() *reporttypes.TableMetadata {
	return &reporttypes.TableMetadata{
		Type: reporttypes.ReportTypeTable,
		Columns: []reporttypes.ReportColumn{
			{Key: "method_name", Name: "Payment Method", Type: reporttypes.ColumnTypeString},
			{Key: "revenue", Name: "Revenue", Type: reporttypes.ColumnTypeCurrency},
		},
		RowKey: "method_id",
	}
}

func (g *paymentMethodGenerator) GetData(ctx context.Context, query reporttypes.ReportQuery) ([]map[string]interface{}, int64, error) {
	actx := auth.AuthConText(ctx)

	location, err := g.store.Locations().GetLocation(actx)
	if err != nil {
		return nil, 0, err
	}

	methods, err := g.store.PaymentMethods().List(actx, paymenttypes.QueryPaymentMethodParams{
		Status: util.Ptr(commontypes.PaymentMethodStatusEnabled),
	})
	if err != nil {
		return nil, 0, err
	}

	records, _, err := g.store.PaymentRecords().List(actx, paymenttypes.QueryPaymentsParams{
		StatusList: []commontypes.PaymentRecordStatus{
			commontypes.PaymentStatusPaid,
			commontypes.PaymentStatusPartialRefund,
			commontypes.PaymentStatusRefund,
		},
		StartDate: &query.StartDate,
		EndDate:   &query.EndDate,
		Timezone:  location.Timezone,
	})
	if err != nil {
		return nil, 0, err
	}

	summary := g.Calculate(ctx, reporttypes.CalculateMethodSummaryReq{
		Location:       *location,
		PaymentMethods: methods,
		PaymentRecords: records,
	})

	resp := make([]map[string]interface{}, 0, len(summary))
	err = util.DeepCopy(summary, &resp)

	return resp, int64(len(methods)), err
}

func (p *paymentMethodGenerator) Calculate(ctx context.Context, data reporttypes.CalculateMethodSummaryReq) []reporttypes.MethodSummary {

	methodRevenueMap := make(map[string]int64)
	for _, record := range data.PaymentRecords {
		if record.Status == commontypes.PaymentStatusRefund {
			continue
		}
		revenue := record.AmountTotal - record.AmountTax - record.AmountTip - record.AmountFee - record.AmountRefund

		methodRevenueMap[record.MethodId] += revenue
	}

	methodRevenues := make([]reporttypes.MethodSummary, 0, len(data.PaymentMethods))
	for _, method := range data.PaymentMethods {
		methodRevenues = append(methodRevenues, reporttypes.MethodSummary{
			MethodId:   method.MethodId,
			MethodName: method.Name,
			Revenue:    util.WithCurrencySymbol(data.Location.CurrencySymbol, methodRevenueMap[method.MethodId]),
			RevenueNum: methodRevenueMap[method.MethodId],
		})
	}

	return methodRevenues
}
