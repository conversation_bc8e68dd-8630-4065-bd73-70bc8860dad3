package reportservice

import (
	"bytes"
	"context"
	"encoding/csv"
	"fmt"
	"pebble/internal/webapp/models"
	"pebble/internal/webapp/models/sqlserver"
	reporttypes "pebble/internal/webapp/types/report"
	"pebble/pkg/util"
)

// ReportGenerator defines the interface for any specific report generator.
type ReportGenerator interface {
	Generate(ctx context.Context, query reporttypes.ReportQuery) (*reporttypes.GenericReportResp, error)
}

// ReportService orchestrates report generation by dispatching to registered generators.
type ReportService struct {
	generators map[string]ReportGenerator
	store      models.IStore
}

func NewReportService() *ReportService {
	svc := &ReportService{
		generators: make(map[string]ReportGenerator),
		store:      sqlserver.NewStore(),
	}
	svc.registerGenerators()
	return svc
}

func (s *ReportService) register(key string, g ReportGenerator) {
	s.generators[key] = g
}

// GenerateReport is the main entry point that dispatches tasks.
func (s *ReportService) GenerateReport(ctx context.Context, key string, query reporttypes.ReportQuery) (*reporttypes.GenericReportResp, error) {
	generator, ok := s.generators[key]
	if !ok {
		return nil, fmt.Errorf("report with key '%s' not found", key)
	}
	return generator.Generate(ctx, query)
}

// ExportReportToCSV generates a CSV file for the given report.
func (s *ReportService) ExportReportToCSV(ctx context.Context, key string, query reporttypes.ReportQuery) ([]byte, error) {
	// 1. Get the first page to retrieve metadata and total rows.
	query.Page = util.Ptr(1)
	query.PageSize = util.Ptr(1)
	initialReport, err := s.GenerateReport(ctx, key, query)
	if err != nil {
		return nil, err
	}

	if initialReport == nil || initialReport.Metadata == nil {
		return nil, fmt.Errorf("could not generate report metadata for key '%s'", key)
	}

	tableMetadata, ok := initialReport.Metadata.(*reporttypes.TableMetadata)
	if !ok {
		return nil, fmt.Errorf("report with key '%s' is not a table-based report and cannot be exported", key)
	}

	// If there's no data, return an empty CSV with just headers.
	totalRows := initialReport.DataRows
	var fullReportData []map[string]interface{}

	if totalRows > 0 {
		// 2. Fetch all data in a single request.
		query.Page = util.Ptr(1)
		// Set page size to total rows to fetch all data at once.
		query.PageSize = util.Ptr(int(totalRows))
		fullReport, err := s.GenerateReport(ctx, key, query)
		if err != nil {
			return nil, err
		}
		var ok bool
		fullReportData, ok = fullReport.Data.([]map[string]interface{})
		if !ok {
			if fullReport.Data != nil {
				return nil, fmt.Errorf("unexpected data format in report")
			}
		}
	}

	// 3. Prepare CSV writer.
	var buffer bytes.Buffer
	writer := csv.NewWriter(&buffer)
	// Write UTF-8 BOM for Excel compatibility.
	_, _ = buffer.Write([]byte{0xEF, 0xBB, 0xBF})

	// 4. Write header.
	headers := make([]string, 0, len(tableMetadata.Columns))
	columnKeys := make([]string, 0, len(tableMetadata.Columns))
	for _, col := range tableMetadata.Columns {
		headers = append(headers, col.Name)
		columnKeys = append(columnKeys, col.Key)
	}
	if err := writer.Write(headers); err != nil {
		return nil, err
	}

	// 5. Write data rows.
	for _, rowMap := range fullReportData {
		record := make([]string, 0, len(columnKeys))
		for _, colKey := range columnKeys {
			val, _ := rowMap[colKey]
			record = append(record, fmt.Sprintf("%v", val))
		}
		if err := writer.Write(record); err != nil {
			return nil, err
		}
	}

	writer.Flush()
	if err := writer.Error(); err != nil {
		return nil, err
	}

	return buffer.Bytes(), nil
}

// registerGenerators initializes all report generators.
func (s *ReportService) registerGenerators() {
	// Register all generators here.
	s.register("staff-performance", NewStaffPerformanceGenerator())
	s.register("sale-summary", NewBusinessGenerator())
	s.register("sale-payment-methods", NewPaymentMethodGenerator())
	s.register("sale-all", NewAllAppintmentGeneratorGenerator())
	s.register("sale-unpaid-appointment", NewUnpaidAppintmentGeneratorGenerator())
	s.register("sale-daily", NewDailySummaryGenerator())
}
