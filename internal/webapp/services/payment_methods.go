package services

import (
	"context"
	"pebble/internal/webapp/models"
	"pebble/internal/webapp/models/sqlserver"
	paymenttypes "pebble/internal/webapp/types/payment"
	"pebble/pkg/auth"
)

type paymentMethodSvc struct {
	store models.IStore
}

func PaymentMethodSvc() *paymentMethodSvc {
	return &paymentMethodSvc{
		store: sqlserver.NewStore(),
	}
}

func (s *paymentMethodSvc) Create(ctx context.Context, req paymenttypes.CreatePaymentMethodReq) (*paymenttypes.PaymentMethods, error) {
	actx := auth.AuthConText(ctx)
	return s.store.PaymentMethods().Create(actx, req)
}

func (s *paymentMethodSvc) Get(ctx context.Context, paymentMethodId string) (*paymenttypes.PaymentMethods, error) {
	actx := auth.AuthConText(ctx)
	return s.store.PaymentMethods().Get(actx, paymentMethodId)
}

func (s *paymentMethodSvc) List(ctx context.Context, params paymenttypes.QueryPaymentMethodParams) ([]paymenttypes.PaymentMethods, error) {
	actx := auth.AuthConText(ctx)
	return s.store.PaymentMethods().List(actx, params)
}

func (s *paymentMethodSvc) Update(ctx context.Context, paymentMethodId string, req paymenttypes.UpdatePaymentMethodReq) error {
	actx := auth.AuthConText(ctx)
	return s.store.PaymentMethods().Update(actx, paymentMethodId, req)
}

func (s *paymentMethodSvc) Delete(ctx context.Context, paymentMethodId string) error {
	actx := auth.AuthConText(ctx)
	return s.store.PaymentMethods().Delete(actx, paymentMethodId)
}
