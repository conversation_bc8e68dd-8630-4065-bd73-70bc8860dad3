package models

import (
	commontypes "pebble/internal/webapp/types/common"
	tagtypes "pebble/internal/webapp/types/tag"
	"pebble/pkg/auth"
)

type ITag interface {
	CreateTag(actx *auth.Ctx, req tagtypes.CreateTagReq) (*tagtypes.Tag, error)
	UpdateTag(actx *auth.Ctx, tagId string, req tagtypes.UpdateTagReq) error
	DeleteTag(actx *auth.Ctx, tagId string) error
	GetTag(actx *auth.Ctx, tagId string) (*tagtypes.Tag, error)
	ListTags(actx *auth.Ctx, params tagtypes.QueryTagsParams) ([]tagtypes.Tag, error)
}

type ClientTag interface {
	BatchCreateClientTag(actx *auth.Ctx, req tagtypes.CreateClientTagReq) ([]tagtypes.ClientTag, error)
	UpdateClientTagStatus(actx *auth.Ctx, clientId string, tagIds []string, status commontypes.Status) error
	GetClientTags(actx *auth.Ctx, condition tagtypes.GetClientTagCondition) ([]tagtypes.ClientTag, error)
}
