package models

import (
	appointmenttypes "pebble/internal/webapp/types/appointment"
	commontypes "pebble/internal/webapp/types/common"
	"pebble/pkg/auth"
)

type IAppointment interface {
	Create(actx *auth.Ctx, data appointmenttypes.AppointmentItem) (*appointmenttypes.Appointment, error)
	Get(actx *auth.Ctx, appointmentId string) (*appointmenttypes.Appointment, error)
	GetByAppointmentIds(actx *auth.Ctx, appointmentIds []string) ([]appointmenttypes.Appointment, error)
	List(actx *auth.Ctx, where appointmenttypes.GetAppointmentConditions) ([]appointmenttypes.Appointment, int64, error) // ListByTimeline(actx *auth.Ctx, where appointmenttypes.GetAppointmentTimelineConditions) ([]appointmenttypes.Appointment, error)
	ListByDate(actx *auth.Ctx, date string) ([]appointmenttypes.Appointment, error)
	ListLatestForEachClient(actx *auth.Ctx) ([]appointmenttypes.Appointment, error)
	Update(actx *auth.Ctx, appointmentId string, data appointmenttypes.UpdateAppointmentReq) error
	Delete(actx *auth.Ctx, appointmentId string) error
}

type IAppointmentService interface {
	Create(actx *auth.Ctx, data appointmenttypes.CreateAppointmentServiceReq) (*appointmenttypes.AppointmentService, error)
	BatchCreate(actx *auth.Ctx, items []appointmenttypes.AppointmentServiceEntity) ([]appointmenttypes.AppointmentService, error)
	Get(actx *auth.Ctx, appointmentServiceId string) ([]appointmenttypes.AppointmentService, error)
	GetByAppointmentIds(actx *auth.Ctx, appointmentIds []string) ([]appointmenttypes.AppointmentService, error)
	Update(actx *auth.Ctx, appointmentId, appointmentServiceId string, data appointmenttypes.UpdateAppointmentServiceReq) error
	Delete(actx *auth.Ctx, appointmentId string) error
	BatchDelete(actx *auth.Ctx, appointmentId string, appointmentServiceIds []string) error
	UpdateAccountIdByAppointmentId(actx *auth.Ctx, appointmentId, accountId string) error
	UpdateAccountId(actx *auth.Ctx, appointmentId, srcAccountId, dstccountId string) error
	UpdateAccountAndScheduledStartTime(actx *auth.Ctx, appointmentId string, data appointmenttypes.UpdateAccountAndTime) error
	BatchUpdateTarget(actx *auth.Ctx, appointmentId string, data appointmenttypes.AssignmentAppointmentServiceReq) error
}

type IAppointmentNote interface {
	CreateAppointmentNote(actx *auth.Ctx, req appointmenttypes.CreateAppointmentNoteReq) (*appointmenttypes.AppointmentNote, error)
	UpdateAppointmentNote(actx *auth.Ctx, appointmentId string, req appointmenttypes.UpdateAppointmentNoteReq) error // DeleteAppointmentNote(actx *auth.Ctx, appointmentId string) error
	GetByAppointmentId(actx *auth.Ctx, appointmentId string) (*appointmenttypes.AppointmentNote, error)
	GetByAppointmentIds(actx *auth.Ctx, appointmentIds []string) ([]appointmenttypes.AppointmentNote, error)
}

type IAppointmentTip interface {
	GetByAppointmentIds(actx *auth.Ctx, appointmentIds []string) ([]appointmenttypes.AppointmentTip, error)
	Update(actx *auth.Ctx, appointmentId, appointmentTipId string, data appointmenttypes.UpdateTipReq) error
	BatchUpdatePaymentStatus(actx *auth.Ctx, appointmentId string, appointmentTipIds []string, paymentStatus commontypes.PaymentRecordStatus) error
	Delete(actx *auth.Ctx, appointmentId string) error
	BatchDelete(actx *auth.Ctx, appointmentId string, appointmentTipIds []string) error
	BatchCreate(actx *auth.Ctx, items []appointmenttypes.CreateAppointmentTipReq) ([]appointmenttypes.AppointmentTip, error)
	Create(actx *auth.Ctx, item appointmenttypes.CreateAppointmentTipReq) (*appointmenttypes.AppointmentTip, error)
}

type IAppointmentGuest interface {
	Create(actx *auth.Ctx, data appointmenttypes.AppointmentGuestEntity) (*appointmenttypes.AppointmentGuest, error)
	BatchCreate(actx *auth.Ctx, items []appointmenttypes.AppointmentGuestEntity) ([]appointmenttypes.AppointmentGuest, error)
	Get(actx *auth.Ctx, appointmentId string) ([]appointmenttypes.AppointmentGuest, error)
	GetByAppointmentIds(actx *auth.Ctx, appointmentIds []string) ([]appointmenttypes.AppointmentGuest, error)
}
