package models

import (
	"context"
	"pebble/internal/webapp/types/stripe"
	"pebble/pkg/auth"
)

type IStripeLocations interface {
	Create(actx *auth.Ctx, data stripe.StripeLocationsEntity) (*stripe.StripeLocations, error)
	List(actx *auth.Ctx) ([]stripe.StripeLocations, error)
	GetByStripeLocationId(actx *auth.Ctx, stripeLocationId string) (*stripe.StripeLocations, error)
	GetByStripeLocationIds(actx *auth.Ctx, stripeLocationIds []string) ([]stripe.StripeLocations, error)
}

type IStripeSettings interface {
	Create(actx *auth.Ctx, data stripe.StripeSettingEntity) (*stripe.StripeSetting, error)
	Update(actx *auth.Ctx, data stripe.UpdateStripeSettingReq) error
	Get(actx *auth.Ctx) (*stripe.StripeSetting, error)
}

type IStripeTerminal interface {
	Create(actx *auth.Ctx, data stripe.StripeTerminalEntity) (*stripe.StripeTerminal, error)
	List(actx *auth.Ctx) ([]stripe.StripeTerminal, error)
}

type IStripePaymentIntent interface {
	Create(actx *auth.Ctx, data stripe.StripePaymentIntentEntity) (*stripe.StripePaymentIntent, error)
	AdminGetByIntentId(ctx context.Context, intentId string) (*stripe.StripePaymentIntent, error)
	AdminUpdateIntentStatus(ctx context.Context, intentId string, status stripe.PaymentIntentStatus) error
	AdminUpdateIntent(ctx context.Context, intentId string, req stripe.UpdateStripePaymentIntentReq) error
}
