package sqlserver

import (
	"context"
	"pebble/internal/webapp/types"
	"pebble/pkg/auth"
)

type tenantMemberTable struct {
	BaseTable
}

func NewTenantMemberTable() *tenantMemberTable {
	table := &tenantMemberTable{}
	table.SetTableName("tenant_members")
	return table
}

func (t *tenantMemberTable) AdminGetTenantMemberByAccountId(ctx context.Context, accountId string) ([]types.TenantMember, error) {
	var tenantMembers []types.TenantMember
	err := t.NoLimitTx(ctx).Where("account_id = ?", accountId).
		Order("is_owner DESC, id DESC").
		Find(&tenantMembers).Error
	if err != nil {
		return nil, err
	}
	return tenantMembers, nil
}

func (t *tenantMemberTable) GetTenantMemberByAccountId(actx *auth.Ctx, accountId string) (*types.TenantMember, error) {
	var tenantMembers types.TenantMember
	err := t.LimitLocationTx(actx).Where("account_id = ?", accountId).
		Order("is_owner DESC, id DESC").
		First(&tenantMembers).Error
	if err != nil {
		return nil, err
	}
	return &tenantMembers, nil
}

func (t *tenantMemberTable) GetTenantMemberByAccountIds(actx *auth.Ctx, accountIds []string) ([]types.TenantMember, error) {
	var tenantMembers []types.TenantMember
	err := t.LimitLocationTx(actx).Where("account_id IN ?", accountIds).
		Order("is_owner DESC, id DESC").
		Find(&tenantMembers).Error
	if err != nil {
		return nil, err
	}
	return tenantMembers, nil
}

func (t *tenantMemberTable) GetTenantMembers(actx *auth.Ctx) ([]types.TenantMember, error) {
	var tenantMembers []types.TenantMember
	err := t.LimitLocationTx(actx).Order("sort_index ASC, is_owner DESC, id DESC").Find(&tenantMembers).Error
	if err != nil {
		return nil, err
	}
	return tenantMembers, nil
}

func (t *tenantMemberTable) AdminCreateTenantMember(ctx context.Context, data types.TenantMemberEntity) (types.TenantMember, error) {
	tenantMember := types.TenantMember{
		TenantMemberEntity: data,
	}
	err := t.NoLimitTx(ctx).Create(&tenantMember).Error
	if err != nil {
		return types.TenantMember{}, err
	}
	return tenantMember, nil
}

func (t *tenantMemberTable) CreateTenantMember(actx *auth.Ctx, data types.TenantMemberEntity) (types.TenantMember, error) {
	data.TenantId = actx.TenantId
	data.TenantId = actx.TenantId

	return t.AdminCreateTenantMember(actx.Context(), data)
}

func (t *tenantMemberTable) DeleteTenantMemberByAccountId(actx *auth.Ctx, accountId string) error {
	return t.LimitLocationTx(actx).Where("account_id = ?", accountId).Delete(&types.TenantMember{}).Error
}

func (t *tenantMemberTable) UpdateTenantMember(actx *auth.Ctx, accountId string, data types.UpdateTenantMemberReq) error {
	return t.LimitLocationTx(actx).Where("account_id = ?", accountId).Updates(&data).Error
}
