package sqlserver

import (
	"context"
	"pebble/pkg/auth"
	"pebble/pkg/udb"

	"gorm.io/gorm"
)

type BaseTable struct {
	nameTable string
}

func (b *BaseTable) SetTableName(name string) {
	b.nameTable = name
}

func (b *BaseTable) GetTableName() string {
	return b.nameTable
}

func (b *BaseTable) NoLimitTx(ctx context.Context) *gorm.DB {
	return udb.WrapDB(ctx).Table(b.nameTable)
}

func (b *BaseTable) LimitTenantTx(actx *auth.Ctx) *gorm.DB {
	return b.NoLimitTx(actx.Context()).Scopes(tenantLimit(actx))
}

func (b *BaseTable) LimitLocationTx(actx *auth.Ctx) *gorm.DB {
	return b.NoLimitTx(actx.Context()).Scopes(locationLimit(actx))
}

func locationLimit(actx *auth.Ctx) func(db *gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		return db.Where("tenant_id = ?", actx.TenantId).Where("location_id = ?", actx.LocationId)
	}
}

func tenantLimit(actx *auth.Ctx) func(db *gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		return db.Where("tenant_id = ?", actx.TenantId)
	}
}
