package message

import (
	commonsql "pebble/internal/webapp/models/sqlserver/common"
	"pebble/internal/webapp/types/message"
	"pebble/pkg/auth"
)

type messageTemplateTable struct {
	commonsql.BaseTable
}

func NewMessageTemplateTables() *messageTemplateTable {
	table := &messageTemplateTable{}
	table.SetTableName("message_templates")
	return table
}

func (t *messageTemplateTable) Create(actx *auth.Ctx, template *message.MessageTemplate) (*message.MessageTemplate, error) {
	err := t.NoLimitTx(actx.Context()).Create(template).Error
	if err != nil {
		return nil, err
	}
	return template, nil
}

func (t *messageTemplateTable) Get(actx *auth.Ctx, templateType message.TemplateType) (*message.MessageTemplate, error) {
	var template message.MessageTemplate
	err := t.LimitLocationTx(actx).Where("template_type = ?", templateType).First(&template).Error
	if err != nil {
		return nil, err
	}
	return &template, nil
}

func (t *messageTemplateTable) List(actx *auth.Ctx, locationID string) ([]message.MessageTemplate, error) {
	var templates []message.MessageTemplate

	// Find templates for the specific location OR tenant-wide templates
	query := t.LimitTenantTx(actx).Where("location_id = ? OR location_id = ''", locationID)

	err := query.Find(&templates).Error
	if err != nil {
		return nil, err
	}
	return templates, nil
}

func (t *messageTemplateTable) Update(actx *auth.Ctx, templateType message.TemplateType, req message.UpdateMessageTemplateReq) error {
	return t.LimitLocationTx(actx).
		Where("template_type = ?", templateType).
		Updates(req).Error
}

// Delete removes a message template from the database.
/*
func (t *MessageTemplateTables) Delete(actx *auth.Ctx, eventType string) error {
	return t.LimitLocationTx(actx).
		Where("event_type = ?", eventType).
		Delete(&message.MessageTemplate{}).Error
}
*/
