package sqlserver

import (
	"encoding/json"
	"pebble/internal/webapp/types"
	"pebble/pkg/auth"
)

type staffScheduleOverrideTables struct {
	BaseTable
}

func NewStaffScheduleOverrideTables() *staffScheduleOverrideTables {
	table := &staffScheduleOverrideTables{}
	table.SetTableName("staff_schedule_overrides")
	return table
}

func (t *staffScheduleOverrideTables) List(actx *auth.Ctx, params types.QueryMultiStaffSchedulesDateParams) ([]types.StaffScheduleOverride, error) {
	var staffScheduleOverrides []types.StaffScheduleOverride
	db := t.LimitLocationTx(actx)
	if len(params.AccountIds) == 0 {
		db = db.Where("account_id IN ?", params.AccountIds)
	}
	if params.StartDate != nil {
		db = db.Where("date >= ?", params.StartDate)
	}
	if params.EndDate != nil {
		db = db.Where("date <= ?", params.EndDate)
	}
	err := db.Find(&staffScheduleOverrides).Error
	return staffScheduleOverrides, err
}

func (t *staffScheduleOverrideTables) BatchCreate(actx *auth.Ctx, data []types.CreateStaffScheduleOverrideReq) ([]types.StaffScheduleOverride, error) {
	if len(data) == 0 {
		return []types.StaffScheduleOverride{}, nil
	}

	var staffScheduleOverrides []types.StaffScheduleOverride
	for _, d := range data {
		staffScheduleOverride := types.StaffScheduleOverride{
			StaffScheduleOverrideEntity: types.StaffScheduleOverrideEntity{
				TenantID:     actx.TenantId,
				LocationID:   actx.LocationId,
				AccountID:    d.AccountID,
				Date:         d.Date,
				Reason:       d.Reason,
				WorkingTimes: d.WorkingTimes,
				IsDayOff:     d.IsDayOff,
			},
		}

		staffScheduleOverrides = append(staffScheduleOverrides, staffScheduleOverride)
	}

	err := t.NoLimitTx(actx.Context()).Create(&staffScheduleOverrides).Error
	return staffScheduleOverrides, err
}

func (t *staffScheduleOverrideTables) BatchUpdate(actx *auth.Ctx, data types.BatchUpdateStaffScheduleOverrideReq) error {
	var needUpdate bool
	var updateItem types.UpdateStaffScheduleOverrideItem
	if data.IsDayOff != nil {
		updateItem.IsDayOff = data.IsDayOff
		needUpdate = true
	}
	if len(data.WorkingTimes) > 0 {
		b, _ := json.Marshal(data.WorkingTimes)
		workingTime := string(b)
		updateItem.WorkingTime = &workingTime
		needUpdate = true
	}

	if !needUpdate {
		return nil
	}
	return t.LimitLocationTx(actx).Where("account_id IN ?", data.AccountID).
		Where("date IN ?", data.Dates).
		Updates(updateItem).Error
}

func (t *staffScheduleOverrideTables) GetByAccountIds(actx *auth.Ctx, accountIds []string) ([]types.StaffScheduleOverride, error) {
	if len(accountIds) == 0 {
		return []types.StaffScheduleOverride{}, nil
	}

	var staffScheduleOverrides []types.StaffScheduleOverride
	err := t.LimitLocationTx(actx).Where("account_id IN ?", accountIds).Find(&staffScheduleOverrides).Error
	if err != nil {
		return nil, err
	}

	return staffScheduleOverrides, nil
}
