package sqlserver

import (
	"context"
	"pebble/internal/webapp/types"
	"pebble/pkg/auth"
	"pebble/pkg/uid"
)

type accountTable struct {
	BaseTable
}

func NewAccountTable() *accountTable {
	table := &accountTable{}
	table.SetTableName("accounts")
	return table
}

func (a *accountTable) AdminGetAccountByEmail(ctx context.Context, email string) (types.Account, error) {
	var data types.Account
	err := a.NoLimitTx(ctx).Where("email = ?", email).First(&data).Error
	if err != nil {
		return data, err
	}
	return data, nil
}

func (a *accountTable) AdminGetAccountByPhoneNumber(ctx context.Context, phoneNumber string) (types.Account, error) {
	var data types.Account
	err := a.NoLimitTx(ctx).Where("phone_number = ?", phoneNumber).First(&data).Error
	if err != nil {
		return data, err
	}
	return data, nil
}

func (a *accountTable) AdminGetAccountById(ctx context.Context, accountId string) (types.Account, error) {
	var data types.Account
	err := a.NoLimitTx(ctx).Where("account_id = ?", accountId).First(&data).Error
	if err != nil {
		return data, err
	}
	return data, nil
}

func (a *accountTable) AdminCreateAccount(ctx context.Context, data types.AccountEntity) (types.Account, error) {
	account := types.Account{
		AccountEntity: data,
	}
	account.AccountId = uid.GenerateAccountId()
	err := a.NoLimitTx(ctx).Create(&account).Error
	if err != nil {
		return account, err
	}
	return account, nil
}

func (a *accountTable) AdminGetAccountByIds(ctx context.Context, accountIds []string) ([]types.Account, error) {
	if len(accountIds) == 0 {
		return []types.Account{}, nil
	}
	var accounts []types.Account
	err := a.NoLimitTx(ctx).Where("account_id IN ?", accountIds).Find(&accounts).Error
	if err != nil {
		return nil, err
	}
	return accounts, nil
}

func (a *accountTable) GetAccount(actx *auth.Ctx) (types.Account, error) {
	var data types.Account
	err := a.NoLimitTx(actx.Context()).Where("account_id = ?", actx.AccountId).First(&data).Error
	if err != nil {
		return data, err
	}
	return data, nil
}

func (a *accountTable) UpdateAccount(actx *auth.Ctx, data types.UpdateAccountReq) error {
	err := a.NoLimitTx(actx.Context()).Where("account_id = ?", actx.AccountId).Updates(data).Error
	if err != nil {
		return err
	}
	return nil
}
