package appointmentsql

import (
	commonsql "pebble/internal/webapp/models/sqlserver/common"
	appointmenttypes "pebble/internal/webapp/types/appointment"
	"pebble/pkg/auth"
	"pebble/pkg/uid"

	"gorm.io/gorm"
)

type appointmentServiceTable struct {
	commonsql.BaseTable
}

func NewAppointmentServiceTable() *appointmentServiceTable {
	table := &appointmentServiceTable{}
	table.SetTableName("appointment_services")
	return table
}

func (a *appointmentServiceTable) Create(actx *auth.Ctx, data appointmenttypes.CreateAppointmentServiceReq) (*appointmenttypes.AppointmentService, error) {
	appointmentService := appointmenttypes.AppointmentService{
		AppointmentServiceEntity: appointmenttypes.AppointmentServiceEntity{
			TenantId:      actx.TenantId,
			LocationId:    actx.LocationId,
			AccountId:     data.AccountId,
			AppointmentId: data.AppointmentId,
			ServiceId:     data.ServiceId,
			ApptServiceId: uid.GenerateAppointmentServiceId(),
			TargetType:    data.TargetType,
			TargetId:      data.TargetId,
			Name:          data.Name,
			Duration:      data.Duration,
			TaxRate:       data.TaxRate,
			Price:         data.Price,
			OriginTaxId:   data.OriginTaxId,
			OriginTaxRate: data.OriginTaxRate,
			OriginPrice:   data.OriginPrice,
		},
	}

	err := a.NoLimitTx(actx.Context()).Create(&appointmentService).Error
	if err != nil {
		return nil, err
	}
	return &appointmentService, nil
}

func (s *appointmentServiceTable) BatchCreate(actx *auth.Ctx, items []appointmenttypes.AppointmentServiceEntity) ([]appointmenttypes.AppointmentService, error) {
	if len(items) == 0 {
		return []appointmenttypes.AppointmentService{}, nil
	}
	var services []appointmenttypes.AppointmentService

	for _, item := range items {
		// service := appointmenttypes.AppointmentService{

		// 	AppointmentServiceEntity: appointmenttypes.AppointmentServiceEntity{
		// 		TenantId:           actx.TenantId,
		// 		LocationId:         actx.LocationId,
		// 		AccountId:          item.AccountId,
		// 		AppointmentId:      item.AppointmentId,
		// 		ServiceId:          item.ServiceId,
		// 		ScheduledStartTime: item.ScheduledStartTime,
		// 		ApptServiceId:      uid.GenerateAppointmentServiceId(),
		// 		TargetType:         item.TargetType,
		// 		TargetId:           item.TargetId,
		// 		Name:               item.Name,
		// 		Duration:           item.Duration,
		// 		TaxRate:            item.TaxRate,
		// 		Price:              item.Price,
		// 		OriginTaxId:        item.OriginTaxId,
		// 		OriginTaxRate:      item.OriginTaxRate,
		// 		OriginPrice:        item.OriginPrice,
		// 		ServiceType:        item.ServiceType,
		// 		Snapshot:           item.Snapshot,
		// 	},
		// }
		service := appointmenttypes.AppointmentService{
			AppointmentServiceEntity: item,
		}
		service.LocationId = actx.LocationId
		service.TenantId = actx.TenantId
		service.ApptServiceId = uid.GenerateAppointmentServiceId()
		services = append(services, service)
	}

	err := s.NoLimitTx(actx.Context()).Create(&services).Error
	if err != nil {
		return nil, err
	}

	return services, nil
}

func (a *appointmentServiceTable) Get(actx *auth.Ctx, appointmentId string) ([]appointmenttypes.AppointmentService, error) {
	var appointmentService []appointmenttypes.AppointmentService
	err := a.LimitLocationTx(actx).Where("appointment_id = ?", appointmentId).Find(&appointmentService).Error
	if err != nil {
		return nil, err
	}
	return appointmentService, nil
}

func (a *appointmentServiceTable) GetByAppointmentIds(actx *auth.Ctx, appointmentIds []string) ([]appointmenttypes.AppointmentService, error) {
	if len(appointmentIds) == 0 {
		return []appointmenttypes.AppointmentService{}, nil
	}
	var appointmentServices []appointmenttypes.AppointmentService
	err := a.LimitLocationTx(actx).Where("appointment_id in (?)", appointmentIds).Find(&appointmentServices).Error
	if err != nil {
		return nil, err
	}
	return appointmentServices, nil
}

func (a *appointmentServiceTable) Update(actx *auth.Ctx, appointmentId, appointmentServiceId string, data appointmenttypes.UpdateAppointmentServiceReq) error {
	return a.LimitLocationTx(actx).Where("appointment_id = ? and appt_service_id = ?", appointmentId, appointmentServiceId).Updates(data).Error
}

func (a *appointmentServiceTable) UpdateAccountIdByAppointmentId(actx *auth.Ctx, appointmentId, accountId string) error {
	return a.LimitLocationTx(actx).Where("appointment_id = ?", appointmentId).Update("account_id", accountId).Error
}

func (a *appointmentServiceTable) UpdateAccountId(actx *auth.Ctx, appointmentId, srcAccountId, dstccountId string) error {
	return a.LimitLocationTx(actx).Where("appointment_id = ? and account_id = ?", appointmentId, srcAccountId).Update("account_id", dstccountId).Error
}

func (a *appointmentServiceTable) UpdateAccountAndScheduledStartTime(actx *auth.Ctx, appointmentId string, data appointmenttypes.UpdateAccountAndTime) error {
	updateData := map[string]interface{}{
		"account_id":           data.DstAccountId,
		"scheduled_start_time": data.ScheduledStartTime,
	}
	return a.LimitLocationTx(actx).Where("appointment_id = ? and account_id = ?", appointmentId, data.SrcAccountId).Updates(updateData).Error
}

func (a *appointmentServiceTable) Delete(actx *auth.Ctx, appointmentId string) error {
	return a.LimitLocationTx(actx).Where("appointment_id = ?", appointmentId).Delete(&appointmenttypes.Appointment{}).Error
}

func (a *appointmentServiceTable) BatchDelete(actx *auth.Ctx, appointmentId string, appointmentServiceIds []string) error {
	return a.LimitLocationTx(actx).Where("appointment_id = ? and appt_service_id in (?)", appointmentId, appointmentServiceIds).Delete(&appointmenttypes.Appointment{}).Error
}

func (s *appointmentServiceTable) BatchUpdateTarget(actx *auth.Ctx, appointmentId string, data appointmenttypes.AssignmentAppointmentServiceReq) error {
	return s.LimitLocationTx(actx).Transaction(func(tx *gorm.DB) error {
		for _, item := range data.Items {
			err := tx.Model(&appointmenttypes.AppointmentService{}).
				Where("appointment_id = ?", appointmentId).
				Where("appt_service_id = ?", item.ApptServiceId).
				Update("target_id", item.TargetId).Error
			if err != nil {
				return err
			}
		}
		return nil
	})
}
