package appointmentsql

import (
	commonsql "pebble/internal/webapp/models/sqlserver/common"
	appointmenttypes "pebble/internal/webapp/types/appointment"
	commontypes "pebble/internal/webapp/types/common"
	"pebble/pkg/auth"
	"pebble/pkg/uid"
)

type appointmentTipTable struct {
	commonsql.BaseTable
}

func NewAppointmentTipTable() *appointmentTipTable {
	table := &appointmentTipTable{}
	table.SetTableName("appointment_tips")
	return table
}

func (a *appointmentTipTable) GetByAppointmentIds(actx *auth.Ctx, appointmentIds []string) ([]appointmenttypes.AppointmentTip, error) {
	if len(appointmentIds) == 0 {
		return []appointmenttypes.AppointmentTip{}, nil
	}
	var appointmentTips []appointmenttypes.AppointmentTip
	err := a.LimitLocationTx(actx).Where("appointment_id in (?)", appointmentIds).Find(&appointmentTips).Error
	if err != nil {
		return nil, err
	}
	return appointmentTips, nil
}

func (a *appointmentTipTable) Update(actx *auth.Ctx, appointmentId, appointmentTipId string, data appointmenttypes.UpdateTipReq) error {
	return a.LimitLocationTx(actx).Where("appointment_id = ? and appt_tip_id = ?", appointmentId, appointmentTipId).Updates(data).Error
}

func (a *appointmentTipTable) BatchUpdatePaymentStatus(actx *auth.Ctx, appointmentId string, appointmentTipIds []string, paymentStatus commontypes.PaymentRecordStatus) error {
	if len(appointmentTipIds) == 0 {
		return nil
	}
	return a.LimitLocationTx(actx).Where("appointment_id = ? and appt_tip_id in (?)", appointmentId, appointmentTipIds).Update("payment_status", paymentStatus).Error
}

func (a *appointmentTipTable) Delete(actx *auth.Ctx, appointmentId string) error {
	return a.LimitLocationTx(actx).Where("appointment_id = ?", appointmentId).Delete(&appointmenttypes.AppointmentTip{}).Error
}

func (a *appointmentTipTable) DeleteByApptTipId(actx *auth.Ctx, appointmentId, apptTipId string) error {
	return a.LimitLocationTx(actx).Where("appointment_id = ? and appt_tip_id = ?", appointmentId, apptTipId).Delete(&appointmenttypes.AppointmentTip{}).Error
}

func (a *appointmentTipTable) BatchDelete(actx *auth.Ctx, appointmentId string, appointmentTipIds []string) error {
	return a.LimitLocationTx(actx).Where("appointment_id = ? and appt_tip_id in (?)", appointmentId, appointmentTipIds).Delete(&appointmenttypes.AppointmentTip{}).Error
}

func (s *appointmentTipTable) BatchCreate(actx *auth.Ctx, items []appointmenttypes.CreateAppointmentTipReq) ([]appointmenttypes.AppointmentTip, error) {
	if len(items) == 0 {
		return []appointmenttypes.AppointmentTip{}, nil
	}
	var tips []appointmenttypes.AppointmentTip

	for _, item := range items {
		tip := appointmenttypes.AppointmentTip{
			AppointmentTipEntity: appointmenttypes.AppointmentTipEntity{
				TenantId:      actx.TenantId,
				LocationId:    actx.LocationId,
				AppointmentId: item.AppointmentId,
				ApptTipId:     uid.GenerateAppointmentTipId(),
				AccountId:     item.AccountId,
				ClientId:      item.ClientId,
				AmountTip:     item.AmountTip,
				PaymentStatus: item.PaymentStatus,
			},
		}
		tips = append(tips, tip)
	}

	err := s.NoLimitTx(actx.Context()).Create(&tips).Error
	if err != nil {
		return nil, err
	}

	return tips, nil
}

func (s *appointmentTipTable) Create(actx *auth.Ctx, item appointmenttypes.CreateAppointmentTipReq) (*appointmenttypes.AppointmentTip, error) {
	tip := appointmenttypes.AppointmentTip{
		AppointmentTipEntity: appointmenttypes.AppointmentTipEntity{
			TenantId:      actx.TenantId,
			LocationId:    actx.LocationId,
			AppointmentId: item.AppointmentId,
			ApptTipId:     uid.GenerateAppointmentTipId(),
			AccountId:     item.AccountId,
			ClientId:      item.ClientId,
			AmountTip:     item.AmountTip,
			PaymentStatus: item.PaymentStatus,
		}}

	err := s.NoLimitTx(actx.Context()).Create(&tip).Error
	if err != nil {
		return nil, err
	}

	return &tip, nil
}
