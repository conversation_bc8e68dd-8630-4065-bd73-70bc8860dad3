package appointmentsql

import (
	commonsql "pebble/internal/webapp/models/sqlserver/common"
	appointmenttypes "pebble/internal/webapp/types/appointment"
	"pebble/pkg/auth"
)

type appointmentNoteTable struct {
	commonsql.BaseTable
}

func NewAppointmentNoteTable() *appointmentNoteTable {
	table := &appointmentNoteTable{}
	table.SetTableName("appointment_notes")
	return table
}

func (a *appointmentNoteTable) CreateAppointmentNote(actx *auth.Ctx, req appointmenttypes.CreateAppointmentNoteReq) (*appointmenttypes.AppointmentNote, error) {
	var appointmentNote appointmenttypes.AppointmentNote
	appointmentNote.AppointmentId = req.AppointmentId
	appointmentNote.Note = req.Note
	appointmentNote.TenantId = actx.TenantId
	appointmentNote.LocationId = actx.LocationId

	err := a.NoLimitTx(actx.Context()).Create(&appointmentNote).Error
	if err != nil {
		return nil, err
	}

	return &appointmentNote, nil
}

func (a *appointmentNoteTable) UpdateAppointmentNote(actx *auth.Ctx, appointmentId string, req appointmenttypes.UpdateAppointmentNoteReq) error {
	return a.LimitLocationTx(actx).Where("appointment_id = ?", appointmentId).Updates(req).Error
}

// func (a *appointmentNoteTable) DeleteAppointmentNote(actx *auth.Ctx, appointmentNoteId string) error {
// 	return a.LimitLocationTx(actx).Where("appointment_id = ?", appointmentNoteId).Delete(&appointmenttypes.AppointmentNote{}).Error
// }

func (a *appointmentNoteTable) GetByAppointmentId(actx *auth.Ctx, appointmentId string) (*appointmenttypes.AppointmentNote, error) {
	var appointmentNote appointmenttypes.AppointmentNote
	err := a.LimitLocationTx(actx).Where("appointment_id = ?", appointmentId).First(&appointmentNote).Error
	return &appointmentNote, err
}

func (a *appointmentNoteTable) GetByAppointmentIds(actx *auth.Ctx, appointmentIds []string) ([]appointmenttypes.AppointmentNote, error) {
	if len(appointmentIds) == 0 {
		return []appointmenttypes.AppointmentNote{}, nil
	}

	var appointmentNotes []appointmenttypes.AppointmentNote
	err := a.LimitLocationTx(actx).Where("appointment_id in (?)", appointmentIds).Find(&appointmentNotes).Error
	return appointmentNotes, err
}
