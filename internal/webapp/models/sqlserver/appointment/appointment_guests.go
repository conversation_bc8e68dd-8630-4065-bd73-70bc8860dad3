package appointmentsql

import (
	commonsql "pebble/internal/webapp/models/sqlserver/common"
	appointmenttypes "pebble/internal/webapp/types/appointment"
	"pebble/pkg/auth"
)

type appointmentGuestTable struct {
	commonsql.BaseTable
}

func NewAppointmentGuestTable() *appointmentGuestTable {
	table := &appointmentGuestTable{}
	table.SetTableName("appointment_guests")
	return table
}

func (a *appointmentGuestTable) Create(actx *auth.Ctx, data appointmenttypes.AppointmentGuestEntity) (*appointmenttypes.AppointmentGuest, error) {
	appointmentGuest := appointmenttypes.AppointmentGuest{
		AppointmentGuestEntity: appointmenttypes.AppointmentGuestEntity{
			TenantId:      actx.TenantId,
			LocationId:    actx.LocationId,
			AppointmentId: data.AppointmentId,
			ClientId:      data.ClientId,
		},
	}

	err := a.NoLimitTx(actx.Context()).Create(&appointmentGuest).Error
	if err != nil {
		return nil, err
	}
	return &appointmentGuest, nil
}

func (s *appointmentGuestTable) BatchCreate(actx *auth.Ctx, items []appointmenttypes.AppointmentGuestEntity) ([]appointmenttypes.AppointmentGuest, error) {
	if len(items) == 0 {
		return []appointmenttypes.AppointmentGuest{}, nil
	}
	var appointmentGuests []appointmenttypes.AppointmentGuest

	for _, item := range items {
		appointmentGuest := appointmenttypes.AppointmentGuest{
			AppointmentGuestEntity: item,
		}
		appointmentGuest.LocationId = actx.LocationId
		appointmentGuest.TenantId = actx.TenantId
		appointmentGuests = append(appointmentGuests, appointmentGuest)
	}

	err := s.NoLimitTx(actx.Context()).Create(&appointmentGuests).Error
	if err != nil {
		return nil, err
	}

	return appointmentGuests, nil
}

func (a *appointmentGuestTable) Get(actx *auth.Ctx, appointmentId string) ([]appointmenttypes.AppointmentGuest, error) {
	var appointmentGuests []appointmenttypes.AppointmentGuest
	err := a.LimitLocationTx(actx).Where("appointment_id = ?", appointmentId).Find(&appointmentGuests).Error
	if err != nil {
		return nil, err
	}
	return appointmentGuests, nil
}

func (a *appointmentGuestTable) GetByAppointmentIds(actx *auth.Ctx, appointmentIds []string) ([]appointmenttypes.AppointmentGuest, error) {
	if len(appointmentIds) == 0 {
		return []appointmenttypes.AppointmentGuest{}, nil
	}
	var appointmentGuests []appointmenttypes.AppointmentGuest
	err := a.LimitLocationTx(actx).Where("appointment_id in (?)", appointmentIds).Find(&appointmentGuests).Error
	if err != nil {
		return nil, err
	}
	return appointmentGuests, nil
}
