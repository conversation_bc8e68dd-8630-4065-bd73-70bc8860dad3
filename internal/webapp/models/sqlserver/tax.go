package sqlserver

import (
	"pebble/internal/webapp/types"
	"pebble/pkg/auth"
	"pebble/pkg/uid"
)

type taxTables struct {
	BaseTable
}

func NewTaxTables() *taxTables {
	table := &taxTables{}
	table.SetTableName("taxes")
	return table
}

func (t *taxTables) Create(actx *auth.Ctx, data types.TaxEntity) (*types.Tax, error) {
	tax := types.Tax{
		TaxEntity: data,
	}
	tax.TaxId = uid.GenerateTaxId()
	tax.TenantId = actx.TenantId
	tax.LocationId = actx.LocationId
	err := t.NoLimitTx(actx.Context()).Create(&tax).Error
	if err != nil {
		return nil, err
	}

	return &tax, nil
}

func (s *taxTables) Get(actx *auth.Ctx, taxId string) (*types.Tax, error) {
	var tax types.Tax
	err := s.LimitLocationTx(actx).Where("tax_id = ?", taxId).First(&tax).Error
	if err != nil {
		return nil, err
	}
	return &tax, nil
}

func (s *taxTables) GetByIds(actx *auth.Ctx, taxIds []string) ([]types.Tax, error) {
	if len(taxIds) == 0 {
		return []types.Tax{}, nil
	}

	var taxes []types.Tax
	err := s.LimitLocationTx(actx).Where("tax_id in (?)", taxIds).Find(&taxes).Error
	if err != nil {
		return nil, err
	}
	return taxes, nil
}

func (s *taxTables) List(actx *auth.Ctx) ([]*types.Tax, error) {
	var taxes []*types.Tax
	err := s.LimitLocationTx(actx).Find(&taxes).Error
	if err != nil {
		return nil, err
	}

	return taxes, nil
}

func (s *taxTables) Update(actx *auth.Ctx, taxId string, tax types.UpdateTaxReq) error {
	return s.LimitLocationTx(actx).Where("tax_id = ?", taxId).Updates(tax).Error
}

func (s *taxTables) Delete(actx *auth.Ctx, taxId string) error {
	return s.LimitLocationTx(actx).Where("tax_id = ?", taxId).Delete(&types.Tax{}).Error
}
