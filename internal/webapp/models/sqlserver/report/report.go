package reportsql

import (
	"pebble/internal/webapp/models"
	commonsql "pebble/internal/webapp/models/sqlserver/common"
	reporttypes "pebble/internal/webapp/types/report"
	"pebble/pkg/auth"
)

type reprot struct {
	commonsql.BaseTable
}

func NewReport() models.IReports {
	table := &reprot{}
	return table
}

func (r *reprot) GetStaffPerformanceData(actx *auth.Ctx, query *reporttypes.ReportQuery) ([]map[string]interface{}, error) {
	// TODO: Implement the logic to retrieve staff performance data from the database
	return nil, nil
}
