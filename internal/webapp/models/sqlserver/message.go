package sqlserver

import (
	"pebble/internal/webapp/types"
	"pebble/pkg/auth"
)

// messageRecordTable implements IMessageRecord interface
type messageRecordTable struct {
	BaseTable
}

// NewMessageRecordTable creates a new messageRecordTable instance
func NewMessageRecordTable() *messageRecordTable {
	table := &messageRecordTable{}
	table.SetTableName("message_records")
	return table
}

// Create creates a new message record
func (t *messageRecordTable) Create(actx *auth.Ctx, data types.MessageRecordEntity) (*types.MessageRecord, error) {
	messageRecord := types.MessageRecord{
		MessageRecordEntity: data,
	}
	messageRecord.TenantID = actx.TenantId
	messageRecord.LocationID = actx.LocationId

	err := t.NoLimitTx(actx.Context()).Create(&messageRecord).Error
	return &messageRecord, err
}

// GetByMessageID gets message record by message ID
func (t *messageRecordTable) GetByMessageID(actx *auth.Ctx, messageID string) (*types.MessageRecord, error) {
	var messageRecord types.MessageRecord
	err := t.LimitLocationTx(actx).
		Where("message_id = ?", messageID).
		First(&messageRecord).Error
	return &messageRecord, err
}

// GetByClientID gets message records by client ID
func (t *messageRecordTable) GetByClientID(actx *auth.Ctx, clientID string) ([]types.MessageRecord, error) {
	var messageRecords []types.MessageRecord
	err := t.LimitLocationTx(actx).
		Where("client_id = ?", clientID).
		Order("created_at DESC").
		Find(&messageRecords).Error
	return messageRecords, err
}

func (t *messageRecordTable) GetMessages(actx *auth.Ctx, params types.QueryMessageParams) ([]types.MessageRecord, error) {
	var messageRecords []types.MessageRecord
	db := t.LimitLocationTx(actx)
	if params.ClientID != nil {
		db = db.Where("client_id = ?", params.ClientID)
	}
	if params.Page != nil && params.PageSize != nil {
		db = db.Offset(*params.Page * *params.PageSize).Limit(*params.PageSize)
	}
	err := db.Order("created_at DESC").Find(&messageRecords).Error
	return messageRecords, err
}

func (t *messageRecordTable) GetMessagesGroupedByClient(actx *auth.Ctx, params types.QueryMessageParams) ([]types.MessageRecord, error) {
	// var messageRecords []types.MessageRecord
	// db := t.LimitLocationTx(actx)
	// if params.ClientID != nil {
	// 	db = db.Where("client_id = ?", params.ClientID)
	// }
	// if params.Page != nil && params.PageSize != nil {
	// 	db = db.Offset((*params.Page - 1) * *params.PageSize).Limit(*params.PageSize)
	// }

	// err := db.Order("created_at DESC").Group("client_id").Find(&messageRecords).Error
	// return messageRecords, err
	var messageRecords []types.MessageRecord
	db := t.LimitLocationTx(actx)
	if params.ClientID != nil {
		db = db.Where("client_id = ?", params.ClientID)
	}

	db = db.Where("NOT EXISTS (" +
		"SELECT 1 FROM message_records m2 " +
		"WHERE m2.client_id = message_records.client_id " +
		"AND m2.tenant_id = message_records.tenant_id " +
		"AND m2.location_id = message_records.location_id " +
		"AND m2.deleted_at IS NULL " +
		"AND m2.created_at > message_records.created_at" +
		")")

	if params.Page != nil && params.PageSize != nil {
		db = db.Offset((*params.Page - 1) * *params.PageSize).Limit(*params.PageSize)
	}

	err := db.Order("message_records.created_at DESC").Find(&messageRecords).Error
	return messageRecords, err
}
