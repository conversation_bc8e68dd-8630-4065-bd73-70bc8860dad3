package sqlserver

import (
	"pebble/internal/webapp/types"
	"pebble/pkg/auth"
)

type rolePermissionTable struct {
	BaseTable
}

func NewRolePermissionTable() *rolePermissionTable {
	table := &rolePermissionTable{}
	table.SetTableName("role_permissions")
	return table
}

func (r *rolePermissionTable) CreateRolePermission(actx *auth.Ctx, req types.CreateRolePermissionReq) (*types.RolePermission, error) {
	role := types.RolePermission{
		RolePermissionEntity: types.RolePermissionEntity{
			RoleId:       req.RoleId,
			PermissionId: req.PermissionId,
		},
	}
	role.TenantId = actx.TenantId
	role.LocationId = actx.LocationId

	err := r.NoLimitTx(actx.Context()).Create(&role).Error
	return &role, err

}

func (r *rolePermissionTable) BatchCreateRolePermissions(actx *auth.Ctx, req types.BatchCreateRolePermissionReq) ([]types.RolePermission, error) {
	if len(req.PermissionIds) == 0 {
		return []types.RolePermission{}, nil
	}

	rolePermissions := make([]types.RolePermission, 0, len(req.PermissionIds))

	for _, permissionId := range req.PermissionIds {
		rolePermissions = append(rolePermissions, types.RolePermission{
			RolePermissionEntity: types.RolePermissionEntity{
				RoleId:       req.RoleId,
				PermissionId: permissionId,
				LocationId:   actx.LocationId,
				TenantId:     actx.TenantId,
			},
		})
	}

	err := r.NoLimitTx(actx.Context()).Create(&rolePermissions).Error
	if err != nil {
		return nil, err
	}

	return rolePermissions, nil
}

func (r *rolePermissionTable) QueryRolePermissions(actx *auth.Ctx, roleId string) ([]types.RolePermission, error) {
	var rolePermission []types.RolePermission
	err := r.LimitLocationTx(actx).Where("role_id = ?", roleId).Find(&rolePermission).Error
	return rolePermission, err
}

func (r *rolePermissionTable) DeleteRolePermission(actx *auth.Ctx, roleId, permissionId string) error {
	return r.LimitLocationTx(actx).Where("role_id = ? and permission_id = ?", roleId, permissionId).Delete(&types.RolePermission{}).Error
}

func (r *rolePermissionTable) DeleteRolePermissions(actx *auth.Ctx, roleId string, permissionIds []string) error {
	return r.LimitLocationTx(actx).Where("role_id = ? and permission_id in (?)", roleId, permissionIds).Delete(&types.RolePermission{}).Error
}

func (r *rolePermissionTable) QueryRolePermissionsByRoleIds(actx *auth.Ctx, roleIds []string) ([]types.RolePermission, error) {
	var rolePermissions []types.RolePermission
	err := r.LimitLocationTx(actx).Where("role_id in (?)", roleIds).Find(&rolePermissions).Error
	return rolePermissions, err
}
