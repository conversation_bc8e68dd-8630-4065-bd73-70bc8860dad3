package sqlserver

import (
	"pebble/internal/webapp/types"
	"pebble/pkg/auth"
)

type serviceStaffTables struct {
	BaseTable
}

func NewServiceStaffTable() *serviceStaffTables {
	table := &serviceStaffTables{}
	table.SetTableName("service_staffs")
	return table
}

func (s *serviceStaffTables) BatchCreate(actx *auth.Ctx, data []types.ServiceStaffItem) ([]types.ServiceStaff, error) {
	if len(data) == 0 {
		return []types.ServiceStaff{}, nil
	}
	var serviceStaffs []types.ServiceStaff

	for _, req := range data {
		serviceStaff := types.ServiceStaff{
			ServiceStaffEntity: types.ServiceStaffEntity{
				TenantId:   actx.TenantId,
				LocationId: actx.TenantId,
				ServiceId:  req.ServiceId,
				AccountId:  req.AccountId,
			},
		}
		serviceStaffs = append(serviceStaffs, serviceStaff)
	}

	err := s.NoLimitTx(actx.Context()).Create(&serviceStaffs).Error
	if err != nil {
		return nil, err
	}

	return serviceStaffs, nil
}

func (s *serviceStaffTables) Create(actx *auth.Ctx, data types.ServiceStaffItem) (*types.ServiceStaff, error) {
	serviceCategory := types.ServiceStaff{
		ServiceStaffEntity: types.ServiceStaffEntity{
			TenantId:   actx.TenantId,
			LocationId: actx.TenantId,
			ServiceId:  data.ServiceId,
			AccountId:  data.AccountId,
		},
	}

	err := s.NoLimitTx(actx.Context()).Create(&serviceCategory).Error
	if err != nil {
		return nil, err
	}

	return &serviceCategory, nil
}

func (s *serviceStaffTables) ListByServiceId(actx *auth.Ctx, serviceId string) ([]types.ServiceStaff, error) {
	var serviceStaffs []types.ServiceStaff
	err := s.NoLimitTx(actx.Context()).
		Where("service_id = ?", serviceId).
		Find(&serviceStaffs).Error
	if err != nil {
		return nil, err
	}

	return serviceStaffs, nil
}
func (s *serviceStaffTables) List(actx *auth.Ctx) ([]types.ServiceStaff, error) {
	var serviceStaffs []types.ServiceStaff
	err := s.NoLimitTx(actx.Context()).
		Find(&serviceStaffs).Error
	if err != nil {
		return nil, err
	}

	return serviceStaffs, nil
}

// todo
func (s *serviceStaffTables) BatchDelete(actx *auth.Ctx, serviceId string, accountIds []string) error {
	if len(accountIds) == 0 {
		return nil
	}

	err := s.NoLimitTx(actx.Context()).
		Where("service_id = ?", serviceId).
		Where("account_id IN ?", accountIds).
		Delete(&types.ServiceStaff{}).Error
	if err != nil {
		return err
	}

	return nil
}
