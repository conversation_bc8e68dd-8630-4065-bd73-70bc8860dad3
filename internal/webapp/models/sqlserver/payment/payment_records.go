package paymentsql

import (
	commonsql "pebble/internal/webapp/models/sqlserver/common"
	commontypes "pebble/internal/webapp/types/common"
	paymenttypes "pebble/internal/webapp/types/payment"
	"pebble/pkg/auth"
	"pebble/pkg/uid"
	"pebble/pkg/utime"
	"time"

	"gorm.io/gorm"
)

type paymentRecordsTable struct {
	commonsql.BaseTable
}

// NewMessageRecordTable creates a new messageRecordTable instance
func NewPaymentRecordsTable() *paymentRecordsTable {
	table := &paymentRecordsTable{}
	table.SetTableName("payment_records")
	return table
}

func (t *paymentRecordsTable) Create(actx *auth.Ctx, data paymenttypes.CreatePaymentReq) (*paymenttypes.PaymentRecords, error) {
	paymentRecord := paymenttypes.PaymentRecords{
		PaymentRecordsEntity: paymenttypes.PaymentRecordsEntity{
			TenantId:       actx.TenantId,
			LocationId:     actx.LocationId,
			PaymentId:      uid.GeneratePaymentId(),
			OrderType:      data.OrderType,
			OrderId:        data.OrderId,
			MethodId:       data.MethodId,
			MethodName:     data.MethodName,
			MethodType:     data.MethodType,
			AmountTip:      data.AmountTip,
			AmountTax:      data.AmountTax,
			AmountFee:      data.AmountFee,
			AmountTotal:    data.AmountTotal,
			AmountSubtotal: data.AmountSubTotal,
			AmountRefund:   data.AmountRefund,
			FeePayer:       data.FeePayer,
			Status:         data.Status,
			ReferenceId:    data.ReferenceId,
			ClientId:       data.ClientId,
			TargetId:       data.TargetId,
			Currency:       data.Currency,
			CaptureAt:      data.CaptureAt,
		},
	}

	err := t.NoLimitTx(actx.Context()).Create(&paymentRecord).Error
	return &paymentRecord, err
}

func (t *paymentRecordsTable) List(actx *auth.Ctx, where paymenttypes.QueryPaymentsParams) ([]paymenttypes.PaymentRecords, int64, error) {
	var paymentRecords []paymenttypes.PaymentRecords
	db := t.LimitLocationTx(actx)
	if where.OrderType != nil {
		db = db.Where("order_type = ?", where.OrderType)
	}
	if where.OrderId != nil {
		db = db.Where("order_id = ?", where.OrderId)
	}
	if where.MethodId != nil {
		db = db.Where("method_id = ?", where.MethodId)
	}
	if where.StartDate != nil {
		start := utime.ParseDateToUTC(*where.StartDate, where.Timezone).Truncate(time.Second)
		db = db.Where("capture_at >= ?", start)
	}
	if where.EndDate != nil {
		end := utime.ParseDateToUTC(*where.EndDate, where.Timezone).Add(time.Duration(24)*time.Hour - time.Second).Truncate(time.Second)
		db = db.Where("capture_at <= ?", end)
	}
	if len(where.StatusList) > 0 {
		db = db.Where("status in (?)", where.StatusList)
	}
	// if where.Types != nil {
	// 	db = db.Where("type in (?)", where.Types)
	// }

	var total int64
	countDb := db
	err := countDb.Count(&total).Error
	if err != nil {
		return nil, 0, err
	}

	db = db.Order("created_at desc")
	if where.Page != nil && where.PageSize != nil {
		db = db.Offset((*where.Page - 1) * *where.PageSize).Limit(*where.PageSize)
	}

	err = db.Find(&paymentRecords).Error
	if err != nil {
		return nil, 0, err
	}
	return paymentRecords, total, nil
}

func (t *paymentRecordsTable) Get(actx *auth.Ctx, orderId string) ([]paymenttypes.PaymentRecords, error) {
	var paymentRecords []paymenttypes.PaymentRecords
	err := t.LimitLocationTx(actx).Where("order_id = ?", orderId).Find(&paymentRecords).Error
	return paymentRecords, err
}

func (t *paymentRecordsTable) GetByOrderIds(actx *auth.Ctx, orderIds []string) ([]paymenttypes.PaymentRecords, error) {
	if len(orderIds) == 0 {
		return []paymenttypes.PaymentRecords{}, nil
	}
	var paymentRecords []paymenttypes.PaymentRecords
	err := t.LimitLocationTx(actx).Where("order_id in (?)", orderIds).Find(&paymentRecords).Error
	return paymentRecords, err
}

func (t *paymentRecordsTable) GetByPaymentIds(actx *auth.Ctx, paymentIds []string) ([]paymenttypes.PaymentRecords, error) {
	if len(paymentIds) == 0 {
		return []paymenttypes.PaymentRecords{}, nil
	}
	var paymentRecords []paymenttypes.PaymentRecords
	err := t.LimitLocationTx(actx).Where("payment_id in (?)", paymentIds).Find(&paymentRecords).Error
	return paymentRecords, err
}

func (t *paymentRecordsTable) Update(actx *auth.Ctx, paymentId string, req paymenttypes.UpdatePaymentReq) error {
	paymentRecord := paymenttypes.PaymentRecordsEntity{
		PaymentId: paymentId,
	}
	return t.LimitLocationTx(actx).Where("payment_id = ?", paymentId).Updates(&paymentRecord).Error
}

func (t *paymentRecordsTable) Refund(actx *auth.Ctx, paymentId string, AmountRefund int64, status commontypes.PaymentRecordStatus) error {
	updateData := map[string]interface{}{
		"status":        status,
		"amount_refund": gorm.Expr("amount_refund + ?", AmountRefund),
	}
	return t.LimitLocationTx(actx).Where("payment_id = ?", paymentId).Updates(updateData).Error
}
