package paymentsql

import (
	commonsql "pebble/internal/webapp/models/sqlserver/common"
	paymenttypes "pebble/internal/webapp/types/payment"
	"pebble/pkg/auth"
	"pebble/pkg/uid"
)

type refundItemRecordsTable struct {
	commonsql.BaseTable
}

// NewMessageRecordTable creates a new messageRecordTable instance
func NewRefundItemRecordsTable() *refundItemRecordsTable {
	table := &refundItemRecordsTable{}
	table.SetTableName("refund_item_records")
	return table
}

func (t *refundItemRecordsTable) BatchCreate(actx *auth.Ctx, datas []paymenttypes.CreateRefundItemRecordReq) ([]paymenttypes.RefundItemRecords, error) {
	if len(datas) == 0 {
		return []paymenttypes.RefundItemRecords{}, nil
	}

	var records []paymenttypes.RefundItemRecords
	for _, record := range datas {
		records = append(records, paymenttypes.RefundItemRecords{
			RefundItemRecordsEntity: paymenttypes.RefundItemRecordsEntity{
				TenantId:      actx.TenantId,
				LocationId:    actx.LocationId,
				AccountId:     record.AccountId,
				ClientId:      record.ClientId,
				RefundId:      record.RefundId,
				PaymentId:     record.PaymentId,
				PaymentItemId: record.PaymentItemId,
				OrderType:     record.OrderType,
				OrderId:       record.OrderId,
				OrderItemType: record.OrderItemType,
				OrderItemId:   record.OrderItemId,
				TargetType:    record.TargetType,
				TargetId:      record.TargetId,
				RefundItemId:  uid.GenerateRefundItemId(),
				Currency:      record.Currency,
				AmountRefund:  record.AmountRefund,
				Status:        record.Status,
				ReferenceId:   record.ReferenceId,
				CaptureAt:     record.CaptureAt,
			},
		})
	}

	err := t.NoLimitTx(actx.Context()).Create(&records).Error
	if err != nil {
		return nil, err
	}
	return records, nil
}
