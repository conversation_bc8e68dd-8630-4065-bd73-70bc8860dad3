package paymentsql

import (
	commonsql "pebble/internal/webapp/models/sqlserver/common"
	paymenttypes "pebble/internal/webapp/types/payment"
	"pebble/pkg/auth"
	"pebble/pkg/uid"
)

type paymentMethodsTable struct {
	commonsql.BaseTable
}

// NewMessageRecordTable creates a new messageRecordTable instance
func NewPaymentMethodsTable() *paymentMethodsTable {
	table := &paymentMethodsTable{}
	table.SetTableName("payment_methods")
	return table
}

func (t *paymentMethodsTable) Create(actx *auth.Ctx, data paymenttypes.CreatePaymentMethodReq) (*paymenttypes.PaymentMethods, error) {
	paymentMethod := paymenttypes.PaymentMethods{
		PaymentMethodsEntity: paymenttypes.PaymentMethodsEntity{
			TenantId:   actx.TenantId,
			LocationId: actx.LocationId,
			MethodId:   uid.GeneratePaymentMethodId(),
			Type:       data.Type,
			Name:       data.Name,
			Status:     data.Status,
		},
	}

	err := t.NoLimitTx(actx.Context()).Create(&paymentMethod).Error
	return &paymentMethod, err
}

func (t *paymentMethodsTable) Get(actx *auth.Ctx, methodId string) (*paymenttypes.PaymentMethods, error) {
	var paymentMethod paymenttypes.PaymentMethods
	err := t.LimitLocationTx(actx).Where("method_id = ?", methodId).First(&paymentMethod).Error
	if err != nil {
		return nil, err
	}

	return &paymentMethod, nil
}

func (t *paymentMethodsTable) GetByName(actx *auth.Ctx, methodName string) (*paymenttypes.PaymentMethods, error) {
	var paymentMethod paymenttypes.PaymentMethods
	err := t.LimitLocationTx(actx).Where("name = ?", methodName).First(&paymentMethod).Error
	if err != nil {
		return nil, err
	}

	return &paymentMethod, nil
}

func (t *paymentMethodsTable) List(actx *auth.Ctx, params paymenttypes.QueryPaymentMethodParams) ([]paymenttypes.PaymentMethods, error) {
	var paymentMethods []paymenttypes.PaymentMethods

	db := t.LimitLocationTx(actx)

	if params.Name != nil && *params.Name != "" {
		db = db.Where("name LIKE ?", "%"+*params.Name+"%")
	}

	if params.Type != nil {
		db = db.Where("type = ?", *params.Type)
	}

	if params.Status != nil {
		db = db.Where("status = ?", *params.Status)
	}

	err := db.Find(&paymentMethods).Error
	if err != nil {
		return nil, err
	}

	return paymentMethods, nil
}

func (t *paymentMethodsTable) Update(actx *auth.Ctx, methodId string, data paymenttypes.UpdatePaymentMethodReq) error {
	return t.LimitLocationTx(actx).Where("method_id = ?", methodId).Updates(&data).Error
}

func (t *paymentMethodsTable) Delete(actx *auth.Ctx, methodId string) error {
	return t.LimitLocationTx(actx).Where("method_id = ?", methodId).Delete(&paymenttypes.PaymentMethods{}).Error
}
