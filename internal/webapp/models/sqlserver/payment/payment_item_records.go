package paymentsql

import (
	commonsql "pebble/internal/webapp/models/sqlserver/common"
	commontypes "pebble/internal/webapp/types/common"
	paymenttypes "pebble/internal/webapp/types/payment"
	"pebble/pkg/auth"
	"pebble/pkg/uid"
	"pebble/pkg/utime"
	"time"

	"gorm.io/gorm"
)

type paymentItemRecordsTable struct {
	commonsql.BaseTable
}

// NewMessageRecordTable creates a new messageRecordTable instance
func NewPaymentItemRecordsTable() *paymentItemRecordsTable {
	table := &paymentItemRecordsTable{}
	table.SetTableName("payment_item_records")
	return table
}

func (t *paymentItemRecordsTable) BatchCreate(actx *auth.Ctx, req []paymenttypes.CreatePaymentItemReq) ([]paymenttypes.PaymentItemRecords, error) {
	if len(req) == 0 {
		return []paymenttypes.PaymentItemRecords{}, nil
	}

	var records []paymenttypes.PaymentItemRecords
	for _, record := range req {
		records = append(records, paymenttypes.PaymentItemRecords{
			PaymentItemRecordsEntity: paymenttypes.PaymentItemRecordsEntity{
				ClientId:       record.ClientId,
				TenantId:       actx.TenantId,
				LocationId:     actx.LocationId,
				PaymentId:      record.PaymentId,
				PaymentItemId:  uid.GeneratePaymentItemId(),
				OrderType:      record.OrderType,
				OrderId:        record.OrderId,
				OrderItemType:  record.OrderItemType,
				OrderItemId:    record.OrderItemId,
				AmountTax:      record.AmountTax,
				AmountSubtotal: record.AmountSubTotal,
				AmountTotal:    record.AmountTotal,
				Status:         record.Status,
				TaxInclusive:   record.TaxInclusive,
				ReferenceId:    record.ReferenceId,
				AccountId:      record.AccountId,
				TargetType:     record.TargetType,
				TargetId:       record.TargetId,
				Currency:       record.Currency,
				AmountRefund:   0,
				CaptureAt:      record.CaptureAt,
			},
		})
	}

	err := t.NoLimitTx(actx.Context()).Create(&records).Error
	if err != nil {
		return nil, err
	}
	return records, nil
}

// func (t *paymentItemRecordsTable) Create(actx *auth.Ctx, data paymenttypes.PaymentItemRecordsEntity) (*paymenttypes.PaymentItemRecords, error) {
// 	paymentItemRecord := paymenttypes.PaymentItemRecords{
// 		PaymentItemRecordsEntity: data,
// 	}
// 	paymentItemRecord.LocationId = actx.LocationId
// 	paymentItemRecord.TenantId = actx.TenantId

// 	err := t.NoLimitTx(actx.Context()).Create(&paymentItemRecord).Error
// 	return &paymentItemRecord, err
// }

func (t *paymentItemRecordsTable) List(actx *auth.Ctx, where paymenttypes.QueryPaymentItemParams) ([]paymenttypes.PaymentItemRecords, int64, error) {
	var paymentRecords []paymenttypes.PaymentItemRecords
	db := t.LimitLocationTx(actx)

	if where.AccountId != nil {
		db = db.Where("account_id = ?", where.AccountId)
	}
	if where.StartDate != nil {
		start := utime.ParseDateToUTC(*where.StartDate, where.Timezone).Truncate(time.Second)
		db = db.Where("capture_at >= ?", start)
	}
	if where.EndDate != nil {
		end := utime.ParseDateToUTC(*where.EndDate, where.Timezone).Add(time.Duration(24)*time.Hour - time.Second).Truncate(time.Second)
		db = db.Where("capture_at <= ?", end)
	}
	if len(where.StatusList) > 0 {
		db = db.Where("status in (?)", where.StatusList)
	}

	var total int64
	countDb := db
	err := countDb.Count(&total).Error
	if err != nil {
		return nil, 0, err
	}

	if where.Page != nil && where.PageSize != nil {
		db = db.Offset((*where.Page - 1) * *where.PageSize).Limit(*where.PageSize)
	}

	err = db.Find(&paymentRecords).Error
	if err != nil {
		return nil, 0, err
	}
	return paymentRecords, total, nil
}

func (t *paymentItemRecordsTable) Get(actx *auth.Ctx, orderId string) ([]paymenttypes.PaymentItemRecords, error) {
	var paymentItemRecords []paymenttypes.PaymentItemRecords
	err := t.LimitLocationTx(actx).Where("order_id = ?", orderId).Find(&paymentItemRecords).Error
	return paymentItemRecords, err
}

func (t *paymentItemRecordsTable) GetByOrderIds(actx *auth.Ctx, orderIds []string) ([]paymenttypes.PaymentItemRecords, error) {
	var paymentItemRecords []paymenttypes.PaymentItemRecords
	err := t.LimitLocationTx(actx).Where("order_id in (?)", orderIds).Find(&paymentItemRecords).Error
	return paymentItemRecords, err
}

func (t *paymentItemRecordsTable) GetByPaymentIds(actx *auth.Ctx, paymentIds []string) ([]paymenttypes.PaymentItemRecords, error) {
	var paymentItemRecords []paymenttypes.PaymentItemRecords
	err := t.LimitLocationTx(actx).Where("payment_id in (?)", paymentIds).Find(&paymentItemRecords).Error
	return paymentItemRecords, err
}

func (t *paymentItemRecordsTable) GetByPaymentItemIds(actx *auth.Ctx, paymentIds, paymentItemIds []string) ([]paymenttypes.PaymentItemRecords, error) {
	var paymentItemRecords []paymenttypes.PaymentItemRecords
	err := t.LimitLocationTx(actx).Where("payment_id in (?) and payment_item_id in (?)", paymentIds, paymentItemIds).Find(&paymentItemRecords).Error
	return paymentItemRecords, err
}

func (t *paymentItemRecordsTable) Update(actx *auth.Ctx, paymentId string, req paymenttypes.UpdatePaymentItemReq) error {
	paymentItemRecord := paymenttypes.PaymentItemRecordsEntity{
		PaymentId: paymentId,
	}
	return t.LimitLocationTx(actx).Where("payment_id = ?", paymentId).Updates(&paymentItemRecord).Error
}

func (t *paymentItemRecordsTable) RefundByPaymentItemIds(actx *auth.Ctx, paymentIds, paymentItemIds []string) error {
	updateData := map[string]interface{}{
		"status":        commontypes.PaymentStatusRefund,
		"amount_refund": gorm.Expr("amount_total"),
	}
	err := t.LimitLocationTx(actx).Where("payment_id IN ? AND payment_item_id IN ?", paymentIds, paymentItemIds).Updates(updateData).Error
	if err != nil {
		return err
	}

	return nil
}
