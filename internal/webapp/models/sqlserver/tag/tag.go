package tagsql

import (
	commonsql "pebble/internal/webapp/models/sqlserver/common"
	tagtypes "pebble/internal/webapp/types/tag"
	"pebble/pkg/auth"
	"pebble/pkg/uid"
)

type tagTable struct {
	commonsql.BaseTable
}

func NewTagTable() *tagTable {
	table := &tagTable{}
	table.SetTableName("tags")
	return table
}

func (t *tagTable) CreateTag(actx *auth.Ctx, req tagtypes.CreateTagReq) (*tagtypes.Tag, error) {
	tag := tagtypes.Tag{
		TagEntity: tagtypes.TagEntity{
			Color:       req.Color,
			Description: req.Description,
			Type:        req.Type,
			Tag:         req.Tag,
			TagId:       uid.GenerateTagId(),
		},
	}
	tag.LocationId = actx.LocationId
	tag.TenantId = actx.TenantId

	err := t.NoLimitTx(actx.Context()).Create(&tag).Error
	if err != nil {
		return nil, err
	}
	return &tag, nil
}

func (t *tagTable) UpdateTag(actx *auth.Ctx, tagId string, req tagtypes.UpdateTagReq) error {
	return t.LimitLocationTx(actx).Where("tag_id = ?", tagId).Updates(req).Error
}

func (t *tagTable) DeleteTag(actx *auth.Ctx, tagId string) error {
	return t.LimitLocationTx(actx).Where("tag_id = ?", tagId).Delete(&tagtypes.Tag{}).Error
}

func (t *tagTable) GetTag(actx *auth.Ctx, tagId string) (*tagtypes.Tag, error) {
	var tag tagtypes.Tag
	err := t.LimitLocationTx(actx).Where("tag_id = ?", tagId).First(&tag).Error
	if err != nil {
		return nil, err
	}
	return &tag, nil
}

func (t *tagTable) ListTags(actx *auth.Ctx, params tagtypes.QueryTagsParams) ([]tagtypes.Tag, error) {
	var tags []tagtypes.Tag
	query := t.LimitLocationTx(actx)
	if params.Type != nil {
		query = query.Where("type = ?", *params.Type)
	}
	err := query.Find(&tags).Error
	if err != nil {
		return nil, err
	}

	return tags, nil
}
