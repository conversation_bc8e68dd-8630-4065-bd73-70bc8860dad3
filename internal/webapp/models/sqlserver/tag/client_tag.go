package tagsql

import (
	commonsql "pebble/internal/webapp/models/sqlserver/common"
	commontypes "pebble/internal/webapp/types/common"
	tagtypes "pebble/internal/webapp/types/tag"
	"pebble/pkg/auth"
)

type clientTagTable struct {
	commonsql.BaseTable
}

func NewClientTagTable() *clientTagTable {
	table := &clientTagTable{}
	table.SetTableName("client_tags")
	return table
}

func (c *clientTagTable) BatchCreateClientTag(actx *auth.Ctx, req tagtypes.CreateClientTagReq) ([]tagtypes.ClientTag, error) {
	if len(req.TagIds) == 0 {
		return []tagtypes.ClientTag{}, nil
	}

	var clientTags []tagtypes.ClientTag
	for _, tagId := range req.TagIds {
		clientTag := tagtypes.ClientTag{
			ClientTagEntity: tagtypes.ClientTagEntity{
				ClientId:   req.ClientId,
				LocationId: actx.LocationId,
				TagId:      tagId,
				TenantId:   actx.TenantId,
			},
		}
		clientTags = append(clientTags, clientTag)
	}
	err := c.NoLimitTx(actx.Context()).Create(&clientTags).Error
	if err != nil {
		return nil, err
	}
	return clientTags, nil
}

func (c *clientTagTable) UpdateClientTagStatus(actx *auth.Ctx, clientId string, tagIds []string, status commontypes.Status) error {
	return c.LimitLocationTx(actx).Where("client_id = ?", clientId).Where("tag_id IN ?", tagIds).Update("status", status).Error
}

func (c *clientTagTable) GetClientTags(actx *auth.Ctx, condition tagtypes.GetClientTagCondition) ([]tagtypes.ClientTag, error) {
	var clientTags []tagtypes.ClientTag
	db := c.LimitLocationTx(actx).Where("client_id in (?)", condition.ClientIds)
	if condition.Status != nil {
		db = db.Where("status = ?", *condition.Status)
	}
	if len(condition.TagIds) > 0 {
		db = db.Where("tag_id in (?)", condition.TagIds)
	}

	err := db.Find(&clientTags).Error
	if err != nil {
		return nil, err
	}
	return clientTags, nil
}
