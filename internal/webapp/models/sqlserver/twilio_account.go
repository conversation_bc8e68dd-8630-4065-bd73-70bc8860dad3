package sqlserver

import (
	"pebble/internal/webapp/types"
	"pebble/pkg/auth"
)

type twilioAccountTable struct {
	BaseTable
}

func NewTwilioAccountTable() *twilioAccountTable {
	table := &twilioAccountTable{}
	table.SetTableName("twilio_accounts")
	return table
}

func (t *twilioAccountTable) Create(actx *auth.Ctx, data types.TwilioAccountEntity) (*types.TwilioAccount, error) {
	twilioAccount := types.TwilioAccount{TwilioAccountEntity: data}
	twilioAccount.TenantID = actx.TenantId
	twilioAccount.LocationID = actx.LocationId

	err := t.NoLimitTx(actx.Context()).Create(&twilioAccount).Error
	return &twilioAccount, err
}

func (t *twilioAccountTable) GetByTenantAndLocation(actx *auth.Ctx) (*types.TwilioAccount, error) {
	var twilioAccount types.TwilioAccount
	err := t.LimitLocationTx(actx).First(&twilioAccount).Error
	return &twilioAccount, err
}

func (t *twilioAccountTable) Update(actx *auth.Ctx, req types.UpdateTwilioAccountReq) error {
	return t.LimitTenantTx(actx).Updates(req).Error
}

func (t *twilioAccountTable) Delete(actx *auth.Ctx) error {
	return t.LimitTenantTx(actx).Delete(&types.TwilioAccount{}).Error
}
