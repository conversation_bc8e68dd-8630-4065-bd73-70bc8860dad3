package stripe

import (
	commonsql "pebble/internal/webapp/models/sqlserver/common"
	"pebble/internal/webapp/types/stripe"
	"pebble/pkg/auth"
	"pebble/pkg/util"
)

type stripeSettingTable struct {
	commonsql.BaseTable
}

func NewStripeSettingsTable() *stripeSettingTable {
	table := &stripeSettingTable{}
	table.SetTableName("stripe_settings")
	return table
}

func (t *stripeSettingTable) Create(actx *auth.Ctx, data stripe.StripeSettingEntity) (*stripe.StripeSetting, error) {
	stripeSetting := stripe.StripeSetting{
		StripeSettingEntity: data,
	}
	stripeSetting.TenantId = actx.TenantId
	stripeSetting.LocationId = actx.LocationId

	err := t.NoLimitTx(actx.Context()).Create(&stripeSetting).Error
	if err != nil {
		return nil, err
	}
	return &stripeSetting, nil
}

func (t *stripeSettingTable) Get(actx *auth.Ctx) (*stripe.StripeSetting, error) {
	db := t.LimitLocationTx(actx)
	var stripeSetting stripe.StripeSetting

	err := db.First(&stripeSetting).Error
	if err != nil {
		return &stripeSetting, err
	}
	return &stripeSetting, nil
}

func (t *stripeSettingTable) Update(actx *auth.Ctx, data stripe.UpdateStripeSettingReq) error {
	db := t.LimitLocationTx(actx)
	if len(data.TipPercentageList) > 0 {
		data.TipPercentage = util.Ptr(util.Join(data.TipPercentageList, ","))
	}

	err := db.Updates(&data).Error
	if err != nil {
		return err
	}

	return nil
}
