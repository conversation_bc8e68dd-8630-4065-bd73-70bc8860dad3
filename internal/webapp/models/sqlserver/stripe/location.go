package stripe

import (
	commonsql "pebble/internal/webapp/models/sqlserver/common"
	"pebble/internal/webapp/types/stripe"
	"pebble/pkg/auth"
	"pebble/pkg/util"

	"gorm.io/gorm"
)

type stripeLocationsTable struct {
	commonsql.BaseTable
}

// NewMessageRecordTable creates a new messageRecordTable instance
func NewStripeLocationsTable() *stripeLocationsTable {
	table := &stripeLocationsTable{}
	table.SetTableName("stripe_locations")
	return table
}

func (t *stripeLocationsTable) Create(actx *auth.Ctx, data stripe.StripeLocationsEntity) (*stripe.StripeLocations, error) {
	data.TenantId = actx.TenantId
	data.LocationId = actx.LocationId

	var locationData map[string]interface{}
	_ = util.DeepCopy(data, &locationData)
	delete(locationData, "lat")
	delete(locationData, "lng")
	locationData["coordinates"] = gorm.Expr("ST_SRID(POINT(?, ?), 4326)", data.Lng, data.Lat)

	err := t.NoLimitTx(actx.Context()).Create(&locationData).Error
	if err != nil {
		return nil, err
	}

	var resp stripe.StripeLocations
	_ = util.DeepCopy(locationData, &resp)

	return &resp, nil
}

func (t *stripeLocationsTable) List(actx *auth.Ctx) ([]stripe.StripeLocations, error) {
	db := t.LimitLocationTx(actx)
	var stripeLocations []stripe.StripeLocations

	err := db.Select(
		"*",                        // 首先，选择 stripe_locations 表中的所有原生列。
		"ST_Y(coordinates) as lat", // 使用 ST_Y() 从 POINT 中提取纬度，并将其别名设置为 "lat"。
		"ST_X(coordinates) as lng", // 使用 ST_X() 从 POINT 中提取经度，并将其别名设置为 "lng"。
	).Find(&stripeLocations).Error
	if err != nil {
		return nil, err
	}
	return stripeLocations, nil
}

func (t *stripeLocationsTable) GetByStripeLocationId(actx *auth.Ctx, stripeLocationId string) (*stripe.StripeLocations, error) {
	db := t.LimitLocationTx(actx)
	var stripeLocation stripe.StripeLocations
	err := db.Select(
		"*",                        // 首先，选择 stripe_locations 表中的所有原生列。
		"ST_Y(coordinates) as lat", // 使用 ST_Y() 从 POINT 中提取纬度，并将其别名设置为 "lat"。
		"ST_X(coordinates) as lng", // 使用 ST_X() 从 POINT 中提取经度，并将其别名设置为 "lng"。
	).Where("stripe_location_id = ?", stripeLocationId).First(&stripeLocation).Error
	if err != nil {
		return nil, err
	}
	return &stripeLocation, nil
}

func (t *stripeLocationsTable) GetByStripeLocationIds(actx *auth.Ctx, stripeLocationIds []string) ([]stripe.StripeLocations, error) {
	if len(stripeLocationIds) == 0 {
		return []stripe.StripeLocations{}, nil
	}

	db := t.LimitLocationTx(actx)
	var stripeLocations []stripe.StripeLocations

	err := db.Select(
		"*",                        // 首先，选择 stripe_locations 表中的所有原生列。
		"ST_Y(coordinates) as lat", // 使用 ST_Y() 从 POINT 中提取纬度，并将其别名设置为 "lat"。
		"ST_X(coordinates) as lng", // 使用 ST_X() 从 POINT 中提取经度，并将其别名设置为 "lng"。
	).Where("stripe_location_id IN (?)", stripeLocationIds).Find(&stripeLocations).Error
	if err != nil {
		return nil, err
	}
	return stripeLocations, nil
}
