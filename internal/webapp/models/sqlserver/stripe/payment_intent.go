package stripe

import (
	commonsql "pebble/internal/webapp/models/sqlserver/common"
	"pebble/internal/webapp/types/stripe"
	"pebble/pkg/auth"
)

type stripePaymentIntentTable struct {
	commonsql.BaseTable
}

func NewStripePaymentIntentTable() *stripePaymentIntentTable {
	table := &stripePaymentIntentTable{}
	table.SetTableName("stripe_payment_intents")
	return table
}

func (t *stripePaymentIntentTable) Create(actx *auth.Ctx, data stripe.StripePaymentIntentEntity) (*stripe.StripePaymentIntent, error) {
	stripePaymentIntent := stripe.StripePaymentIntent{
		StripePaymentIntentEntity: data,
	}
	stripePaymentIntent.TenantId = actx.TenantId
	stripePaymentIntent.LocationId = actx.LocationId

	err := t.NoLimitTx(actx.Context()).Create(&stripePaymentIntent).Error
	if err != nil {
		return nil, err
	}

	return &stripePaymentIntent, nil
}
