package stripe

import (
	"context"
	commonsql "pebble/internal/webapp/models/sqlserver/common"
	"pebble/internal/webapp/types/stripe"
	"pebble/pkg/auth"
)

type stripePaymentIntentTable struct {
	commonsql.BaseTable
}

func NewStripePaymentIntentTable() *stripePaymentIntentTable {
	table := &stripePaymentIntentTable{}
	table.SetTableName("stripe_payment_intents")
	return table
}

func (t *stripePaymentIntentTable) Create(actx *auth.Ctx, data stripe.StripePaymentIntentEntity) (*stripe.StripePaymentIntent, error) {
	stripePaymentIntent := stripe.StripePaymentIntent{
		StripePaymentIntentEntity: data,
	}
	stripePaymentIntent.TenantId = actx.TenantId
	stripePaymentIntent.LocationId = actx.LocationId

	err := t.NoLimitTx(actx.Context()).Create(&stripePaymentIntent).Error
	if err != nil {
		return nil, err
	}

	return &stripePaymentIntent, nil
}

func (t *stripePaymentIntentTable) AdminGetByIntentId(ctx context.Context, intentId string) (*stripe.StripePaymentIntent, error) {
	stripePaymentIntent := stripe.StripePaymentIntent{}
	err := t.NoLimitTx(context.Background()).Where("stripe_payment_intent_id = ?", intentId).First(&stripePaymentIntent).Error
	return &stripePaymentIntent, err
}

func (t *stripePaymentIntentTable) AdminUpdateIntentStatus(ctx context.Context, intentId string, status stripe.PaymentIntentStatus) error {
	stripePaymentIntent := stripe.StripePaymentIntent{}
	err := t.NoLimitTx(context.Background()).Where("stripe_payment_intent_id = ?", intentId).First(&stripePaymentIntent).Error
	if err != nil {
		return err
	}

	stripePaymentIntent.Status = status
	err = t.NoLimitTx(context.Background()).Save(&stripePaymentIntent).Error
	if err != nil {
		return err
	}

	return nil
}

func (t *stripePaymentIntentTable) AdminUpdateIntent(ctx context.Context, intentId string, req stripe.UpdateStripePaymentIntentReq) error {
	stripePaymentIntent := stripe.StripePaymentIntent{}
	err := t.NoLimitTx(context.Background()).Where("stripe_payment_intent_id = ?", intentId).First(&stripePaymentIntent).Error
	if err != nil {
		return err
	}

	if req.Amount != nil {
		stripePaymentIntent.Amount = *req.Amount
	}
	if req.AmountTip != nil {
		stripePaymentIntent.AmountTip = *req.AmountTip
	}
	if req.AmountFee != nil {
		stripePaymentIntent.AmountFee = *req.AmountFee
	}
	if req.Status != nil {
		stripePaymentIntent.Status = *req.Status
	}
	err = t.NoLimitTx(context.Background()).Save(&stripePaymentIntent).Error
	if err != nil {
		return err
	}

	return nil
}
