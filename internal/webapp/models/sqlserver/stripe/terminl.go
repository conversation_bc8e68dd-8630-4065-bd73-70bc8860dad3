package stripe

import (
	commonsql "pebble/internal/webapp/models/sqlserver/common"
	"pebble/internal/webapp/types/stripe"
	"pebble/pkg/auth"
)

type stripeTerminalsTable struct {
	commonsql.BaseTable
}

// NewMessageRecordTable creates a new messageRecordTable instance
func NewStripeTerminalsTable() *stripeTerminalsTable {
	table := &stripeTerminalsTable{}
	table.SetTableName("stripe_terminals")
	return table
}

func (t *stripeTerminalsTable) Create(actx *auth.Ctx, data stripe.StripeTerminalEntity) (*stripe.StripeTerminal, error) {
	stripeTerminal := stripe.StripeTerminal{
		StripeTerminalEntity: data,
	}
	stripeTerminal.TenantId = actx.TenantId
	stripeTerminal.LocationId = actx.LocationId

	db := t.NoLimitTx(actx.Context())
	err := db.Create(&stripeTerminal).Error
	if err != nil {
		return nil, err
	}

	return &stripeTerminal, nil
}

func (t *stripeTerminalsTable) List(actx *auth.Ctx) ([]stripe.StripeTerminal, error) {
	db := t.LimitLocationTx(actx)
	var stripeTerminals []stripe.StripeTerminal
	err := db.Find(&stripeTerminals).Error
	if err != nil {
		return nil, err
	}
	return stripeTerminals, nil
}
