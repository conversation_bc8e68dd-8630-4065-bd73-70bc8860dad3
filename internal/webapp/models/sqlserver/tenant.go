package sqlserver

import (
	"context"
	"pebble/internal/webapp/types"
	"pebble/pkg/auth"
	"pebble/pkg/uid"
)

type tenantTable struct {
	BaseTable
}

func NewTenantTable() *tenantTable {
	table := &tenantTable{}
	table.SetTableName("tenants")
	return table
}

func (b *tenantTable) GetTenant(actx *auth.Ctx) (types.Tenant, error) {
	var tenant types.Tenant
	err := b.LimitTenantTx(actx).First(&tenant).Error
	if err != nil {
		return types.Tenant{}, err
	}
	return tenant, nil
}

func (b *tenantTable) UpdateTenant(actx *auth.Ctx, req types.UpdateTenantReq) error {
	err := b.LimitTenantTx(actx).Updates(req).Error
	if err != nil {
		return err
	}
	return nil
}

func (b *tenantTable) AdminGetTenantByEmail(ctx context.Context, email string) (types.Tenant, error) {
	var tenant types.Tenant
	err := b.NoLimitTx(ctx).Where("email = ?", email).First(&tenant).Error
	if err != nil {
		return types.Tenant{}, err
	}
	return tenant, nil
}

func (b *tenantTable) AdminCreateTenant(ctx context.Context, data types.TenantEntity) (types.Tenant, error) {
	tenant := types.Tenant{
		TenantEntity: data,
	}
	tenant.TenantId = uid.GenerateTenantId()
	err := b.NoLimitTx(ctx).Create(&tenant).Error
	if err != nil {
		return types.Tenant{}, err
	}
	return tenant, nil
}
