package sqlserver

import (
	"pebble/internal/webapp/types"
	"pebble/pkg/auth"
)

type tenantMemberRolesTables struct {
	BaseTable
}

func NewTenantMemberRolesTable() *tenantMemberRolesTables {
	table := &tenantMemberRolesTables{}
	table.SetTableName("tenant_members_roles")
	return table
}

func (t *tenantMemberRolesTables) BatchCreateTenantMemberRoles(actx *auth.Ctx, req types.CreateTenantMemberRolesReq) ([]types.TenantMemberRoles, error) {
	if len(req.RoleIds) == 0 {
		return []types.TenantMemberRoles{}, nil
	}

	tenantMemberRoles := make([]types.TenantMemberRoles, 0, len(req.RoleIds))

	for _, roleId := range req.RoleIds {
		tenantMemberRoles = append(tenantMemberRoles, types.TenantMemberRoles{
			TenantMemberRolesEntity: types.TenantMemberRolesEntity{
				TenantId:   actx.TenantId,
				LocationId: actx.LocationId,
				AccountId:  req.AccountId,
				RoleId:     roleId,
			},
		})
	}

	err := t.NoLimitTx(actx.Context()).Create(&tenantMemberRoles).Error
	if err != nil {
		return nil, err
	}
	return tenantMemberRoles, err
}

func (t *tenantMemberRolesTables) BatchDeleteTenantMemberRoles(actx *auth.Ctx, req types.DeleteTenantMemberRolesReq) error {
	if len(req.RoleIds) == 0 {
		return nil
	}
	err := t.LimitLocationTx(actx).Where("account_id = ? and role_id in (?)", req.AccountId, req.RoleIds).Delete(&types.TenantMemberRolesEntity{}).Error
	return err
}

func (t *tenantMemberRolesTables) GetTenantMemberRoles(actx *auth.Ctx, accountId string) ([]types.TenantMemberRoles, error) {
	var tenantMemberRoles []types.TenantMemberRoles
	err := t.LimitLocationTx(actx).Where("account_id = ?", accountId).Find(&tenantMemberRoles).Error
	return tenantMemberRoles, err

}

func (t *tenantMemberRolesTables) GetTenantMemberRoleByAccountIds(actx *auth.Ctx, accountIds []string) ([]types.TenantMemberRoles, error) {
	var tenantMemberRoles []types.TenantMemberRoles
	err := t.LimitLocationTx(actx).Where("account_id IN (?)", accountIds).Find(&tenantMemberRoles).Error
	return tenantMemberRoles, err
}
