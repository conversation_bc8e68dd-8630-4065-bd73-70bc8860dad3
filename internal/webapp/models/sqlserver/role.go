package sqlserver

import (
	"pebble/internal/webapp/types"
	"pebble/pkg/auth"
	"pebble/pkg/uid"
)

type roleTable struct {
	BaseTable
}

func NewRoleTable() *roleTable {
	table := &roleTable{}
	table.SetTableName("roles")
	return table
}

func (r *roleTable) CreateRole(actx *auth.Ctx, req types.CreateRoleReq) (*types.Role, error) {
	role := types.Role{
		RoleEntity: types.RoleEntity{
			Name:   req.Name,
			RoleId: uid.GenerateRoleId(),
		},
	}
	role.TenantId = actx.TenantId
	role.LocationId = actx.LocationId

	err := r.NoLimitTx(actx.Context()).Create(&role).Error
	return &role, err
}

func (r *roleTable) UpdateRole(actx *auth.Ctx, roleId string, req types.UpdateRoleReq) error {
	return r.LimitLocationTx(actx).Where("role_id = ?", roleId).Updates(&req).Error
}

func (r *roleTable) DeleteRole(actx *auth.Ctx, roleId string) error {
	return r.LimitLocationTx(actx).Where("role_id = ?", roleId).Delete(&types.Role{}).Error
}

func (r *roleTable) QueryRoles(actx *auth.Ctx, params types.QueryRolesParams) ([]types.Role, error) {
	var roles []types.Role
	db := r.LimitLocationTx(actx)
	if params.Name != nil {
		db = db.Where("name LIKE ?", "%"+*params.Name+"%")
	}

	err := db.Find(&roles).Error
	return roles, err
}

func (r *roleTable) QueryRole(actx *auth.Ctx, roleId string) (*types.Role, error) {
	var role types.Role
	err := r.LimitLocationTx(actx).Where("role_id = ?", roleId).First(&role).Error
	return &role, err
}

func (r *roleTable) GetRoleByIds(actx *auth.Ctx, roleIds []string) ([]types.Role, error) {
	var roles []types.Role
	err := r.LimitLocationTx(actx).Where("role_id in (?)", roleIds).Find(&roles).Error
	return roles, err
}
