package sqlserver

import (
	"encoding/json"
	"pebble/internal/webapp/types"
	"pebble/pkg/auth"
)

type staffScheduleTables struct {
	BaseTable
}

func NewStaffScheduleTables() *staffScheduleTables {
	table := &staffScheduleTables{}
	table.SetTableName("staff_schedules")
	return table
}

func (s *staffScheduleTables) BatchCreate(actx *auth.Ctx, data []types.CreateStaffScheduleReq) ([]types.StaffSchedule, error) {
	if len(data) == 0 {
		return []types.StaffSchedule{}, nil
	}
	var staffSchedules []types.StaffSchedule

	for _, req := range data {
		staffSchedule := types.StaffSchedule{
			StaffScheduleEntity: types.StaffScheduleEntity{
				TenantID:     actx.TenantId,
				LocationID:   actx.LocationId,
				AccountID:    req.AccountID,
				Weekday:      req.Weekday,
				WorkingTimes: req.WorkingTimes,
				IsDayOff:     req.IsDayOff,
			},
		}

		staffSchedules = append(staffSchedules, staffSchedule)
	}

	err := s.NoLimitTx(actx.Context()).Create(&staffSchedules).Error
	if err != nil {
		return nil, err
	}

	return staffSchedules, nil
}

func (s *staffScheduleTables) BatchUpdate(actx *auth.Ctx, data types.BatchUpdateStaffScheduleReq) error {
	if len(data.AccountID) == 0 {
		return nil
	}

	var needUpdate bool
	var updateItem types.UpdateStaffScheduleItem
	if data.IsDayOff != nil {
		updateItem.IsDayOff = data.IsDayOff
		needUpdate = true
	}
	if len(data.WorkingTimes) > 0 {
		b, _ := json.Marshal(data.WorkingTimes)
		workingTime := string(b)
		updateItem.WorkingTime = &workingTime
		needUpdate = true
	}

	if !needUpdate {
		return nil
	}

	err := s.LimitLocationTx(actx).Where("account_id IN ?", data.AccountID).
		Where("weekday IN ?", data.Weekday).
		Updates(updateItem).Error

	return err

}

func (s *staffScheduleTables) List(actx *auth.Ctx) ([]types.StaffSchedule, error) {
	var schedules []types.StaffSchedule
	err := s.LimitLocationTx(actx).Find(&schedules).Error
	if err != nil {
		return nil, err
	}

	return schedules, nil
}

func (s *staffScheduleTables) GetByAccountIds(actx *auth.Ctx, accountIds []string) ([]types.StaffSchedule, error) {
	if len(accountIds) == 0 {
		return []types.StaffSchedule{}, nil
	}

	var schedules []types.StaffSchedule
	err := s.LimitLocationTx(actx).Where("account_id IN ?", accountIds).Find(&schedules).Error
	if err != nil {
		return nil, err
	}

	return schedules, nil

}
