package sqlserver

import (
	"pebble/internal/webapp/types"
	"pebble/pkg/auth"
)

// phoneConfigTable implements IPhoneConfig interface
type phoneConfigTable struct {
	BaseTable
}

// NewPhoneConfigTable creates a new phoneConfigTable instance
func NewPhoneConfigTable() *phoneConfigTable {
	table := &phoneConfigTable{}
	table.SetTableName("phone_configs")
	return table
}

// Create creates a new phone configuration
func (p *phoneConfigTable) Create(actx *auth.Ctx, data types.PhoneConfigEntity) (*types.PhoneConfig, error) {
	phoneConfig := types.PhoneConfig{PhoneConfigEntity: data}
	phoneConfig.TenantID = actx.TenantId
	phoneConfig.LocationID = actx.LocationId

	err := p.NoLimitTx(actx.Context()).Create(&phoneConfig).Error
	return &phoneConfig, err
}

// GetByTenantAndLocation gets phone configuration by tenant and location
func (t *phoneConfigTable) GetByTenantAndLocation(actx *auth.Ctx) (*types.PhoneConfig, error) {
	var phoneConfig types.PhoneConfig
	err := t.LimitLocationTx(actx).First(&phoneConfig).Error
	return &phoneConfig, err
}

// GetByPhoneNumber gets phone configuration by phone number
func (t *phoneConfigTable) GetByPhoneNumber(actx *auth.Ctx, phoneNumber string) (*types.PhoneConfig, error) {
	var phoneConfig types.PhoneConfig
	err := t.LimitLocationTx(actx).
		Where("phone_number = ?", phoneNumber).
		First(&phoneConfig).Error
	return &phoneConfig, err
}

// Update updates phone configuration
func (t *phoneConfigTable) Update(actx *auth.Ctx, req types.UpdatePhoneConfigReq) error {
	return t.LimitLocationTx(actx).Updates(req).Error
}

// Delete soft deletes phone configuration
func (t *phoneConfigTable) Delete(actx *auth.Ctx) error {
	return t.LimitLocationTx(actx).Delete(&types.PhoneConfig{}).Error
}
