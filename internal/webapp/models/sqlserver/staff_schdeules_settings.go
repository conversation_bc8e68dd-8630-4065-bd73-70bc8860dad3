package sqlserver

import (
	"pebble/internal/webapp/types"
	"pebble/pkg/auth"
)

type staffScheduleSettingsTables struct {
	BaseTable
}

func NewStaffScheduleSettingsTables() *staffScheduleSettingsTables {
	table := &staffScheduleSettingsTables{}
	table.SetTableName("staff_schedules_settings")
	return table
}

func (t *staffScheduleSettingsTables) Create(actx *auth.Ctx, req types.CreateStaffScheduleSettingsReq) (*types.StaffScheduleSettings, error) {
	settings := types.StaffScheduleSettings{
		StaffScheduleSettingsEntity: types.StaffScheduleSettingsEntity{
			LocationID: actx.LocationId,
			StartDate:  req.StartDate,
			TenantID:   actx.TenantId,
		},
	}

	err := t.NoLimitTx(actx.Context()).Create(&settings).Error
	if err != nil {
		return nil, err
	}

	return &settings, nil
}

func (s *staffScheduleSettingsTables) Update(actx *auth.Ctx, req types.UpdateStaffScheduleSettingsReq) error {
	return s.LimitLocationTx(actx).Updates(req).Error
}

func (s *staffScheduleSettingsTables) Get(actx *auth.Ctx) (*types.StaffScheduleSettings, error) {
	var settings types.StaffScheduleSettings
	err := s.LimitLocationTx(actx).First(&settings).Error
	if err != nil {
		return nil, err
	}

	return &settings, nil
}
