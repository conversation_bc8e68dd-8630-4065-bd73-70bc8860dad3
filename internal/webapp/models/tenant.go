package models

import (
	"context"
	"pebble/internal/webapp/types"
	"pebble/pkg/auth"
)

type ITenant interface {
	AdminGetTenantByEmail(ctx context.Context, email string) (types.Tenant, error)
	AdminCreateTenant(ctx context.Context, data types.TenantEntity) (types.Tenant, error)

	GetTenant(actx *auth.Ctx) (types.Tenant, error)
	UpdateTenant(actx *auth.Ctx, req types.UpdateTenantReq) error
}

type ILocation interface {
	AdminCreateLocation(ctx context.Context, data types.LocationEntity) (*types.Location, error)
	AdminGetLocationByIdsTenantIds(ctx context.Context, locationIds, tenantIds []string) ([]types.Location, error)
	AdminGetAllLocations(ctx context.Context) ([]types.Location, error)

	GetLocation(actx *auth.Ctx) (*types.Location, error)
	UpdateLocation(actx *auth.Ctx, data types.UpdateLocationReq) error
	GetLocationByTenant(actx *auth.Ctx) ([]types.Location, error)
}

type ITenantMember interface {
	AdminGetTenantMemberByAccountId(ctx context.Context, accountId string) ([]types.TenantMember, error)
	AdminCreateTenantMember(ctx context.Context, data types.TenantMemberEntity) (types.TenantMember, error)

	CreateTenantMember(actx *auth.Ctx, data types.TenantMemberEntity) (types.TenantMember, error)
	GetTenantMembers(actx *auth.Ctx) ([]types.TenantMember, error)
	GetTenantMemberByAccountId(actx *auth.Ctx, accountId string) (*types.TenantMember, error)
	GetTenantMemberByAccountIds(actx *auth.Ctx, accountIds []string) ([]types.TenantMember, error)
	DeleteTenantMemberByAccountId(actx *auth.Ctx, accountId string) error
	UpdateTenantMember(actx *auth.Ctx, accountId string, data types.UpdateTenantMemberReq) error
}
