package models

import (
	"pebble/internal/webapp/types"
	"pebble/pkg/auth"
)

type IServiceCategories interface {
	Create(actx *auth.Ctx, data types.CreateServiceCategoryReq) (*types.ServiceCategory, error)
	Get(actx *auth.Ctx, serviceCategoryId string) (*types.ServiceCategory, error)
	List(actx *auth.Ctx) ([]types.ServiceCategory, error)
	Update(actx *auth.Ctx, serviceCategoryId string, req types.UpdateServiceCategoryReq) error
	Delete(actx *auth.Ctx, serviceCategoryId string) error
}

type IServices interface {
	Create(actx *auth.Ctx, data types.CreateServiceReq) (*types.Service, error)
	Get(actx *auth.Ctx, serviceId string) (*types.Service, error)
	GetByServiceIds(actx *auth.Ctx, serviceIds []string) ([]types.Service, error)
	List(actx *auth.Ctx) ([]types.Service, error)
	Update(actx *auth.Ctx, serviceId string, req types.UpdateServiceReq) error
	Delete(actx *auth.Ctx, serviceId string) error
}

type IServiceStaff interface {
	BatchCreate(actx *auth.Ctx, data []types.ServiceStaffItem) ([]types.ServiceStaff, error)
	Create(actx *auth.Ctx, data types.ServiceStaffItem) (*types.ServiceStaff, error)
	ListByServiceId(actx *auth.Ctx, serviceId string) ([]types.ServiceStaff, error)
	List(actx *auth.Ctx) ([]types.ServiceStaff, error)
	BatchDelete(actx *auth.Ctx, serviceId string, accountIds []string) error
}
