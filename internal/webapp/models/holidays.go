package models

import (
	"pebble/internal/webapp/types"
	"pebble/pkg/auth"
)

type IHoliday interface {
	GetByDates(ctx *auth.Ctx, date []string) ([]types.Holiday, error)
	Create(ctx *auth.Ctx, data types.CreateHolidayReq) (*types.Holiday, error)
	BatchCreate(ctx *auth.Ctx, data []types.CreateHolidayReq) ([]types.Holiday, error)
	Update(ctx *auth.Ctx, holidayId string, data types.UpdateHolidayReq) error
	Delete(actx *auth.Ctx, holidayId string) error
	List(ctx *auth.Ctx) ([]types.Holiday, error)
}
