package models

import (
	"pebble/internal/webapp/types"
	"pebble/pkg/auth"
)

type IStaffSchedule interface {
	BatchCreate(actx *auth.Ctx, data []types.CreateStaffScheduleReq) ([]types.StaffSchedule, error)
	// Create(actx *auth.Ctx, data types.CreateStaffScheduleReq) (*types.StaffSchedule, error)
	// Get(actx *auth.Ctx, accountId string) (*types.StaffSchedule, error)
	BatchUpdate(actx *auth.Ctx, data types.BatchUpdateStaffScheduleReq) error
	// Update(actx *auth.Ctx, data types.UpdateStaffScheduleReq) error
	List(actx *auth.Ctx) ([]types.StaffSchedule, error)
	GetByAccountIds(actx *auth.Ctx, accountIds []string) ([]types.StaffSchedule, error)
}

type IStaffScheduleOverride interface {
	BatchCreate(actx *auth.Ctx, data []types.CreateStaffScheduleOverrideReq) ([]types.StaffScheduleOverride, error)
	BatchUpdate(actx *auth.Ctx, data types.BatchUpdateStaffScheduleOverrideReq) error
	List(actx *auth.Ctx, params types.QueryMultiStaffSchedulesDateParams) ([]types.StaffScheduleOverride, error)
	GetByAccountIds(actx *auth.Ctx, accountIds []string) ([]types.StaffScheduleOverride, error)
	// Get(actx *auth.Ctx, date string) ([]types.StaffScheduleOverride, error)
}

type IStaffScheduleSettings interface {
	Create(actx *auth.Ctx, req types.CreateStaffScheduleSettingsReq) (*types.StaffScheduleSettings, error)
	Update(actx *auth.Ctx, req types.UpdateStaffScheduleSettingsReq) error
	Get(actx *auth.Ctx) (*types.StaffScheduleSettings, error)
}
