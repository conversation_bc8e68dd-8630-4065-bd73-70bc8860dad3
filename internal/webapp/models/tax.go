package models

import (
	"pebble/pkg/auth"

	"pebble/internal/webapp/types"
)

// ITax defines the interface for tax data operations
type ITax interface {
	// Create creates a new tax record
	Create(actx *auth.Ctx, data types.TaxEntity) (*types.Tax, error)

	// Get retrieves a tax record by ID
	Get(actx *auth.Ctx, taxId string) (*types.Tax, error)
	GetByIds(actx *auth.Ctx, taxIds []string) ([]types.Tax, error)

	// List retrieves all tax records
	List(actx *auth.Ctx) ([]*types.Tax, error)

	// Update updates an existing tax record
	Update(actx *auth.Ctx, taxId string, tax types.UpdateTaxReq) error

	// Delete removes a tax record
	Delete(actx *auth.Ctx, taxId string) error
}
