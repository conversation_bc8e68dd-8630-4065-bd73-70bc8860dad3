package models

import (
	"context"
	"pebble/internal/webapp/types"
	"pebble/pkg/auth"
)

type IAccounts interface {
	AdminCreateAccount(ctx context.Context, data types.AccountEntity) (types.Account, error)
	AdminGetAccountByEmail(ctx context.Context, email string) (types.Account, error)
	AdminGetAccountByPhoneNumber(ctx context.Context, phoneNumber string) (types.Account, error)
	AdminGetAccountById(ctx context.Context, accountId string) (types.Account, error)
	AdminGetAccountByIds(ctx context.Context, accountIds []string) ([]types.Account, error)
	GetAccount(actx *auth.Ctx) (types.Account, error)
	UpdateAccount(actx *auth.Ctx, data types.UpdateAccountReq) error
}
