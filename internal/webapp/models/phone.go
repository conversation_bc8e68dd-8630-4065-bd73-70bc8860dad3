package models

import (
	"pebble/internal/webapp/types"
	"pebble/pkg/auth"
)

// IPhoneConfig defines the interface for phone number management
type IPhoneConfig interface {
	// Create creates a new phone configuration
	Create(actx *auth.Ctx, data types.PhoneConfigEntity) (*types.PhoneConfig, error)

	// GetByTenantAndLocation gets phone configuration by tenant and location
	GetByTenantAndLocation(actx *auth.Ctx) (*types.PhoneConfig, error)

	// GetByPhoneNumber gets phone configuration by phone number
	GetByPhoneNumber(actx *auth.Ctx, phoneNumber string) (*types.PhoneConfig, error)

	// Update updates phone configuration
	Update(actx *auth.Ctx, req types.UpdatePhoneConfigReq) error

	// Delete soft deletes phone configuration
	Delete(actx *auth.Ctx) error
}
