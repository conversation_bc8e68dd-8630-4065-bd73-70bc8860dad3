package models

import (
	"pebble/internal/webapp/types"
	"pebble/pkg/auth"
)

type IRole interface {
	CreateRole(actx *auth.Ctx, req types.CreateRoleReq) (*types.Role, error)
	UpdateRole(actx *auth.Ctx, roleId string, req types.UpdateRoleReq) error
	DeleteRole(actx *auth.Ctx, roleId string) error
	QueryRoles(actx *auth.Ctx, params types.QueryRolesParams) ([]types.Role, error)
	QueryRole(actx *auth.Ctx, roleId string) (*types.Role, error)
	GetRoleByIds(actx *auth.Ctx, roleIds []string) ([]types.Role, error)
}

type IPermission interface {
	QueryPermissions(actx *auth.Ctx, params types.QueryPermissionsParams) ([]types.Permission, error)
	GetPermission(actx *auth.Ctx, permissionId string) (*types.Permission, error)
	GetPermissionByIds(actx *auth.Ctx, permissionIds []string) ([]types.Permission, error)
	CreatePermission(actx *auth.Ctx, data types.CreatePermissionReq) (*types.Permission, error)
	UpdatePermission(actx *auth.Ctx, permissionId string, data types.UpdatePermissionReq) error
	DeletePermission(actx *auth.Ctx, permissionId string) error
}

type IRolePermission interface {
	BatchCreateRolePermissions(actx *auth.Ctx, req types.BatchCreateRolePermissionReq) ([]types.RolePermission, error)
	CreateRolePermission(actx *auth.Ctx, req types.CreateRolePermissionReq) (*types.RolePermission, error)
	DeleteRolePermission(actx *auth.Ctx, roleId, permissionId string) error
	DeleteRolePermissions(actx *auth.Ctx, roleId string, permissionIds []string) error
	QueryRolePermissions(actx *auth.Ctx, roleId string) ([]types.RolePermission, error)
	QueryRolePermissionsByRoleIds(actx *auth.Ctx, roleIds []string) ([]types.RolePermission, error)
}

type ITenantMemberRoles interface {
	BatchCreateTenantMemberRoles(actx *auth.Ctx, req types.CreateTenantMemberRolesReq) ([]types.TenantMemberRoles, error)
	BatchDeleteTenantMemberRoles(actx *auth.Ctx, req types.DeleteTenantMemberRolesReq) error
	GetTenantMemberRoles(actx *auth.Ctx, accountId string) ([]types.TenantMemberRoles, error)
	GetTenantMemberRoleByAccountIds(actx *auth.Ctx, accountIds []string) ([]types.TenantMemberRoles, error)
}
