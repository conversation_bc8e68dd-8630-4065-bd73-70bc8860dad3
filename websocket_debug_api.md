# WebSocket Debug API 使用说明

## 概述

WebSocket Debug API 提供了一组HTTP接口，用于在开发环境中测试WebSocket推送功能。这些接口可以伪造各种业务数据并通过WebSocket发送给客户端。

**注意：这些接口仅在开发环境（`env: dev` 或 `env: development`）中可用。**

## API 接口

### 1. 获取WebSocket统计信息

**GET** `/api/v1/debug/websocket/stats`

获取当前WebSocket连接的统计信息。

**响应示例：**
```json
{
  "success": true,
  "data": {
    "stats": {
      "total_connections": 5,
      "tenant_connections": 2,
      "location_connections": 3,
      "account_connections": 5,
      "running": true
    },
    "timestamp": *************
  }
}
```

### 2. 发送Stripe Terminal成功消息

**POST** `/api/v1/debug/websocket/stripe-terminal/success`

伪造Stripe Terminal支付成功消息并通过WebSocket推送。

**查询参数：**
- `tenant_id` (可选): 租户ID，默认 "tenant_123"
- `location_id` (可选): 位置ID，默认 "loc_456"
- `account_id` (可选): 账户ID，默认 "acc_789"
- `client_id` (可选): 客户ID，默认 "client_001"
- `order_id` (可选): 订单ID，默认自动生成
- `amount` (可选): 金额（分），默认 5000 ($50.00)
- `tip` (可选): 小费（分），默认 500 ($5.00)
- `fee` (可选): 手续费（分），默认 150 ($1.50)
- `tax` (可选): 税费（分），默认 400 ($4.00)

**请求示例：**
```bash
curl -X POST "http://localhost:8082/api/v1/debug/websocket/stripe-terminal/success?tenant_id=tenant_123&location_id=loc_456&account_id=acc_789&amount=10000&tip=1000"
```

**响应示例：**
```json
{
  "success": true,
  "data": {
    "message": "Stripe Terminal success message sent successfully",
    "data": {
      "tenant_id": "tenant_123",
      "location_id": "loc_456",
      "client_id": "client_001",
      "target_id": "target_client_001",
      "order_id": "order_1737360000",
      "order_type": 1,
      "amount": 10000,
      "tip": 1000,
      "fee": 150,
      "tax": 400,
      "fee_payer": 1,
      "payment_status": 2,
      "timestamp": *************,
      "debug": true
    }
  }
}
```

### 3. 发送Stripe Terminal失败消息

**POST** `/api/v1/debug/websocket/stripe-terminal/failed`

伪造Stripe Terminal支付失败消息并通过WebSocket推送。

**查询参数：**
- `tenant_id` (可选): 租户ID，默认 "tenant_123"
- `location_id` (可选): 位置ID，默认 "loc_456"
- `account_id` (可选): 账户ID，默认 "acc_789"
- `client_id` (可选): 客户ID，默认 "client_001"
- `order_id` (可选): 订单ID，默认自动生成
- `amount` (可选): 金额（分），默认 5000 ($50.00)
- `error_code` (可选): 错误代码，默认 "card_declined"
- `error_message` (可选): 错误消息，默认 "Your card was declined"

**请求示例：**
```bash
curl -X POST "http://localhost:8082/api/v1/debug/websocket/stripe-terminal/failed?tenant_id=tenant_123&location_id=loc_456&account_id=acc_789&error_code=insufficient_funds&error_message=Insufficient funds"
```

### 4. 发送自定义WebSocket消息

**POST** `/api/v1/debug/websocket/custom`

发送自定义WebSocket消息，可以指定消息类型、事件和内容。

**查询参数：**
- `tenant_id` (可选): 租户ID，默认 "tenant_123"
- `location_id` (可选): 位置ID，默认 "loc_456"
- `account_id` (可选): 账户ID，默认 "acc_789"
- `type` (可选): 消息类型，默认 "notify"
- `event` (可选): 事件名称，默认 "debug.test"
- `message` (可选): 消息内容，默认 "This is a debug message"

**推送范围规则：**
- 如果提供了 `account_id`，则推送到特定账户
- 如果只提供了 `location_id`，则推送到整个位置
- 如果只提供了 `tenant_id`，则推送到整个租户

**请求示例：**
```bash
# 推送到特定账户
curl -X POST "http://localhost:8082/api/v1/debug/websocket/custom?tenant_id=tenant_123&location_id=loc_456&account_id=acc_789&type=notify&event=appointment.reminder&message=Your appointment is in 15 minutes"

# 推送到整个位置
curl -X POST "http://localhost:8082/api/v1/debug/websocket/custom?tenant_id=tenant_123&location_id=loc_456&type=broadcast&event=system.maintenance&message=System maintenance starting"

# 推送到整个租户
curl -X POST "http://localhost:8082/api/v1/debug/websocket/custom?tenant_id=tenant_123&type=notify&event=system.update&message=New features available"
```

## WebSocket消息格式

所有通过WebSocket发送的消息都遵循统一格式：

```json
{
  "id": "msg_unique_id",
  "type": "notify",
  "event": "stripe-terminal.succeeded",
  "data": {
    // 具体的业务数据
  },
  "timestamp": *************,
  "meta": {
    "target": {
      "tenant_id": "tenant_123",
      "location_id": "loc_456",
      "account_id": "acc_789"
    },
    "priority": 2,
    "reliable": true
  }
}
```

## 测试流程

1. **启动服务**：
   ```bash
   go run cmd/webapp/main.go
   ```

2. **连接WebSocket客户端**：
   - 打开 `websocket_client_test.html`
   - 输入有效的JWT token
   - 连接到 `ws://localhost:8082/api/v1/ws`

3. **发送测试消息**：
   使用上述API接口发送各种测试消息

4. **观察客户端**：
   在WebSocket客户端中观察接收到的消息

## 常见用例

### 测试支付流程
```bash
# 模拟支付成功
curl -X POST "http://localhost:8082/api/v1/debug/websocket/stripe-terminal/success?amount=5000&tip=500"

# 模拟支付失败
curl -X POST "http://localhost:8082/api/v1/debug/websocket/stripe-terminal/failed?error_code=card_declined"
```

### 测试通知系统
```bash
# 预约提醒
curl -X POST "http://localhost:8082/api/v1/debug/websocket/custom?type=notify&event=appointment.reminder&message=Appointment in 15 minutes"

# 系统维护通知
curl -X POST "http://localhost:8082/api/v1/debug/websocket/custom?type=broadcast&event=system.maintenance&message=Maintenance starting in 10 minutes"
```

### 监控连接状态
```bash
# 获取连接统计
curl "http://localhost:8082/api/v1/debug/websocket/stats"
```

## 注意事项

1. **环境限制**：这些接口仅在开发环境中可用
2. **调试标记**：所有通过debug接口发送的消息都包含 `"debug": true` 标记
3. **数据伪造**：这些接口发送的是伪造数据，仅用于测试
4. **连接要求**：需要有活跃的WebSocket连接才能接收消息
5. **权限验证**：WebSocket连接需要有效的JWT token
