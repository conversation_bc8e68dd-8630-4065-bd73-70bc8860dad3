package main

import (
	_ "pebble/api/webapp" // Import generated docs
	"pebble/internal/webapp"
)

//	@title			Pebble API
//	@version		1.0
//	@description	This is the API documentation for the Pebble application.
//	@termsOfService	http://swagger.io/terms/

//	@contact.name	API Support
//	@contact.url	http://www.example.com/support
//	@contact.email	<EMAIL>

//	@license.name	Apache 2.0
//	@license.url	http://www.apache.org/licenses/LICENSE-2.0.html

//	@host		localhost:8081
//	@BasePath	/api/v1

//	@securityDefinitions.apikey	ApiKeyAuth
//	@in	header
//	@name	Authorization

func main() {
	// Initialize and run webapp
	webapp.Init()
	webapp.Run()
}
