# Pebble System

Pebble 是一个基于 Go 语言开发的多项目、多租户应用程序框架。当前主要包含一个 WebApp 应用（多租户预约系统雏形），并规划扩展更多应用。

本文档旨在作为 Pebble 项目的综合指南，涵盖项目介绍、技术栈、架构、开发、配置、部署和未来规划等各个方面。

## 目录

- [Pebble System](#pebble-system)
  - [目录](#目录)
  - [项目概述](#项目概述)
  - [技术栈](#技术栈)
  - [项目结构](#项目结构)
  - [环境要求](#环境要求)
  - [快速开始](#快速开始)
    - [1. 克隆项目](#1-克隆项目)
    - [2. 安装依赖](#2-安装依赖)
    - [3. 配置环境](#3-配置环境)
    - [4. 运行项目](#4-运行项目)
    - [5. 构建与部署 (使用 Makefile)](#5-构建与部署-使用-makefile)
  - [配置管理](#配置管理)
    - [配置文件](#配置文件)
    - [配置加载](#配置加载)
    - [主要配置项 (WebApp)](#主要配置项-webapp)
  - [运行与部署](#运行与部署)
    - [本地运行](#本地运行)
    - [Docker 运行](#docker-运行)
    - [自动化部署 (CI/CD)](#自动化部署-cicd)
    - [部署脚本 (`scripts/webapp/`)](#部署脚本-scriptswebapp)
  - [应用程序](#应用程序)
    - [WebApp](#webapp)
  - [核心库 (`pkg/`)](#核心库-pkg)
  - [开发指南](#开发指南)
    - [代码规范](#代码规范)
    - [测试](#测试)
    - [版本管理与发布](#版本管理与发布)
  - [未来规划](#未来规划)
  - [许可证](#许可证)
  - [联系方式](#联系方式)

## 项目概述

Pebble 的目标是提供一个健壮、可扩展的 Go 后端应用基础框架，特别适用于需要多租户管理的场景（如 SaaS 服务、连锁店管理系统等）。

**核心概念:**

- **Tenant (租户):** 代表一个独立的组织或客户，拥有自己的数据和配置隔离。一个 Tenant 可以包含多个 Location。
- **Location (地点/店铺):** 代表 Tenant 下的一个具体实体，如一个分店或办公地点。Location 拥有自己的时区等具体信息。
- **Account (账户):** 指系统的内部用户或管理员，属于某个 Tenant 和 Location。
- **Client (客户):** 指使用 Tenant 服务的最终用户或消费者。

**当前状态:**

- 主要实现了一个 **WebApp** 应用，作为多租户预约系统的基础。
- 提供了用户认证、租户/地点管理、客户管理、员工管理的基础 API。
- 集成了常用的 Go 生态库，封装了配置、日志、数据库、JWT 等通用模块。
- 实现了基于 GitHub Actions 的自动化部署流程。

## 技术栈

- **开发语言:** [Go 1.23.2](https://go.dev/dl/)
- **Web 框架:** [Gin](https://github.com/gin-gonic/gin)
- **数据库 ORM:** [GORM](https://gorm.io/)
- **数据库:** [MySQL](https://www.mysql.com/)
- **配置管理:** [Viper](https://github.com/spf13/viper) (通过 `pkg/uconfig` 封装)
- **日志系统:** [Zap](https://github.com/uber-go/zap) + [Lumberjack](https://github.com/natefinch/lumberjack) (通过 `pkg/ulog` 封装)
- **认证:** [JWT](https://jwt.io/) (通过 `pkg/ujwt` 封装)
- **密码加密:** Bcrypt (通过 `pkg/ubcrypt` 封装)
- **ID 生成:** KSUID (通过 `pkg/uid` 封装)
- **参数验证:** [go-playground/validator](https://github.com/go-playground/validator) (通过 `pkg/ugin` 集成)
- **SMS 服务:** [Twilio](https://www.twilio.com/) (通过 `pkg/utwilio` 封装，具体使用待确认)
- **构建工具:** Make
- **容器化:** Docker, Docker Compose
- **CI/CD:** GitHub Actions

## 项目结构

本项目采用标准的 Go 项目布局，结构清晰，易于扩展。

```txt
.
├── .github/
│   └── workflows/          # CI/CD 工作流配置 (GitHub Actions, e.g., dev-webapp-deploy-to-ec2.yml)
├── api/                    # API 文档定义 (例如 Swagger/OpenAPI)
│   └── webapp/             # WebApp 应用的 API 文档
│       ├── swagger.json    # Swagger JSON 格式定义
│       ├── swagger.yaml    # Swagger YAML 格式定义
│       └── docs.go         # Go-Swagger 生成的文档代码
├── build/                  # 构建产物目录
│   ├── package/            # 打包相关文件 (如 Dockerfile, docker-compose.yml)
│   │   └── webapp/
│   │       ├── dockerfile
│   │       └── docker-compose.yml
│   └── webapp              # WebApp 构建后的可执行文件
├── cmd/
│   ├── booking/            # Booking 应用入口 (规划中)
│   └── webapp/             # WebApp 应用入口 ([main.go](mdc:cmd/webapp/main.go))
├── configs/                # 应用配置文件目录 (Viper)
│   ├── webapp.yaml         # WebApp 应用的实际配置文件 (示例, 基于模板创建)
│   └── webapp_template.yaml # WebApp 应用的配置模板 ([webapp_template.yaml](mdc:configs/webapp_template.yaml))
├── docs/                   # 项目文档 (设计文档、数据库结构等)
│   ├── webapp_db.md        # WebApp 数据库结构说明 ([webapp_db.md](mdc:docs/webapp_db.md))
│   └── ...                 # 其他文档 (如 Twilio 集成、AI 功能文档)
├── internal/               # 各个应用的私有业务逻辑和内部库
│   ├── booking/            # Booking 核心业务逻辑 (规划中)
│   └── webapp/             # WebApp 核心业务逻辑
│       ├── config/         # 配置加载与解析 ([config.go](mdc:internal/webapp/config/config.go))
│       ├── docs/           # WebApp 内部模块的文档或说明
│       ├── handler/        # HTTP 请求处理器 (Gin Handlers)
│       ├── models/         # 数据库模型 (GORM Models)
│       ├── router.go       # 应用路由定义 ([router.go](mdc:internal/webapp/router.go))
│       ├── server.go       # HTTP 服务器设置 ([server.go](mdc:internal/webapp/server.go))
│       ├── services/       # 业务逻辑服务层
│       └── types/          # API 请求/响应结构体定义
├── logs/                   # 运行时日志文件目录 (由应用和脚本生成)
├── pkg/                    # 项目级的可共享公共库 (详见 [核心库 (`pkg/`)](#核心库-pkg) 部分)
│   ├── auth/               # 认证相关辅助功能
│   ├── ubcrypt/            # Bcrypt 密码加密封装
│   ├── uconfig/            # Viper 配置加载封装
│   ├── udb/                # 数据库 (GORM) 连接和操作封装
│   ├── uerror/             # 统一错误处理机制
│   ├── email/              # 邮件发送服务封装 (可能基于 Mandrill 或其他)
│   ├── ugin/               # Gin 框架扩展 (中间件, 验证器, 响应写入器)
│   ├── uid/                # KSUID 生成封装
│   ├── ujwt/               # JWT 认证工具
│   ├── ulog/               # Zap 日志封装
│   ├── mandrill/           # Mandrill 邮件服务客户端封装
│   ├── utime/              # 时间处理工具库
│   ├── utwilio/            # Twilio 服务封装
│   ├── util/               # 通用工具函数
│   └── version/            # 版本信息管理 (构建时注入)
├── scripts/                # 部署和运维脚本
│   └── webapp/             # WebApp 专用脚本 (详见 [部署脚本 (`scripts/webapp/`)](#部署脚本-scriptswebapp) 部分)
├── .gitignore              # Git 忽略文件配置
├── go.mod                  # Go 模块依赖定义 ([go.mod](mdc:go.mod))
├── go.sum                  # Go 模块校验和
├── makefile                # 项目构建、运行、部署脚本 ([makefile](mdc:makefile))
└── README.md               # 项目说明文档 (本文档)
```

## 环境要求

- **Go:** 1.23.2 或更高版本
- **Make:** 用于执行 `makefile` 中的命令
- **MySQL:** 数据库存储
- **Docker & Docker Compose:** (可选) 用于容器化运行和部署
- **Git:** 版本控制
- **golangci-lint:** (推荐) 用于代码风格检查

## 快速开始

以下步骤将指导您在本地设置和运行 WebApp 应用。

### 1. 克隆项目

```bash
git clone [项目仓库地址] # 替换为实际地址
cd pebble
```

### 2. 安装依赖

```bash
go mod download
```

### 3. 配置环境

WebApp 的配置通过 `configs/webapp.yaml` 文件管理。

- **复制模板:**
  ```bash
  cp configs/webapp_template.yaml configs/webapp.yaml
  ```
- **修改配置:** 使用您喜欢的编辑器打开 `configs/webapp.yaml`，并根据您的本地环境修改以下关键配置：
    - `database`: MySQL 连接信息 (host, port, user, password, dbname)
    - `jwt`: JWT 密钥 (`secret`) 和过期时间 (`expires_minutes`)
    - 其他配置项（如 `app.port`）可按需调整。

  *注意: 请勿将包含敏感信息的 `webapp.yaml` 文件提交到版本控制中。*

### 4. 运行项目

我们推荐使用 `makefile` 来简化构建和运行流程。

```bash
make webapp
```

此命令会自动完成以下操作：
1.  编译 `cmd/webapp/main.go`，将可执行文件输出到 `build/webapp`。
2.  在编译时注入版本信息 (Git Tag, Commit Hash, Build Date)。
3.  查找并停止任何已在运行的 `webapp` 进程。
4.  在后台启动新的 `webapp` 进程，并将错误日志输出到 `logs/error.log` (基于 `scripts/webapp/deploy.sh` 逻辑，注意 `makefile` 中直接启动可能输出到 `logs/error.log`)。

启动成功后，WebApp 服务将在配置文件中指定的端口（默认为 8081）上监听。

您也可以直接运行 Go 源码（主要用于开发调试）：

```bash
go run cmd/webapp/main.go
```

### 5. 构建与部署 (使用 Makefile)

`makefile` 提供了一些便捷的命令：

- **本地构建并运行:**
  ```bash
  make webapp
  ```
- **构建 Docker 镜像:** (需要 Docker 环境)
  ```bash
  make webapp-images
  ```
  这将根据 `build/package/webapp/dockerfile` 构建一个名为 `webapp:<version>` 的 Docker 镜像。
- **使用 Docker Compose 运行:** (需要 Docker & Docker Compose 环境)
  ```bash
  make webapp-up
  ```
  这将根据 `build/package/webapp/docker-compose.yml` 启动 WebApp 服务及其可能依赖的服务（如数据库）。它会先尝试构建镜像。
- **停止服务:** `make webapp-down`
- **查看状态:** `make webapp-ps`
- **清理构建产物:**
  ```bash
  make clean
  ```
- **运行测试:**
  ```bash
  make test
  ```

## 配置管理

Pebble 项目使用 [Viper](https://github.com/spf13/viper) 进行配置管理，并通过 `pkg/uconfig` 包进行了封装，提供了灵活的配置加载方式。

### 配置文件

- **位置:** 主要配置文件位于项目根目录下的 `configs/` 目录。
- **模板:** 每个应用应提供一个配置模板文件，例如 `configs/webapp_template.yaml`。
- **实际配置:** 实际使用的配置文件应根据模板创建，例如 `configs/webapp.yaml`。**实际配置文件不应提交到版本库。**

### 配置加载

配置加载由 `internal/<app>/config/config.go` 中的 `Init()` 函数（内部调用 `pkg/uconfig`）处理，优先级如下（从高到低）：

1.  **命令行参数:**
    - `-config <path>`: 指定配置文件或配置目录的路径。
    - `-env <environment>`: 指定运行环境 (如 `dev`, `prod`, `test`)，会影响加载的配置文件名 (如 `webapp_dev.yaml`) 和 Viper 的环境变量读取逻辑。
    - `-config-name <name>`: 指定配置文件的基础名称（不含扩展名和环境后缀），默认为 `webapp`。
2.  **环境变量:** Viper 会自动尝试读取环境变量。`pkg/uconfig` 设置了特定应用的环境变量前缀（例如 WebApp 为 `WEBAPP_`）。例如，可以通过设置 `WEBAPP_DATABASE_PASSWORD=your_db_password` 来覆盖配置文件中的数据库密码。
3.  **配置文件:** 根据 `-config`, `-env`, `-config-name` 参数或默认设置，在指定路径 (`./configs`, `./config`, `.`) 查找并加载对应的 YAML 文件 (例如 `configs/webapp.yaml` 或 `configs/webapp_dev.yaml`)。
4.  **默认值:** 可以在代码中的配置结构体定义中设置默认值（当前实现中较少使用）。

### 主要配置项 (WebApp)

以下是 `configs/webapp_template.yaml` 中定义的主要配置项及其说明：

```yaml
# 应用基本配置
app:
  name: webapp           # 应用名称
  env: ""               # 运行环境 (dev, test, prod), 留空则不区分环境加载配置
  mode: debug           # Gin 运行模式 (debug, release, test)
  port: 8081            # 应用监听端口
  domain: https://api.nononoilike.xyz # 应用外部访问域名

# 数据库配置
database:
  host: 127.0.0.1       # MySQL 主机地址
  port: 3306            # MySQL 端口
  username: test_user   # 数据库用户名
  password: "123456"    # 数据库密码
  Name: test_db         # 数据库名称
  max_conn: 100         # 最大连接数
  max_idle_conn: 10     # 最大空闲连接数
  slow_threshold: 100   # 慢查询阈值 (ms)
  log_level: info       # GORM 日志级别 (silent, error, warn, info)

# 日志配置 (使用 Zap + Lumberjack)
log:
  level: info           # 日志级别 (debug, info, warn, error, panic, fatal)
  prefix: webapp        # 日志文件名前缀
  path: logs            # 日志文件存放目录
  max_size: 1024        # 单个日志文件最大尺寸 (MB)
  max_age: 30           # 旧日志文件最大保留天数
  max_backups: 10       # 最多保留的旧日志文件数量
  compress: false       # 是否压缩旧日志文件

# JWT 认证配置
jwt:
  secret: "ny9SBWlomK5ixCBzYURM2UJZSvK6PfPA" # JWT 签名密钥 (!!!请务必修改!!!)
  issuer: webapp.com    # JWT 签发者
  access_token_expire: 3600   # Access Token 过期时间 (秒)
  refresh_token_expire: 2592000 # Refresh Token 过期时间 (秒)
```

其他配置项，如第三方服务（例如 Twilio）的 API 密钥，也应在此文件中进行配置。请参考模板文件和 `internal/webapp/config/config.go` 中的结构体定义。

## 运行与部署

### 本地运行

- **使用 `make` (推荐):** `make webapp`
- **直接运行源码:** `go run cmd/webapp/main.go`

### Docker 运行

前提：已安装 Docker 和 Docker Compose。

1.  **构建镜像:** `make webapp-images`
2.  **使用 Docker Compose 启动:** `make webapp-up`
   这将根据 `build/package/webapp/docker-compose.yml` 启动服务。该文件可能包含 WebApp 服务和一个 MySQL 数据库服务。您可能需要根据需要调整 `docker-compose.yml` 中的配置（如端口映射、卷挂载、环境变量等）。
3.  **停止服务:** `make webapp-down`
4.  **查看状态:** `make webapp-ps`

### 自动化部署 (CI/CD)

本项目使用 [GitHub Actions](https://github.com/features/actions) 实现 `dev` 分支的自动化部署到 EC2 服务器。

- **工作流文件:** [`.github/workflows/dev-webapp-deploy-to-ec2.yml`](mdc:.github/workflows/dev-webapp-deploy-to-ec2.yml)
- **触发条件:** 代码推送到 `dev` 分支。
- **主要流程:**
    1.  **Test:** 运行 `go test` 进行单元测试，运行 `golangci-lint` 进行代码质量检查。
    2.  **Build:**
        - 安装 Go 环境和依赖。
        - 从 `scripts/webapp/config.sh` 加载应用配置（如应用名、构件名）。
        - 编译 Go 代码，注入版本信息，生成二进制文件到 `build/webapp`。
        - 从 GitHub Secret `DEV_WEBAPP_CONFIG_BASE64` 恢复 `configs/webapp.yaml` 配置文件。
        - 将二进制文件、恢复的配置文件、部署脚本 (`scripts/webapp/`) 打包成 `webapp.tar.gz` 构件。
        - 上传构件。
    3.  **Deploy (仅在 push 到 dev 时触发):**
        - 下载构件。
        - 使用 `scp` 将构件上传到 EC2 服务器的 `/tmp` 目录。
        - 使用 `ssh` 连接到 EC2 服务器，执行以下操作：
            - 创建远程部署目录 (`/home/<USER>/app/pebble`，由 `config.sh` 定义)。
            - 解压构件到部署目录。
            - 执行 `scripts/webapp/init.sh` 进行环境初始化（创建目录、设置 Cron 任务）和首次部署（调用 `deploy.sh`）。
            - 清理临时构件文件。
    4.  **Verify (仅在 push 到 dev 时触发):**
        - 使用 `ssh` 连接到 EC2 服务器，执行以下操作：
            - 检查 Cron 监控任务是否设置，如果没有则设置。
            - 等待一段时间后，通过 `curl` 访问 `/health` 端点，验证应用是否健康运行。
    5.  **Notify:**
        - 根据 `deploy` 步骤的结果，通过飞书 (Feishu) Webhook 发送部署成功或失败的通知。

- **必要的 GitHub Secrets:**
    - `EC2_HOST`: EC2 服务器主机名或 IP 地址。
    - `EC2_USERNAME`: EC2 服务器 SSH 用户名。
    - `EC2_SSH_KEY`: 用于 SSH 连接的私钥。
    - `DEV_WEBAPP_CONFIG_BASE64`: **Base64 编码**后的 `webapp.yaml` 配置文件内容，用于在 CI/CD 流程中恢复生产环境配置，避免将生产配置直接写入代码库。
    - `FEISHU_WEBHOOK_URL`: 用于发送部署状态通知的飞书机器人 Webhook URL。

- **部署配置:** 部署相关的配置（如远程目录、应用名、端口、日志路径、监控间隔等）统一由部署目标服务器上的 `scripts/webapp/config.sh` 管理。CI/CD 流程会读取此文件以获取必要的环境变量。

### 部署脚本 (`scripts/webapp/`)

位于服务器部署目录下的 `scripts/webapp/` 包含一组用于管理应用生命周期的 Shell 脚本：

- **[`config.sh`](mdc:scripts/webapp/config.sh):** 核心配置文件，定义了应用名称、端口、日志目录、部署目录、健康检查端点、监控间隔、备份设置等所有可配置参数。修改此文件可以调整应用的部署和运行行为。
- **[`init.sh`](mdc:scripts/webapp/init.sh):** 环境初始化脚本。由 CI/CD 在首次部署或需要重置环境时调用。负责创建必要的目录（如日志目录），确保脚本可执行，调用 `setup_cron.sh` 设置监控和备份的定时任务，并调用 `deploy.sh` 启动应用。
- **[`deploy.sh`](mdc:scripts/webapp/deploy.sh):** 应用部署/重启脚本。负责安全地停止当前正在运行的应用实例（先尝试优雅停止，超时后强制停止），然后使用 `nohup` 在后台启动新的应用实例，并将日志输出到 `config.sh` 中定义的日志文件。
- **[`monitor.sh`](mdc:scripts/webapp/monitor.sh):** 应用监控脚本。由 Cron 定时任务周期性调用。它会检查应用进程是否存在，如果不存在则调用 `deploy.sh` 尝试重启。如果进程存在，它会通过 `curl` 访问 `config.sh` 中定义的健康检查端点 (`/health`)，如果检查失败，也会调用 `deploy.sh` 尝试重启。监控结果会记录到 `config.sh` 定义的监控日志文件中。
- **[`setup_cron.sh`](mdc:scripts/webapp/setup_cron.sh):** Cron 定时任务设置脚本。由 `init.sh` 调用，或在 CI/CD 的 `Verify` 阶段调用以确保任务存在。负责将 `monitor.sh` 和 `backup.sh` 添加到系统的 crontab 中，实现应用的定时健康检查/自动重启和数据备份。会检查任务是否已存在，避免重复添加。
- **[`backup.sh`](mdc:scripts/webapp/backup.sh):** (可选) 备份脚本。由 Cron 定时任务周期性调用（如果 `config.sh` 中开启了备份）。负责备份配置文件、日志文件，以及可能的数据库备份（需在 `config.sh` 中配置数据库信息）。

## 应用程序

### WebApp

WebApp 是 Pebble 项目当前的核心应用，作为一个多租户预约系统的基础实现。

**主要功能:**

- **用户认证:** 提供注册、登录、刷新 Token、验证 Token 的 API。使用 JWT 进行状态管理。
- **多租户管理:**
    - Tenant (租户) 查询与更新。
    - Location (地点/店铺) 查询与更新。
- **客户管理 (Client):** 提供对租户客户信息的 CRUD 操作。
- **员工管理 (Staff):** 提供邀请员工、查询员工列表/详情、移除员工的操作。

**主要 API 端点 (基于 `internal/webapp/router.go`):**

- `/version` (GET): 获取应用版本信息。
- `/health` (GET): 健康检查接口（检查数据库连接）。
- `/api/v1`: API V1 版本根路径。
    - `/auth`:
        - `/register` (POST): 注册新账户。
        - `/login` (POST): 账户登录，返回 JWT Token。
        - `/refresh` (POST): 使用 Refresh Token 获取新的 Access Token。
        - `/validate` (POST): (需 JWT 认证) 验证当前 Token 是否有效。
        - `/logout` (POST): (TODO) 登出。
        - `/forgot-password` (POST): (TODO) 忘记密码。
    - `/tenants` (GET, PUT): (需 JWT 认证) 管理租户信息。
    - `/locations` (GET, PUT): (需 JWT 认证) 管理地点/店铺信息。
    - `/clients` (GET, POST, PUT, DELETE): (需 JWT 认证) 管理客户信息。
    - `/staffs` (GET, POST, DELETE): (需 JWT 认证) 管理员工信息。

*(注意: 上述 API 列表仅为基本概述，具体的请求/响应格式、参数验证规则等需要查阅 `internal/webapp/types/` 和 `internal/webapp/handler/` 下的源码。)*

## 核心库 (`pkg/`)

`pkg/` 目录包含了项目级的可共享公共库，旨在提高代码复用性和维护性。

- **[`auth/`](mdc:pkg/auth):** 提供认证相关的辅助功能，可能包含从 Context 获取用户信息等。
- **[`ubcrypt/`](mdc:pkg/ubcrypt):** 封装了 Bcrypt 密码加密和验证功能。
- **[`uconfig/`](mdc:pkg/uconfig):** 封装了基于 Viper 的配置加载逻辑，支持多文件路径、环境变量、命令行参数和默认值。
- **[`udb/`](mdc:pkg/udb):** 封装了 GORM 数据库初始化、连接管理和健康检查功能。
- **[`uerror/`](mdc:pkg/uerror):** 定义了统一的错误处理结构和辅助函数，方便错误传递和 API 响应。
- **[`ugin/`](mdc:pkg/ugin):** 提供了 Gin 框架的扩展：
    - `middleware/`: 常用的 Gin 中间件，如 CORS、Gzip 压缩、Trace 日志、参数日志、Recovery 恢复、JWT 认证等。
    - `validator/`: 集成了 `go-playground/validator`，提供结构体验证功能。
    - `writer/`: 统一的 API 响应写入器，封装了成功的 JSON 响应和错误的 JSON 响应格式。
- **[`uid/`](mdc:pkg/uid):** 封装了 [KSUID](https://github.com/segmentio/ksuid) 的生成，提供按时间排序且全局唯一的 ID。
- **[`ujwt/`](mdc:pkg/ujwt):** 封装了 JWT Token 的生成、解析和验证逻辑。
- **[`ulog/`](mdc:pkg/ulog):** 封装了 Zap 日志库和 Lumberjack 日志切割库，提供结构化日志记录和自动切割归档功能。
- **[`utwilio/`](mdc:pkg/utwilio):** (推测) 封装了与 Twilio API 交互的功能，可能用于发送 SMS 验证码或其他通知。
- **[`util/`](mdc:pkg/util):** 包含一些通用的工具函数。
- **[`version/`](mdc:pkg/version):** 管理应用的版本信息（Version, GitCommit, BuildDate），这些信息在编译时通过 `makefile` 自动注入。

## 开发指南

### 代码规范

- **格式化:** 遵循标准的 Go `fmt` 格式。推荐使用 IDE 的自动格式化功能。
- **静态检查:** 推荐使用 `golangci-lint` 进行代码质量检查。可以在本地运行：
  ```bash
  golangci-lint run ./...
  ```
  (CI 流程中已包含此步骤)
- **项目结构:** 遵循标准的 Go 项目布局（如本文档 [项目结构](#项目结构) 部分所述）。
- **错误处理:** 使用 `pkg/uerror` 提供的机制进行错误处理和传递。
- **日志记录:** 使用 `pkg/ulog` 进行结构化日志记录。
- **命名规范:** 遵循 Go 社区的通用命名规范。

### 测试

- **单元测试:** 所有新功能和重要的业务逻辑都应包含单元测试。测试文件应与被测试的源文件放在同一目录下，并以 `_test.go` 结尾。
- **运行测试:** 使用 `make` 命令运行所有测试：
  ```bash
  make test
  ```
  该命令会包含 `-race` 标志以进行竞态检测，并生成覆盖率报告 `coverage.out`。

### 版本管理与发布

- **版本号:** 使用 [Semantic Versioning](https://semver.org/) (语义化版本) `vX.Y.Z`。
- **发布流程:**
    1.  确认 `dev` 分支代码稳定并通过所有测试。
    2.  合并 `dev` 分支到 `main` 分支（或发布分支）。
    3.  在 `main` 分支上打上新的版本标签：
        ```bash
        git tag vX.Y.Z
        ```
    4.  推送代码和标签到远程仓库：
        ```bash
        git push origin main && git push origin vX.Y.Z
        ```
- **版本注入:** `makefile` 在执行 `make webapp` 或 CI 构建时，会自动获取最新的 Git Tag 作为版本号，连同 Git Commit Hash 和构建日期一起注入到 `pkg/version` 包中，可通过 `/version` API 端点查询。

## 未来规划

- **添加 Booking 应用:** 实现具体的预约业务逻辑。
- **完善 RBAC:** 完成基于角色的访问控制功能 (相关数据库表已设计)。
- **引入服务发现:** (如 Consul, etcd) 以支持未来可能的微服务架构。
- **增加监控和告警:** 集成 Prometheus, Grafana , Netdata等监控系统，并设置告警规则。
- **API 文档:** (已部分实现) 使用 Swagger/OpenAPI 规范自动生成和展示 API 文档 (当前通过 `api/webapp/` 目录下的文件实现)。
- **提高测试覆盖率:** 增加集成测试和端到端测试。
- **国际化支持:** 为应用添加多语言支持。

## 许可证

[待定] - 请根据项目需求选择合适的开源许可证。

## 联系方式

[待定] - 请添加项目维护者的联系方式或社区链接。
