### **Pebble 项目报表模块 API 设计文档 (v1.0)**

#### 1. 设计哲学与核心思路

本报表模块的设计遵循以下四大核心原则，旨在实现极致的**前后端解耦**、**灵活性**和**可维护性**。

##### 1.1. 后端驱动的UI (Backend-Driven UI)
前端的报表导航菜单、报表页面的具体形态（是表格还是图表）完全由后端接口动态下发。前端不硬编码任何与报表分类、形态相关的业务逻辑。

*   **优势**：当需要新增报表、调整报表分类、或将某个报表从表格切换为图表时，只需修改后端逻辑与配置，前端无需重新开发和发布，极大提升了业务迭代速度和灵活性。

##### 1.2. 表现与数据分离 (Separation of Presentation and Data)
后端接口返回的数据被明确地划分为两部分：
*   **纯净的数据 (`data`)**：未经加工的、结构化的数据记录。
*   **元数据 (`metadata`)**：一份"说明书"，详细描述了应如何渲染这些纯净数据，包括它们的显示名称、数据类型、UI建议等。

*   **优势**：后端专注于数据计算与业务逻辑，前端专注于可视化展示。前端可以开发出高度通用的渲染组件（如通用表格、通用图表），根据元数据来动态渲染，极大减少了重复的UI开发工作。

##### 1.3. 权限内置于设计 (Permissions-by-Design)
用户能看到哪些报表、报表里的哪些数据，完全由后端在数据生成时根据用户权限进行过滤。前端无需编写任何权限判断代码。

*   **优势**：权限逻辑收敛在后端，是唯一可信源（Single Source of Truth），保证了系统的安全性，避免了在多端（Web, App）重复实现权限逻辑，降低了维护成本和出错风险。

##### 1.4. 统一与泛化 (Consistency and Generalization)
所有报表接口都遵循一套统一的顶层数据结构。通过元数据的不同，来适配各种具体报表的需求。

*   **优势**：前端可以用一套统一的逻辑来请求和解析所有报表数据，降低了学习成本和代码复杂度。

---

### 2. 核心接口设计

#### 2.1. 获取报表导航菜单接口

此接口用于动态构建前端的报表中心左侧导航栏。

*   **Endpoint**: `GET /api/v1/reports/navigation`
*   **功能描述**: 返回当前登录用户有权查看的所有报表的分类、名称和描述。
*   **权限要求**: 已登录用户 (`@LoginRequired`)。
*   **成功响应 (200 OK)**:
    ```json
    {
        "categories": [
            {
                "group_name": "财务报表",
                "items": [
                    {
                        "key": "business_summary",
                        "name": "营业汇总",
                        "description": "查看收入、小费和利润的核心指标。"
                    },
                    {
                        "key": "payment_methods",
                        "name": "支付方式统计",
                        "description": "按信用卡、现金等支付方式分析营业额。"
                    }
                ]
            },
            {
                "group_name": "员工业绩",
                "items": [
                    {
                        "key": "staff_performance",
                        "name": "员工业绩报表",
                        "description": "统计每位员工的服务数量和相关收入。"
                    }
                ]
            }
        ]
    }
    ```
*   **字段详解**:
    *   `categories` (`array`): 报表的分组列表。
    *   `group_name` (`string`): 分组的显示名称，如"财务报表"。
    *   `items` (`array`): 该分组下的报表项列表。
    *   `key` (`string`): 报表的唯一标识符。**前端将使用此 `key` 去请求具体的报表数据**。
    *   `name` (`string`): 报表的显示名称。
    *   `description` (`string`): 报表的简短描述，可用于鼠标悬浮提示。

#### 2.2. 获取具体报表数据接口

这是一个泛型接口，用于获取指定 `key` 的报表数据。

*   **Endpoint**: `GET /api/v1/reports/{reportKey}`
*   **URL 参数**:
    *   `reportKey` (`string`): **必须**。报表的唯一标识符，来源于导航接口返回的 `key`。
*   **Query 参数**:
    *   `start_date` (`string`): **必须**。格式 `YYYY-MM-DD`。
    *   `end_date` (`string`): **必须**。格式 `YYYY-MM-DD`。
    *   `location_id` (`string`): 可选。按单个地点筛选。
    *   *其他自定义筛选参数*...
*   **功能描述**: 根据报表 `key` 和筛选参数，返回计算好的报表数据及其渲染元数据。
*   **权限要求**: 接口内部会根据 `reportKey` 和用户权限进行二次校验。
*   **成功响应 (200 OK)**:
    ```json
    // 通用顶层结构
    {
        "summary": { ... },
        "metadata": { ... },
        "data": [ ... ] or { ... }
    }
    ```
*   **字段详解**:
    *   `summary` (`object`, 可选): 报表的顶层关键指标汇总。一个键值对对象。
    *   `metadata` (`object`): **渲染元数据**，是指导前端如何展示的核心。详见第3节。
    *   `data` (`array` 或 `object`): **核心数据**。其结构由 `metadata` 决定，通常是对象数组。

---

### 3. 元数据 (`metadata`) 结构详解

`metadata` 对象的核心是 `type` 字段，它告诉前端应使用哪种渲染器。

#### 3.1. 指标卡组 (`type: "kpi_deck"`)
*   **用途**: 在报表顶部展示一组醒目的核心数据。
*   **结构**:
    ```json
    "metadata": {
        "type": "kpi_deck",
        "cards": [
            {
                "key": "total_revenue",
                "name": "总营业额",
                "type": "currency"
            }
        ]
    },
    "data": { // 注意：data 是单个对象
        "total_revenue": 152030.50
    }
    ```
*   **字段**:
    *   `cards` (`array`): 定义每一张卡片。
    *   `key` (`string`): 对应 `data` 对象中的键。
    *   `name` (`string`): 卡片标题。
    *   `type` (`string`): 数据格式，详见附录。

#### 3.2. 表格 (`type: "table"`)
*   **用途**: 展示结构化的行和列数据。
*   **结构**:
    ```json
    "metadata": {
        "type": "table",
        "columns": [
            {
                "key": "staff_name",
                "name": "员工姓名",
                "type": "string",
                "sortable": true,
                "align": "left"
            }
        ]
    },
    "data": [ // 注意：data 是对象数组
        { "staff_name": "张三" }
    ]
    ```
*   **字段**:
    *   `columns` (`array`): 定义每一列。
    *   `key` (`string`): 列ID，对应 `data` 数组中每个对象的键。
    *   `name` (`string`): 列头的显示名称。
    *   `type` (`string`): 列中单元格的数据格式。
    *   `sortable` (`boolean`): 前端是否应允许按此列排序。
    *   `align` (`string`): 单元格内容对齐建议 (`left`, `center`, `right`)。

#### 3.3. 图表 (`type: "chart"`)
*   **用途**: 展示数据趋势，如折线图、柱状图。
*   **结构**:
    ```json
    "metadata": {
        "type": "chart",
        "xAxisKey": "date",
        "series": [
            { "key": "revenue", "name": "总营业额", "type": "currency" },
            { "key": "net_revenue", "name": "净收入", "type": "currency" }
        ]
    },
    "data": [ // 注意：data 是对象数组
        { "date": "2024-07-01", "revenue": 500, "net_revenue": 450 }
    ]
    ```
*   **字段**:
    *   `xAxisKey` (`string`): 指定 `data` 中哪个键作为图表的X轴。
    *   `series` (`array`): 定义图表中的每一条数据系列（如每一条折线）。
        *   `key`: 系列ID，对应 `data` 中每个对象的键。
        *   `name`: 系列名称，用于图例。
        *   `type`: 数据格式，用于悬浮提示。

#### 3.4. 饼图/环形图 (`type: "pie_chart"`)
*   **用途**: 展示各部分占整体的比例。
*   **结构**:
    ```json
    "metadata": {
        "type": "pie_chart",
        "labelKey": "method_name",
        "valueKey": "total_amount",
        "valueType": "currency"
    },
    "data": [ // 注意：data 是对象数组
        { "method_name": "信用卡", "total_amount": 75000 }
    ]
    ```
*   **字段**:
    *   `labelKey` (`string`): `data` 中作为扇区标签的键。
    *   `valueKey` (`string`): `data` 中作为扇区数值的键。
    *   `valueType` (`string`): 数值的格式。

---

### 4. 附录：通用数据类型 (`type` 字段)

以下是推荐在 `metadata` 中使用的标准数据类型字符串，以便前端进行统一的格式化处理。

| `type` 值 | 含义 | 示例 |
| :--- | :--- | :--- |
| `"string"` | 普通文本 | `张三` |
| `"integer"` | 整数 | `1,234` |
| `"decimal"` | 小数 | `12.34` |
| `"currency"` | 货币 | `¥1,234.56` |
| `"percentage"` | 百分比 | `75.20%` |
| `"date"` | 日期 | `2024-07-28` |
| `"datetime"`| 日期时间 | `2024-07-28 15:30` |

---

### 5. 总结与实践建议

本设计文档提供了一套完整、可扩展的报表API框架。

*   **后端开发**: 核心工作是实现 `GET /api/v1/reports/{reportKey}` 接口。针对不同的 `reportKey`，编写相应的SQL查询，组装 `data` 和 `metadata`，并确保权限过滤正确。
*   **前端开发**: 核心工作是开发可复用的渲染组件。
    1.  一个 `ReportContainer` 组件，负责调用API，并根据 `metadata.type` 来决定渲染哪个子组件。
    2.  一个 `KpiDeckRenderer` 组件。
    3.  一个 `TableRenderer` 组件。
    4.  一个 `ChartRenderer` 组件等等。

通过严格遵守这套设计，团队可以实现高效的并行开发，并构建一个健壮、灵活、易于长期维护的商业智能报表系统。

---

### 6. 高级场景：组合式报表 (Dashboard)

真实世界的报表往往不是单一的表格或图表，而是如下图所示，在一个页面内组合了多个信息区块的"仪表盘"或"日报"。

*(此处可以插入用户提供的图片)*

这种需求不能通过特殊接口来"写死"，而应该通过扩展我们现有的元数据模型来优雅地实现。

#### 6.1. 设计思路：引入 "layout" 类型

我们引入一种新的顶层 `metadata.type`，称为 `"layout"`。当 `type` 为 `"layout"` 时，它本身不代表一个图表，而是一个**可以容纳其他报表组件的"容器"**。它的 `items` 数组中，每一项都是一个独立的、拥有完整 `metadata` 和 `data` 的报表"区块(Widget)"。

#### 6.2. 新的元数据类型

##### 6.2.1. 布局容器 (`type: "layout"`)
*   **用途**: 作为根容器，定义一个由多个区块组成的页面。
*   **结构**:
    ```json
    "metadata": {
        "type": "layout",
        "title": "Daily Report", // 整个页面的标题
        "items": [
            // ... 此处填充多个独立的报表区块 ...
        ]
    }
    ```

##### 6.2.2. 定义列表 (`type: "definition_list"`)
*   **用途**: 用于展示如"信用卡明细"这类"标签: 值"格式的列表，比用表格更具语义。
*   **结构**:
    ```json
    "metadata": {
        "type": "definition_list",
        "items": [
            { "key": "service_revenue", "name": "Service Revenue", "type": "currency" },
            { "key": "tips", "name": "Tips", "type": "currency" }
        ]
    },
    "data": { // data 是一个对象
        "service_revenue": 150,
        "tips": 10
    }
    ```
*   **字段**:
    *   `items` (`array`): 定义了列表中每一行的顺序、显示名称(`name`)、数据键(`key`)和格式(`type`)。

#### 6.3. 组合日报示例

以下是如何用扩展后的模型来完整描述图中日报的API响应：

```json
{
    "metadata": {
        "type": "layout",
        "title": "Daily Report",
        "items": [
            {
                "title": "Service Sales by Staff",
                "metadata": { "type": "table", "columns": [...] },
                "data": [ ... ],
                "summary": { "total_sales": 900 }
            },
            {
                "title": "Sales by Payment Method (Excludes Tips)",
                "metadata": { "type": "table", "columns": [...] },
                "data": [ ... ],
                "summary": { "total_amount": 900 }
            },
            {
                "title": "Total Credit Card Breakdown",
                "metadata": { "type": "definition_list", "items": [...] },
                "data": { ... },
                "summary": { "total_collected_on_card": 165 }
            }
        ]
    }
}
```

#### 6.4. 前后端协作流程

1.  **前端**请求数据，检查到顶层 `metadata.type` 为 `"layout"`。
2.  前端渲染一个总容器和标题。
3.  前端遍历 `metadata.items` 数组。
4.  对数组中的每个`item`，前端将其视为一个独立的报表组件，递归地调用其渲染逻辑：读取该 `item` 的 `title`, `metadata`, `data`, `summary`，并根据其 `metadata.type` (如 `"table"`, `"definition_list"`) 来选择对应的渲染器。

通过这种"组合"与"递归"模式，我们可以用一套统一的、可预测的规则来构建任意复杂的报表页面，实现了最终极的灵活性。 



### 7. Golang 实现方式与技巧

本章节提供将上述设计在 Go 项目中落地的具体代码结构、模式和技巧。

#### 7.1. 核心数据结构定义 (`types/report.go`)

所有报表相关的Go数据结构都应定义在此文件中。这是连接业务逻辑和JSON响应的桥梁。

```go
package types

// ColumnType is an enumeration of semantic data types for report columns.
type ColumnType string

const (
	ColumnTypeString     ColumnType = "string"
	ColumnTypeInteger    ColumnType = "integer"
	ColumnTypeDecimal    ColumnType = "decimal"
	ColumnTypeCurrency   ColumnType = "currency"
	ColumnTypePercentage ColumnType = "percentage"
	ColumnTypeDate       ColumnType = "date"
	ColumnTypeDateTime   ColumnType = "datetime"
)

// GenericReportResponse is the universal container for all single report API responses.
type GenericReportResponse struct {
	Summary  map[string]interface{} `json:"summary,omitempty"`
	Metadata interface{}            `json:"metadata"`
	Data     interface{}            `json:"data"` // Use interface{} to allow both map and slice
}

// --- Metadata for Layout/Dashboard ---

// LayoutMetadata defines a dashboard-style report composed of multiple widgets.
type LayoutMetadata struct {
	Type  string         `json:"type"` // Should be "layout"
	Title string         `json:"title"`
	Items []ReportWidget `json:"items"`
}

// ReportWidget represents a single component within a layout.
// It's a recursive structure, containing a full report response.
type ReportWidget struct {
	Title    string                 `json:"title"`
	Summary  map[string]interface{} `json:"summary,omitempty"`
	Metadata interface{}            `json:"metadata"`
	Data     interface{}            `json:"data"`
}

// --- Metadata for Specific Widget Types ---

// TableMetadata defines metadata for a table.
type TableMetadata struct {
	Type    string         `json:"type"` // Should be "table"
	Columns []ReportColumn `json:"columns"`
}

type ReportColumn struct {
	Key       string     `json:"key"`
	Name      string     `json:"name"`
	Type      ColumnType `json:"type"`
	Sortable  bool       `json:"sortable,omitempty"`
	Align     string     `json:"align,omitempty"`
	Precision int        `json:"precision,omitempty"`
}

// ChartMetadata defines metadata for charts (line, bar, etc.).
type ChartMetadata struct {
	Type     string        `json:"type"` // Should be "chart"
	XAxisKey string        `json:"xAxisKey"`
	Series   []ChartSeries `json:"series"`
}

type ChartSeries struct {
	Key  string     `json:"key"`
	Name string     `json:"name"`
	Type ColumnType `json:"type"`
}

// PieChartMetadata defines metadata for pie or donut charts.
type PieChartMetadata struct {
	Type      string     `json:"type"` // Should be "pie_chart"
	LabelKey  string     `json:"labelKey"`
	ValueKey  string     `json:"valueKey"`
	ValueType ColumnType `json:"valueType"`
}

// DefinitionListMetadata defines metadata for a key-value list.
type DefinitionListMetadata struct {
	Type  string           `json:"type"` // Should be "definition_list"
	Items []DefinitionItem `json:"items"`
}

type DefinitionItem struct {
	Key  string     `json:"key"`
	Name string     `json:"name"`
	Type ColumnType `json:"type"`
}

// KpiDeckMetadata defines metadata for a set of KPI cards.
type KpiDeckMetadata struct {
    Type  string    `json:"type"` // Should be "kpi_deck"
    Cards []KpiCard `json:"cards"`
}

type KpiCard struct {
    Key  string     `json:"key"`
    Name string     `json:"name"`
    Type ColumnType `json:"type"`
}
```

#### 7.2. 项目文件结构

建议在 `internal/webapp` 目录下，按照现有分层结构创建相关文件：

```
internal/webapp/
├── handler/
│   └── report.go         // (新增) 报表API的Handler
├── services/
│   └── report.go         // (新增) 报表核心业务逻辑
├── models/
│   └── report.go         // (新增) 报表数据层接口
│   └── sqlserver/
│       └── report.go     // (新增) 报表SQL查询实现
└── types/
    └── report.go         // (新增) 所有报表相关的Go数据结构定义
```

#### 7.3. Handler 层实现

有了服务层的支持，Handler 的逻辑变得非常简洁。

```go
// handler/report.go

func (h *ReportHandler) GetReport(c *gin.Context) {
	reportKey := c.Param("reportKey")
	
	var query types.ReportQuery
	if err := c.ShouldBindQuery(&query); err != nil {
		// 返回参数错误
		return
	}
	
	// 权限校验可以在这里做，也可以在Service层做
	// ugin.CheckPermission(...)
	
	response, err := h.reportSvc.GenerateReport(c.Request.Context(), reportKey, &query)
	if err != nil {
		// 处理错误
		return
	}
	
	ugin.Write(c, response)
}
```

#### 7.4. 数据层技巧

*   **避免过度ORM**：报表查询通常涉及复杂的 `JOIN`, `GROUP BY`, `SUM`, `COUNT` 等聚合操作。直接使用 ORM 的 `Find` 或 `First` 可能难以实现，或者性能低下。强烈建议在 `models/sqlserver/report.go` 中**手写原生SQL**。
*   **直接扫描到Map**：为了实现 `data` 字段 `[]map[string]interface{}` 的灵活性，我们通常需要将数据库查询结果转换为 map。与其先扫描到 struct 再转换为 map，不如直接扫描到 map，以提升性能。很多数据库驱动（如 `sqlx`）都支持此功能。

    **使用 `sqlx` 的示例**:
    ```go
    // models/sqlserver/report.go
    
    func (s *store) GetStaffPerformanceData(...) ([]map[string]interface{}, error) {
        rows, err := s.db.QueryxContext(ctx, "SELECT staff_name, SUM(revenue) as revenue FROM ...")
        if err != nil {
            return nil, err
        }
        defer rows.Close()

        results := make([]map[string]interface{}, 0)
        for rows.Next() {
            rowMap := make(map[string]interface{})
            if err := rows.MapScan(rowMap); err != nil {
                return nil, err
            }
            results = append(results, rowMap)
        }
        return results, nil
    }
    ```
    这个技巧避免了中间的 `struct` 转换，代码更简洁，性能也更好。

---