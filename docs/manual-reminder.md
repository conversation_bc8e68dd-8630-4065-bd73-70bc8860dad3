# Manual Reminder Service

手动触发Reminder消息预览和发送功能的实现文档。

## 功能概述

这个服务提供了手动预览和发送reminder消息的功能，支持：

1. **预约提醒 (Appointment Reminder)** - 基于appointment_id预览和发送
2. **重新预约提醒 (Rebook Reminder)** - 基于client_id预览和发送
3. **扩展性设计** - 支持未来添加新的reminder类型

## API接口

### 1. 预览Reminder消息

**接口**: `POST /api/v1/reminders/preview`

**Appointment Reminder请求示例**:
```json
{
  "template_type": "appointment_reminder",
  "appointment_id": "appt_12345"
}
```

**Rebook Reminder请求示例**:
```json
{
  "template_type": "rebook_appointment",
  "client_id": "client_12345"
}
```

**响应示例**:
```json
{
  "content": "Hi <PERSON>, this is a reminder for your appointment on 2025-01-20 at 2:00 PM.",
  "variables": {
    "client_full_name": "<PERSON>",
    "appointment_date": "2025-01-20",
    "appointment_time": "2:00 PM"
  },
  "template_type": "appointment_reminder",
  "appointment_id": "appt_12345",
  "client_phone": "+1234567890",
  "client_email": "<EMAIL>"
}
```

### 2. 发送Reminder消息

**接口**: `POST /api/v1/reminders/send`

**请求示例**:
```json
{
  "template_type": "appointment_reminder",
  "content": "Hi John Doe, this is a reminder for your appointment on 2025-01-20 at 2:00 PM.",
  "phone": "+1234567890",
  "appointment_id": "appt_12345"
}-
```

**响应示例**:
```json
{
  "success": true,
  "message_id": "sms_msg_12345"
}
```

### 3. 获取支持的Reminder类型

**接口**: `GET /api/v1/reminders/types`

**响应示例**:
```json
{
  "supported_types": [
    "appointment_reminder",
    "rebook_appointment"
  ]
}
```

## 技术架构

### 核心组件

1. **ReminderProcessor接口** - 定义每种reminder类型的处理逻辑
2. **ReminderFactory** - 工厂模式创建不同类型的processor
3. **ManualReminderService** - 统一的服务入口
4. **ManualReminderHandler** - API请求处理器

### 代码复用

- **最大化复用现有逻辑**: 从`appointment_reminder_service.go`和`rebook_appointment_service.go`提取核心逻辑
- **防重复发送**: 继承现有的`notification_logs`机制
- **时区处理**: 复用现有的时区转换逻辑
- **模板渲染**: 使用现有的模板变量系统

### 扩展性

添加新的reminder类型只需要：

1. 在`message/message_template.go`中添加新的`TemplateType`
2. 实现新的processor（如`BirthdayReminderProcessor`）
3. 在factory中注册新类型
4. 更新默认模板和变量定义

## 使用示例

### 预览appointment reminder

```bash
curl -X POST http://localhost:8080/api/v1/reminders/preview \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -d '{
    "template_type": "appointment_reminder",
    "appointment_id": "appt_12345"
  }'
```

### 发送reminder消息

```bash
curl -X POST http://localhost:8080/api/v1/reminders/send \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -d '{
    "template_type": "appointment_reminder",
    "content": "Hi John, your appointment is tomorrow at 2 PM.",
    "phone": "+1234567890",
    "appointment_id": "appt_12345"
  }'
```

## 安全和权限

- 所有API接口都需要JWT认证
- 自动根据认证上下文获取tenant和location信息
- 只能操作当前用户权限范围内的数据
- 防重复发送机制防止误操作

## 错误处理

常见错误码：
- `40003` - 参数无效（如缺少required字段）
- `40602` - 预约未找到
- `40500` - 客户未找到
- SMS发送失败会返回具体的错误信息

## 测试

运行测试：
```bash
go test ./internal/webapp/services/reminder/ -v
```

编译检查：
```bash
go build ./cmd/webapp
```