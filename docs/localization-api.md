# 本地化 API 接口文档

本文档描述了用于获取货币、国家和语言信息的公共 API 接口。这些接口不需要身份验证，可以直接调用。

## 接口列表

### 1. 获取完整本地化数据
**接口地址**: `GET /localization`  
**描述**: 获取所有可用的货币、国家和语言信息  
**认证**: 无需认证  

**响应示例**:
```json
{
  "success": true,
  "data": {
    "obj": {
      "currencies": [
        {
          "code": "USD",
          "symbol": "$",
          "name": "US Dollar"
        },
        {
          "code": "EUR",
          "symbol": "€",
          "name": "Euro"
        }
      ],
      "countries": [
        {
          "code": "US",
          "name": "United States",
          "currency_code": "USD",
          "currency": {
            "code": "USD",
            "symbol": "$",
            "name": "US Dollar"
          }
        }
      ],
      "languages": [
        {
          "code": "en",
          "name": "English"
        },
        {
          "code": "zh",
          "name": "Chinese"
        }
      ]
    }
  },
  "message": "",
  "code": 0
}
```

### 2. 获取主要本地化数据
**接口地址**: `GET /localization/major`  
**描述**: 获取常用的货币、国家和语言信息（数据量较少，适合快速加载）  
**认证**: 无需认证  

**响应格式**: 与完整本地化数据相同，但只包含主要的货币、国家和语言

### 3. 获取所有货币
**接口地址**: `GET /currencies`  
**描述**: 获取所有可用货币信息  
**认证**: 无需认证  

**响应示例**:
```json
{
  "success": true,
  "data": {
    "list": [
      {
        "code": "USD",
        "symbol": "$",
        "name": "US Dollar"
      },
      {
        "code": "EUR",
        "symbol": "€",
        "name": "Euro"
      }
    ]
  },
  "message": "",
  "code": 0
}
```

### 4. 获取所有国家
**接口地址**: `GET /countries`  
**描述**: 获取所有可用国家信息及其对应货币  
**认证**: 无需认证  

**响应示例**:
```json
{
  "success": true,
  "data": {
    "list": [
      {
        "code": "US",
        "name": "United States",
        "currency_code": "USD",
        "currency": {
          "code": "USD",
          "symbol": "$",
          "name": "US Dollar"
        }
      }
    ]
  },
  "message": "",
  "code": 0
}
```

### 5. 获取所有语言
**接口地址**: `GET /languages`  
**描述**: 获取所有可用语言信息  
**认证**: 无需认证  

**响应示例**:
```json
{
  "success": true,
  "data": {
    "list": [
      {
        "code": "en",
        "name": "English"
      },
      {
        "code": "zh",
        "name": "Chinese"
      }
    ]
  },
  "message": "",
  "code": 0
}
```

## 数据结构说明

### Currency (货币)
| 字段 | 类型 | 描述 | 示例 |
|------|------|------|------|
| code | string | ISO 4217 货币代码 | "USD" |
| symbol | string | 货币符号 | "$" |
| name | string | 货币全名 | "US Dollar" |

### Country (国家)
| 字段 | 类型 | 描述 | 示例 |
|------|------|------|------|
| code | string | ISO 3166-1 alpha-2 国家代码 | "US" |
| name | string | 国家名称 | "United States" |
| currency_code | string | 该国使用的货币代码 | "USD" |
| currency | Currency | 货币详细信息 | Currency 对象 |

### Language (语言)
| 字段 | 类型 | 描述 | 示例 |
|------|------|------|------|
| code | string | ISO 639-1 语言代码 | "en" |
| name | string | 语言名称 | "English" |

## 支持的数据范围

### 主要货币 (Major Currencies)
- USD ($) - 美元
- EUR (€) - 欧元
- GBP (£) - 英镑
- JPY (¥) - 日元
- CNY (¥) - 人民币
- CAD (C$) - 加拿大元
- AUD (A$) - 澳大利亚元
- CHF (CHF) - 瑞士法郎
- SEK (kr) - 瑞典克朗
- NOK (kr) - 挪威克朗
- DKK (kr) - 丹麦克朗

### 地区覆盖
- **北美**: 美国、加拿大、墨西哥
- **欧洲**: 所有欧盟国家及英国、瑞士、挪威等
- **亚洲**: 中国、日本、印度、东南亚、中亚
- **中东**: 阿联酋、沙特阿拉伯、卡塔尔、科威特等
- **大洋洲**: 澳大利亚、新西兰、太平洋岛屿
- **南美**: 巴西、阿根廷、智利、哥伦比亚、秘鲁
- **非洲**: 南非、埃及

### 主要语言 (Major Languages)
- en - English (英语)
- zh - Chinese (中文)
- es - Spanish (西班牙语)
- hi - Hindi (印地语)
- ar - Arabic (阿拉伯语)
- pt - Portuguese (葡萄牙语)
- bn - Bengali (孟加拉语)
- ru - Russian (俄语)
- ja - Japanese (日语)
- de - German (德语)
- fr - French (法语)
- ko - Korean (韩语)
- it - Italian (意大利语)
- tr - Turkish (土耳其语)
- th - Thai (泰语)

## 使用场景

这些接口适用于以下场景：

1. **电商平台**: 多货币支持和国际化
2. **金融应用**: 货币转换和显示
3. **国际服务**: 本地化和地区设置
4. **支付系统**: 货币验证和格式化
5. **旅行应用**: 国家和货币信息
6. **多语言应用**: 语言选择和本地化

## 错误响应

当发生错误时，所有接口都会返回统一的错误格式：

```json
{
  "success": false,
  "data": {},
  "message": "错误描述",
  "code": 错误代码
}
```

## 性能建议

1. **缓存数据**: 建议前端缓存这些数据，避免频繁请求
2. **选择合适的接口**: 
   - 如果只需要主要货币和国家，使用 `/localization/major`
   - 如果需要完整数据，使用 `/localization`
   - 如果只需要特定类型数据，使用对应的单独接口
3. **数据更新**: 这些数据相对稳定，建议设置较长的缓存时间（如24小时）
