# Pebble 项目报表模块 - 代码执行计划 (v1.0)

本文档旨在将《报表模块 API 设计文档》拆解为一系列具体、可执行的编码任务。开发人员或 AI 助手 (Cursor) 可遵循此计划，一步步完成整个功能的开发。

## 第一阶段：奠定基础 - 定义数据结构与权限

此阶段不涉及业务逻辑，但为后续所有开发工作提供数据契约和安全基础。

### 任务 1.1: 创建核心数据结构

*   **目标**: 定义所有报表相关的 Go struct。
*   **文件**: `internal/webapp/types/report.go` (新建)
*   **操作**: 在该文件中，添加以下所有 Go 代码。这些代码完整定义了报表API的请求、响应及所有元数据结构。

```go
package types

// ReportQuery defines the common query parameters for report endpoints.
type ReportQuery struct {
	StartDate   string `form:"start_date" binding:"required,datetime=2006-01-02"`
	EndDate     string `form:"end_date" binding:"required,datetime=2006-01-02"`
	LocationID  string `form:"location_id"`
}

// ColumnType is an enumeration of semantic data types for report columns.
type ColumnType string

const (
	ColumnTypeString     ColumnType = "string"
	ColumnTypeInteger    ColumnType = "integer"
	ColumnTypeDecimal    ColumnType = "decimal"
	ColumnTypeCurrency   ColumnType = "currency"
	ColumnTypePercentage ColumnType = "percentage"
	ColumnTypeDate       ColumnType = "date"
	ColumnTypeDateTime   ColumnType = "datetime"
)

// GenericReportResponse is the universal container for all single report API responses.
type GenericReportResponse struct {
	Summary  map[string]interface{} `json:"summary,omitempty"`
	Metadata interface{}            `json:"metadata"`
	Data     interface{}            `json:"data"` // Use interface{} to allow both map and slice
}

// --- Metadata for Layout/Dashboard ---

// LayoutMetadata defines a dashboard-style report composed of multiple widgets.
type LayoutMetadata struct {
	Type  string         `json:"type"` // Should be "layout"
	Title string         `json:"title"`
	Items []ReportWidget `json:"items"`
}

// ReportWidget represents a single component within a layout.
type ReportWidget struct {
	Title    string                 `json:"title"`
	Summary  map[string]interface{} `json:"summary,omitempty"`
	Metadata interface{}            `json:"metadata"`
	Data     interface{}            `json:"data"`
}

// --- Metadata for Specific Widget Types ---

// TableMetadata defines metadata for a table.
type TableMetadata struct {
	Type    string         `json:"type"` // Should be "table"
	Columns []ReportColumn `json:"columns"`
}

type ReportColumn struct {
	Key       string     `json:"key"`
	Name      string     `json:"name"`
	Type      ColumnType `json:"type"`
	Sortable  bool       `json:"sortable,omitempty"`
	Align     string     `json:"align,omitempty"`
	Precision int        `json:"precision,omitempty"`
}

// ChartMetadata defines metadata for charts (line, bar, etc.).
type ChartMetadata struct {
	Type     string        `json:"type"` // Should be "chart"
	XAxisKey string        `json:"xAxisKey"`
	Series   []ChartSeries `json:"series"`
}

type ChartSeries struct {
	Key  string     `json:"key"`
	Name string     `json:"name"`
	Type ColumnType `json:"type"`
}

// DefinitionListMetadata defines metadata for a key-value list.
type DefinitionListMetadata struct {
	Type  string           `json:"type"` // Should be "definition_list"
	Items []DefinitionItem `json:"items"`
}

type DefinitionItem struct {
	Key  string     `json:"key"`
	Name string     `json:"name"`
	Type ColumnType `json:"type"`
}
```

### 任务 1.2: 定义并注册报表权限

*   **目标**: 在系统中定义报表相关的权限，并准备数据库迁移脚本。
*   **文件**: `internal/webapp/types/permissions.go` (或类似文件)
*   **操作**:
    1.  在该文件中，新增以下权限常量：
        ```go
        // Add inside the const block
        PermissionReportsBusinessSummaryView   = "reports:business_summary:view"
        PermissionReportsStaffPerformanceView  = "reports:staff_performance:view"
        PermissionReportsPaymentMethodsView    = "reports:payment_methods:view"
        ```
    2.  创建一个新的数据库迁移文件，用于将以上权限插入到 `permissions` 表中。

---

## 第二阶段：实现数据和服务层

此阶段专注于实现获取和处理报表数据的核心逻辑。

### 任务 2.1: 定义数据层接口

*   **目标**: 定义报表数据访问的接口。
*   **文件**: `internal/webapp/models/report.go` (新建)
*   **操作**:
    1.  创建文件并添加以下代码，定义 `ReportStore` 接口。
    ```go
    package models

    import (
    	"context"
    	"pebble/internal/webapp/types"
    )

    type ReportStore interface {
    	// Example: Define methods for each report's data needs
    	GetStaffPerformanceData(ctx context.Context, query *types.ReportQuery) ([]map[string]interface{}, error)
    	GetPaymentMethodsData(ctx context.Context, query *types.ReportQuery) ([]map[string]interface{}, error)
    }
    ```
    2.  打开 `internal/webapp/models/store.go`，在 `Store` 接口中嵌入 `ReportStore`。
    ```go
    // In internal/webapp/models/store.go
    type Store interface {
        // ... other stores
        ReportStore
    }
    ```

### 任务 2.2: 实现数据层 SQL 查询

*   **目标**: 编写获取报表数据的原生 SQL。
*   **文件**: `internal/webapp/models/sqlserver/report.go` (新建)
*   **操作**: 创建文件并为 `ReportStore` 接口中的每个方法提供 SQL 实现。

```go
package sqlserver

import (
	"context"
	"pebble/internal/webapp/types"
)

// GetStaffPerformanceData retrieves sales data grouped by staff.
func (s *store) GetStaffPerformanceData(ctx context.Context, query *types.ReportQuery) ([]map[string]interface{}, error) {
	// SQL to be implemented. Should join payments and staff tables,
	// filter by date and location, and group by staff.
	const sql = `
		SELECT
			s.first_name + ' ' + s.last_name AS staff_name,
			SUM(p.amount) AS revenue
		FROM payments p
		JOIN staff s ON p.staff_id = s.id
		WHERE p.created_at BETWEEN @p1 AND @p2 -- and optional location_id
		GROUP BY s.first_name, s.last_name
		ORDER BY revenue DESC;
	`
	// Use s.db.QueryxContext and sqlx.MapScan to execute and return results
	// as []map[string]interface{}.
	// Placeholder:
	return nil, nil
}


// GetPaymentMethodsData retrieves sales data grouped by payment method.
func (s *store) GetPaymentMethodsData(ctx context.Context, query *types.ReportQuery) ([]map[string]interface{}, error) {
	// SQL to be implemented. Should group payments by method type.
	// Placeholder:
	return nil, nil
}
```

### 任务 2.3: 搭建报表服务与注册中心

*   **目标**: 实现 `ReportService`，使用注册模式来管理不同的报表生成器。
*   **文件**: `internal/webapp/services/report.go` (新建)
*   **操作**: 创建文件并添加以下代码。

```go
package services

import (
	"context"
	"fmt"
	"pebble/internal/webapp/models"
	"pebble/internal/webapp/types"
)

// ReportGenerator defines the interface for any specific report generator.
type ReportGenerator interface {
	Generate(ctx context.Context, query *types.ReportQuery) (*types.GenericReportResponse, error)
}

// ReportService orchestrates report generation by dispatching to registered generators.
type ReportService struct {
	generators map[string]ReportGenerator
	store      models.Store
}

func NewReportService(store models.Store) *ReportService {
	svc := &ReportService{
		generators: make(map[string]ReportGenerator),
		store:      store,
	}
	svc.registerGenerators()
	return svc
}

func (s *ReportService) register(key string, g ReportGenerator) {
	s.generators[key] = g
}

// GenerateReport is the main entry point that dispatches tasks.
func (s *ReportService) GenerateReport(ctx context.Context, key string, query *types.ReportQuery) (*types.GenericReportResponse, error) {
	generator, ok := s.generators[key]
	if !ok {
		return nil, fmt.Errorf("report with key '%s' not found", key)
	}
	return generator.Generate(ctx, query)
}

// registerGenerators initializes all report generators.
func (s *ReportService) registerGenerators() {
	// Register all generators here.
	s.register("staff_performance", NewStaffPerformanceGenerator(s.store))
	// s.register("payment_methods", NewPaymentMethodsGenerator(s.store))
}
```

### 任务 2.4: 实现第一个报表生成器

*   **目标**: 实现 `staff_performance` 报表的业务逻辑。
*   **文件**: `internal/webapp/services/report_staff_performance.go` (新建)
*   **操作**: 创建文件并添加以下代码。

```go
package services

import (
	"context"
	"pebble/internal/webapp/models"
	"pebble/internal/webapp/types"
)

type StaffPerformanceGenerator struct {
	store models.Store
}

func NewStaffPerformanceGenerator(store models.Store) *StaffPerformanceGenerator {
	return &StaffPerformanceGenerator{store: store}
}

func (g *StaffPerformanceGenerator) Generate(ctx context.Context, query *types.ReportQuery) (*types.GenericReportResponse, error) {
	// 1. Get raw data from the data layer.
	rawData, err := g.store.GetStaffPerformanceData(ctx, query)
	if err != nil {
		return nil, err
	}
	
	// 2. Define the metadata for this report.
	meta := &types.TableMetadata{
		Type: "table",
		Columns: []types.ReportColumn{
			{Key: "staff_name", Name: "Staff Name", Type: types.ColumnTypeString},
			{Key: "revenue", Name: "Revenue", Type: types.ColumnTypeCurrency, Align: "right"},
		},
	}
	
	// 3. Assemble the final response.
	return &types.GenericReportResponse{
		Metadata: meta,
		Data:     rawData,
	}, nil
}
```

---

## 第三阶段：暴露API接口

此阶段将内部服务连接到外部世界，使其能被客户端调用。

### 任务 3.1: 创建 Handler

*   **目标**: 创建处理所有报表相关 HTTP 请求的 `ReportHandler`。
*   **文件**: `internal/webapp/handler/report.go` (新建)
*   **操作**: 创建文件并添加以下代码。

```go
package handler

import (
	"pebble/internal/webapp/services"
	"pebble/internal/webapp/types"
	"pebble/pkg/ugin"
	"github.com/gin-gonic/gin"
)

type ReportHandler struct {
	reportSvc *services.ReportService
}

func NewReportHandler(reportSvc *services.ReportService) *ReportHandler {
	return &ReportHandler{reportSvc: reportSvc}
}

// GetReport handles requests for a specific report.
func (h *ReportHandler) GetReport(c *gin.Context) {
	reportKey := c.Param("reportKey")
	
	var query types.ReportQuery
	if err := c.ShouldBindQuery(&query); err != nil {
		// Replace with your actual error handling
		ugin.Write(c, ugin.NewError(400, "Invalid query parameters"))
		return
	}
	
	// Permission check should be done here or in a middleware
	// Example: ugin.CheckPermission(c, "reports:" + reportKey + ":view")
	
	response, err := h.reportSvc.GenerateReport(c.Request.Context(), reportKey, &query)
	if err != nil {
		// Replace with your actual error handling
		ugin.Write(c, ugin.NewError(500, "Failed to generate report"))
		return
	}
	
	ugin.Write(c, response)
}

// GetReportNavigation handles requests for the report navigation menu.
func (h *ReportHandler) GetReportNavigation(c *gin.Context) {
	// 1. Define the complete, static navigation structure.
	// 2. Get the current user's permissions from context.
	// 3. Filter the structure based on user permissions.
	// 4. Return the filtered structure.
	// Placeholder:
	navData := gin.H{ "message": "Navigation endpoint not implemented yet." }
	ugin.Write(c, navData)
}
```

### 任务 3.2: 注册路由

*   **目标**: 将报表 API endpoints 注册到 Gin 路由器中。
*   **文件**: `internal/webapp/router.go`
*   **操作**: 在 `NewRouter` 或类似的函数中，初始化并注册报表 handler 和路由。

```go
// In internal/webapp/router.go, inside the router setup function

// ... initialize store

// Initialize and register report service and handler
reportService := services.NewReportService(store)
reportHandler := handler.NewReportHandler(reportService)

// ...

v1 := r.Group("/api/v1")
{
    // ... other v1 routes
    
    reportGroup := v1.Group("/reports")
    // Apply JWT/Auth middleware to reportGroup here
    
    reportGroup.GET("/navigation", reportHandler.GetReportNavigation)
    reportGroup.GET("/:reportKey", reportHandler.GetReport)
}
```

此执行计划完成后，报表模块的基础框架即已搭建完毕，并实现了第一个"按员工统计"的报表。后续开发新的报表只需重复任务 2.1, 2.2, 2.4，并将其注册到服务中即可。 