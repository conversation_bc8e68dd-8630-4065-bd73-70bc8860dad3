# WebApp 短信功能技术方案

## 1. 方案概述

### 1.1 背景和目标
- 背景：租户需要绑定手机号码并发送短信
- 目标：实现多平台支持的短信发送功能，支持租户级别的号码管理和配额控制

### 1.2 解决问题
- 租户手机号码管理
- 多平台短信发送支持
- 短信发送状态跟踪
- 配额管理预留

### 1.3 预期效果
- 支持租户绑定和管理手机号码
- 支持通过不同平台发送短信
- 实时跟踪短信发送状态
- 为未来配额管理预留扩展性

## 2. 需求分析

### 2.1 功能需求
- 租户 Twilio 账号管理
- 手机号码绑定和管理
- 短信发送功能
- 短信状态回调处理
- 短信发送记录查询

### 2.2 非功能需求
- 多平台支持扩展性
- 高可用性和可靠性
- 可监控和可追踪
- 配额管理预留

## 3. 技术选型

### 3.1 技术栈
- 开发语言：Go
- 数据库：MySQL
- 缓存：Redis（用于限流和状态缓存）

### 3.2 选型理由
- Go：高性能、并发友好
- MySQL：关系型数据存储
- Redis：高性能缓存和限流

## 4. 系统架构

### 4.1 整体架构
```mermaid
graph TD
    A[WebApp] --> B[短信服务]
    B --> C[Twilio Provider]
    B --> D[其他 Provider]
    B --> E[消息队列]
    E --> F[回调处理]
    F --> G[状态更新]
```

### 4.2 核心模块
1. 账号管理模块
   - Twilio 子账号管理
   - 认证信息管理

2. 号码管理模块
   - 号码绑定
   - 号码状态管理
   - 号码查询

3. 短信发送模块
   - 发送请求处理
   - 平台适配
   - 状态跟踪

4. 回调处理模块
   - 回调接收
   - 状态更新
   - 错误处理

### 4.3 关键技术
1. 多平台适配
   - 抽象接口设计
   - 工厂模式实现
   - 配置化管理

2. 状态管理
   - 状态机设计
   - 幂等性处理
   - 异常处理

## 5. 数据设计

### 5.1 数据模型
1. 租户账号表 (twilio_accounts)
```sql
CREATE TABLE `twilio_accounts` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `tenant_id` varchar(64) NOT NULL,
  `location_id` varchar(64) NOT NULL,
  `twilio_sid` VARCHAR(64) NOT NULL COMMENT 'twilio sub-account SID',
  `auth_token` VARCHAR(64) NOT NULL COMMENT 'sub-account token',
  `friendly_name` VARCHAR(100) NOT NULL COMMENT 'sub account name',
  /* `quota_limit` int DEFAULT NULL COMMENT '',
  `quota_used` int DEFAULT 0 COMMENT '', */
  `status` tinyint NOT NULL DEFAULT '1' COMMENT '0-inactive,1-active,2-suspended',
  `messaging_service_sid` VARCHAR(64) NOT NULL COMMENT '',
  `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `deleted_at` TIMESTAMP NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_deleted_at` (`deleted_at`),
  KEY `idx_tenant_location_twilio` (`tenant_id`,`location_id`,`twilio_sid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
```

2. 租户号码配置表 (phone_configs)
```sql
CREATE TABLE `phone_configs` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `tenant_id` varchar(64) NOT NULL,
  `location_id` varchar(64) NOT NULL,
  `phone_number` varchar(20) NOT NULL DEFAULT '',
  `country` varchar(10) NOT NULL DEFAULT 'US',
  `provider_phone_sid` VARCHAR(64) NOT NULL COMMENT 'provider phone number SID',
  `provider`  VARCHAR(32) NOT NULL COMMENT 'eg:twilio、aliyun',
  `capabilities` JSON NOT NULL COMMENT '["SMS", "Voice", "MMS"]',
  `status` tinyint NOT NULL DEFAULT '1' COMMENT '0-inactive,1-active,2-suspended',
  `bind_time` TIMESTAMP NOT NULL COMMENT '',
  `expire_time` TIMESTAMP NULL COMMENT '',
  `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `deleted_at` TIMESTAMP NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_deleted_at` (`deleted_at`),
  KEY `idx_tenant_location_phone` (`tenant_id`,`location_id`,`phone_number`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
```

3. 短信记录表 (sms_message_records)
```sql
CREATE TABLE `sms_message_records` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `tenant_id` varchar(64) NOT NULL,
  `location_id` varchar(64) NOT NULL,
  `client_id` varchar(64) NOT NULL,
  `message_id` varchar(64) NOT NULL,
  `phone_number` VARCHAR(20) NOT NULL COMMENT 'Sender phone number',
  `to_number` VARCHAR(20) NOT NULL COMMENT 'Recipient phone number',
  `provider` VARCHAR(20) NOT NULL COMMENT 'Message provider',
  `msg_type` TINYINT NOT NULL COMMENT 'Message type: 1-Verification Code 2-Notification 3-Marketing',
  `content` TEXT NOT NULL COMMENT 'Message content',
  `template_id` VARCHAR(64) NULL COMMENT 'Template ID',
  `template_params` JSON NULL COMMENT 'Template parameters',
  `status` TINYINT NOT NULL COMMENT 'Status: 0-Pending 1-Sending 2-Sent Successfully 3-Sending Failed',
  `error_code` VARCHAR(50) NULL COMMENT 'Error code',
  `error_msg` VARCHAR(255) NULL COMMENT 'Error message',
  `provider_msg_id` VARCHAR(64) NULL COMMENT 'Provider message ID',
  /* `callback_time` TIMESTAMP NULL COMMENT 'Callback time', */
  `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `deleted_at` TIMESTAMP NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_deleted_at` (`deleted_at`),
  KEY `idx_tenant_location_message` (`tenant_id`,`location_id`,`message_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
```

### 5.2 数据流转
1. 号码绑定流程
```
租户配置 -> 平台购买 -> 绑定记录 -> 状态更新
```

2. 短信发送流程
```
发送请求 -> 记录创建 -> 平台发送 -> 状态更新 -> 回调处理
```

## 6. 接口设计

### 6.1 核心接口
```go
// 平台电话接口
type IPlatformPhone interface {
    BindNumber(ctx context.Context, req *BindNumberReq) (*PlatformPhoneNumber, error)
    UnbindNumber(ctx context.Context, merchantID string) error
    GetNumberInfo(ctx context.Context, merchantID string) (*PlatformPhoneNumber, error)
}

// 短信提供商接口
type ISMSProvider interface {
    // SendMessage 发送短信并自动处理状态更新
    SendMessage(ctx context.Context, req *SendMessageReq) (*SendMessageResp, error)
    // GetMessageStatus 获取消息状态
    GetMessageStatus(ctx context.Context, messageID string) (*MessageStatus, error)
    // HandleCallback 处理平台回调，更新消息状态
    HandleCallback(ctx context.Context, callbackData []byte) error
}

// 短信发送请求
type SendMessageReq struct {
    TenantID        string
    LocationID      string
    ToNumber        string
    TemplateID      string
    TemplateParams  map[string]string
    MsgType         int
}

// 短信发送响应
type SendMessageResp struct {
    MessageID       string
    ProviderMsgID   string
    Status          int
}

// 消息状态
type MessageStatus struct {
    Status          int
    ErrorCode       string
    ErrorMsg        string
    UpdateTime      time.Time
}
```

### 6.2 API 设计
1. 号码管理 API
```
POST   /api/v1/phones/bind
DELETE /api/v1/phones/:phone_id
GET    /api/v1/phones
```

2. 短信发送 API
```
POST   /api/v1/sms-messages/send
GET    /api/v1/sms-messages/:message_id
```

3. 回调接口（每个平台独立）
```
POST   /api/v1/twilio-callback/sms/tenant/:tenant_id/location/:location_id
```

## 7. 性能设计

### 7.1 性能目标
- 短信发送延迟 < 1s
- 支持并发发送
- 高可用性保证

### 7.2 优化方案
1. 缓存优化
   - 号码信息缓存
   - 账号信息缓存
   - 状态信息缓存

2. 并发处理
   - 异步发送
   - 批量处理
   - 连接池优化

3. 幂等性处理
   - 回调消息去重
   - 状态更新幂等
   - 错误重试机制

## 8. 安全方案

### 8.1 安全措施
1. 认证授权
   - 租户认证
   - 接口认证
   - 权限控制

2. 数据安全
   - 敏感信息加密
   - 传输加密
   - 访问控制

3. 防攻击
   - 限流保护
   - 参数验证
   - 异常监控

## 9. 测试方案

### 9.1 测试策略
1. 单元测试
   - 接口测试
   - 业务逻辑测试
   - 工具函数测试

2. 集成测试
   - 平台集成测试
   - 数据库测试
   - 回调处理测试

3. 幂等性测试
   - 重复回调测试
   - 状态更新测试
   - 错误重试测试

## 10. 部署方案

### 10.1 部署架构
1. 服务部署
   - 主服务
   - 回调服务

2. 依赖服务
   - 数据库
   - 缓存服务

### 10.2 监控预留
1. 监控字段
   - 发送成功率
   - 响应时间
   - 错误率
   - 失败原因

2. 统计字段
   - 发送总量
   - 成功数量
   - 失败数量
   - 错误类型分布

## 11. 项目规划

### 11.1 开发计划
1. 第一阶段
   - 基础架构搭建
   - 核心功能实现
   - 单元测试编写

2. 第二阶段
   - 集成测试
   - 性能优化
   - 文档完善

### 11.2 风险评估
1. 技术风险
   - 平台集成风险
   - 性能风险
   - 稳定性风险

2. 应对措施
   - 充分测试
   - 监控告警
   - 应急预案

## 12. 扩展性设计

### 12.1 多平台支持
1. 抽象层设计
   - 统一接口
   - 配置管理
   - 工厂模式

2. 扩展方式
   - 插件化设计
   - 动态加载
   - 配置驱动

### 12.2 未来扩展
1. 消息队列支持
   - 回调处理
   - 异步任务
   - 消息重试

2. 配额管理
   - 时间维度
   - 数量维度
   - 类型维度

3. 监控告警
   - 实时监控
   - 告警规则
   - 统计分析 