# 提醒与通知系统：技术设计文档

## 1. 介绍与范围

本文档旨在详细阐述一套全新的自动化提醒与通知系统的技术设计。该系统的目标是构建一个灵活、可扩展、支持多渠道的通知引擎，以增强客户沟通、降低预约"爽约率"（No-Show）并提升客户留存率。

在初始版本（V1）的实现中，功能范围将严格限定于以下两个核心的、面向客户的提醒功能：

1.  **预约前提醒 (Appointment Reminder)**：在客户预约时间的指定时间前（例如24小时），自动向其发送一条通知。
2.  **客户回访提醒 (Client Recall Reminder)**：当一位客户自上次完成服务后，在指定的一段时间内（例如90天）未再次光临时，自动向其发送一条召回通知。

本系统的设计充分考虑了未来的可扩展性，例如支持更多的事件类型（如预约取消、生日祝福等）和更多的通信渠道（如电子邮件、App 推送等）。

## 2. 数据库设计

为支持本系统，我们将创建两张新表。

### 2.1. `message_templates` (消息模板表)

该表用于存储所有自动化消息的模板。它支持多租户，并提供了门店级别覆盖默认设置的灵活性。

```sql
CREATE TABLE `message_templates` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `template_id` varchar(64) NOT NULL,
  `tenant_id` varchar(64) NOT NULL,
  `location_id` varchar(64) NOT NULL DEFAULT '' COMMENT '空字符串表示租户级别的默认模板。指定ID则为该门店的专属模板，会覆盖默认值。',
  `name` varchar(100) NOT NULL COMMENT '模板名称，例如：24小时预约提醒',
  `event_type` varchar(50) NOT NULL COMMENT '事件类型，例如：appointment_reminder, client_recall',
  `channel_type` varchar(20) NOT NULL DEFAULT 'sms' COMMENT '发送渠道，例如：sms, email',
  `subject` varchar(255) DEFAULT NULL COMMENT '消息主题 (用于邮件渠道)',
  `content` TEXT NOT NULL COMMENT '模板正文，可包含占位符，如 {{client_name}}',
  `trigger_config_days` int NOT NULL COMMENT '时间偏移量（天）。对于预约提醒，表示"事件发生前X天"；对于客户召回，表示"最后一次光临后X天"。',
  `status` TINYINT NOT NULL DEFAULT 1 COMMENT '1-激活, 2-禁用。允许商家开启或关闭特定的提醒规则。',
  `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `deleted_at` TIMESTAMP NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_template_id` (`template_id`),
  KEY `idx_tenant_location_event_channel` (`tenant_id`, `location_id`, `event_type`, `channel_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
```

**关键字段解析**:

*   `location_id`: 实现了 **"租户默认，门店覆盖"** 的策略。当需要为某个 `location` 发送消息时，系统会首先查找 `location_id` 匹配的模板。如果找不到，则回退使用 `location_id` 为空字符串的租户级默认模板。
*   `event_type`: 定义触发通知的业务逻辑的关键字段。在V1版本中，其有效值仅为 `appointment_reminder` 和 `client_recall`。
*   `trigger_config_days`: 定义提醒的触发时机，其具体含义取决于 `event_type` 的值。
*   `status`: 允许商家轻松地启用或禁用某个提醒规则，而无需删除它。

### 2.2. `notification_logs` (通知日志表)

该表作为自动化系统发送的所有通知的台账。其主要目的是防止向同一客户的同一事件发送重复的消息。

```sql
CREATE TABLE `notification_logs` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `tenant_id` varchar(64) NOT NULL,
  `location_id` varchar(64) NOT NULL,
  `trigger_event_type` varchar(50) NOT NULL COMMENT '触发通知的事件类型，例如：appointment_reminder',
  `trigger_event_id` varchar(64) NOT NULL COMMENT '触发事件的唯一ID，例如：appointment_id 或 client_id',
  `template_id` varchar(64) NOT NULL,
  `channel_type` varchar(20) NOT NULL COMMENT '发送渠道，例如：sms, email',
  `message_record_id` varchar(64) NOT NULL COMMENT '对应渠道日志中的消息ID (例如：sms_message_records.message_id)',
  `sent_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_event_template` (`trigger_event_id`, `template_id`),
  KEY `idx_tenant_location` (`tenant_id`, `location_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
```

**关键字段解析**:

*   `trigger_event_id`: 一个通用的ID，指向触发事件的源实体。对于 `appointment_reminder`，它将是 `appointment_id`。对于 `client_recall`，它将是 `client_id`。
*   `UNIQUE KEY uk_event_template`: 这是防止重复的核心机制。任何试图插入具有相同 `trigger_event_id` 和 `template_id` 的记录都将失败，从而确保一个特定的提醒对一个特定的事件只发送一次。

## 3. 后端架构

后端逻辑将由独立的、计划性的定时任务 (Cron Jobs) 驱动。

### 3.1. `AppointmentReminderWorker` (预约提醒任务)

*   **执行周期**: 高频次（例如：每5分钟一次）。
*   **核心逻辑**:
    1.  获取所有激活状态 (`status = 1`) 的 `appointment_reminder` 类型的模板。
    2.  对每一个模板，计算出需要检查预约的目标时间窗口（例如：从现在起24小时到24小时零5分钟之间）。
    3.  查询 `appointments` 表，找出所有处于该时间窗口内的预约。
    4.  对于每一个找到的预约，检查 `notification_logs` 表，确保针对此预约和此模板的提醒尚未发送。
    5.  如果尚未发送，则用必要数据（客户姓名、预约时间等）填充模板，并调用 `NotificationService` 来派发消息。
    6.  派发成功后，在 `notification_logs` 中创建一条记录。

### 3.2. `ClientRecallWorker` (客户回访任务)

*   **执行周期**: 低频次（例如：每天午夜一次）。
*   **核心逻辑**:
    1.  获取所有激活状态 (`status = 1`) 的 `client_recall` 类型的模板。
    2.  对每一个模板（例如一个90天回访的模板），确定最后一次光临的目标日期（`今天 - 90天`）。
    3.  执行数据库查询，找出所有最后一次*已完成*的预约恰好在目标日期，并且之后没有任何新预约的客户。
    4.  对于每一个找到的客户，检查 `notification_logs` 表，确保针对此客户和此模板组合的回访提醒尚未发送。
    5.  如果尚未发送，则填充模板并调用 `NotificationService`。
    6.  派发成功后，在 `notification_logs` 中创建一条记录。

## 4. API 接口

我们将创建一套标准的 RESTful API 用于管理 `message_templates`。

*   `GET /api/tenants/{tenant_id}/message-templates`: 列出指定租户下的所有模板（包括默认和各门店专属的）。
*   `POST /api/tenants/{tenant_id}/message-templates`: 创建一个新的租户级默认模板。
*   `POST /api/tenants/{tenant_id}/locations/{location_id}/message-templates`: 创建一个门店专属模板。
*   `PUT /api/tenants/{tenant_id}/message-templates/{template_id}`: 更新一个模板（例如修改内容、状态或触发时间）。
*   `DELETE /api/tenants/{tenant_id}/message-templates/{template_id}`: 删除一个模板。 