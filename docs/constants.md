# 项目常量定义

## 性别 (Gender)
| 常量名 | 值 | 描述 |
|--------|-----|------|
| GenderUnknown | 1 | 未知性别，未指定性别 |
| GenderMale | 2 | 男性 |
| GenderFemale | 3 | 女性 |

## 预约状态 (Appointment Status)
| 常量名 | 值 | 描述 |
|--------|-----|------|
| AppointmentStatusUnconfirm | 1 | 未确认状态，预约尚未确认 |
| AppointmentStatusWaitListen | 2 | 等待响应状态，等待客户回应确认电话 |
| AppointmentStatusConfirm | 3 | 已确认状态，预约已确认 |
| AppointmentStatusArrived | 4 | 已到达状态，客户已到达地点 |
| AppointmentStatusInService | 5 | 服务中状态，正在提供服务 |
| AppointmentStatusCompleted | 6 | 已完成状态，服务已完成 |
| AppointmentStatusCancelled | 7 | 已取消状态，预约已取消 |
| AppointmentStatusNoShow | 8 | 未出现状态，客户未出席预约 |

## 预约支付状态 (Appointment Payment Status)
| 常量名 | 值 | 描述 |
|--------|-----|------|
| AppointmentPaymentStatusUnpaid | 1 | 未支付状态，预约费用尚未支付 |
| AppointmentPaymentStatusPartialPaid | 2 | 部分支付状态，预约费用已部分支付 |
| AppointmentPaymentStatusPaid | 3 | 已支付状态，预约费用已完全支付 |

## 服务类型 (Service Type)
| 常量名 | 值 | 描述 |
|--------|-----|------|
| BaseServiceType | 1 | 基础服务类型，单个独立服务 |
| ComboServiceType | 2 | 组合服务类型，包含多个服务的套餐 |

## 支付方式类型 (Payment Method Types)
| 常量名 | 值 | 描述 |
|--------|-----|------|
| PaymentMethodCash | 1 | 现金支付方式，实体现金支付 |
| PaymentMethodGiftCard | 2 | 礼品卡支付方式，使用礼品卡支付 |
| PaymentMethodOnlinePayment | 3 | 在线支付方式，电子支付方式 |

## 支付方式状态 (Payment Method Status)
| 常量名 | 值 | 描述 |
|--------|-----|------|
| PaymentMethodStatusEnabled | 1 | 启用状态，支付方式可用 |
| PaymentMethodStatusDisabled | 2 | 禁用状态，支付方式不可用 |

## 服务目标类型 (Target Type)
| 常量名 | 值 | 描述 |
|--------|-----|------|
| TargetTypeClient | 1 | 主要客户类型，服务针对主要客户 |
| TargetTypeSubClient | 2 | 子客户类型，服务针对子客户 |
| TargetTypePet | 3 | 宠物类型，服务针对宠物 |

## Twilio 账户状态 (Twilio Account Status)
| 常量名 | 值 | 描述 |
|--------|-----|------|
| TwilioAccountStatusActive | 1 | 活跃状态，账户可用 |
| TwilioAccountStatusInactive | 2 | 非活跃状态，账户不可用 |

## 消息通道类型 (Message Channel Type)
| 常量名 | 值 | 描述 |
|--------|-----|------|
| MessageChannelTypeSMS | 1 | 短信消息 |
| MessageChannelTypeEmail | 2 | 邮件消息 |
| MessageChannelTypePhone | 3 | 电话消息 |

## 消息方向 (Message Direction)
| 常量名 | 值 | 描述 |
|--------|-----|------|
| MessageDirectionSend | 1 | 外发消息 |
| MessageDirectionReceived | 2 | 接收消息 | 