# 短信服务集成技术方案

## 1. 项目概述

### 1.1 背景
作为SaaS预约系统的重要组成部分，短信服务将为B端商家提供短信通知能力，包括验证码发送、预约通知等功能。

### 1.2 目标
- 实现基础的短信发送功能
- 支持商家自定义短信模板
- 保证短信发送的可靠性和可追踪性
- 预留未来扩展的可能性

### 1.3 MVP范围
- 短信模板管理（CRUD + 审核）
- 基础短信发送功能
- 发送记录和失败追踪
- 基础的重试机制

## 2. 系统设计

### 2.1 整体架构
```ascii
+----------------+     +----------------+     +------------------+
|   业务系统      |     |   短信服务      |     |   短信供应商      |
|  (预约系统)     |---->|  (内部服务)     |---->|  (T<PERSON><PERSON>等)     |
+----------------+     +----------------+     +------------------+
        |                     |                       |
        v                     v                       |
+----------------+     +----------------+             |
|    模板管理      |     |   发送记录      |<------------+
|   (商家后台)     |     |  (监控统计)     |
+----------------+     +----------------+
```

### 2.2 目录结构

```txt
.
├── .github/
│   └── workflows/          # CI/CD 工作流配置 (GitHub Actions)
├── build/                 # 构建产物目录
│   └── ci/                # CI/CD 配置文件
├── cmd/
│   ├── booking/           # Booking应用入口
│   └── webapp/            # WebApp 应用入口
├── configs/                # 应用配置文件目录 (Viper)
├── docs/                  # 项目文档
├── internal/
│   ├── booking/           # Booking 核心业务逻辑
│   └── webapp/            # WebApp 核心业务逻辑
│       ├── config/         # 配置加载与解析
│       ├── handler/       # HTTP 请求处理器 (Gin)
│       ├── models/        # 数据库模型 (GORM)
│       ├── router.go      # 应用路由定义
│       ├── server.go      # HTTP 服务器设置
│       ├── services/      # 业务逻辑服务层
│       └── types/         # API 请求/响应结构体
├── logs/                  # 运行时日志文件目录
├── pkg/                   # 项目级通用工具包
│   ├── db/                # 数据库连接和操作封装
│   ├── ubcrypt/           # Bcrypt 密码加密
│   ├── uerror/            # 统一错误处理
│   ├── ugin/              # Gin 框架扩展和中间件
│   ├── ujwt/              # JWT 认证工具
│   ├── ulog/              # Zap 日志封装
│   └── version/           # 版本信息管理
├── scripts/              # 部署和运维脚本
│   └── webapp/           # WebApp 专用脚本
├── .gitignore             # Git 忽略文件配置
├── go.mod                 # Go 模块依赖
├── go.sum                 # Go 模块校验和
├── makefile                # 项目构建、运行、部署脚本
└── README.md              # 项目说明文档
```

## 3. 数据模型

### 3.1 短信模板表 (sms_templates)
```sql
CREATE TABLE sms_templates (
    id BIGINT PRIMARY KEY,
    tenant_id BIGINT NOT NULL,          -- 商户ID
    name VARCHAR(64) NOT NULL,          -- 模板名称
    code VARCHAR(32) NOT NULL,          -- 模板编码
    content TEXT NOT NULL,              -- 模板内容
    variables JSON NOT NULL,            -- 变量定义
    scene VARCHAR(32) NOT NULL,         -- 使用场景
    status TINYINT NOT NULL DEFAULT 0,  -- 状态
    audit_remark VARCHAR(256),          -- 审核备注
    created_at TIMESTAMP NOT NULL,
    updated_at TIMESTAMP NOT NULL,
    UNIQUE KEY `uk_tenant_code` (tenant_id, code)
);
```

### 3.2 短信发送记录表 (sms_messages)
```sql
CREATE TABLE sms_messages (
    id BIGINT PRIMARY KEY,
    tenant_id BIGINT NOT NULL,          -- 商户ID
    template_id BIGINT NOT NULL,        -- 模板ID
    template_code VARCHAR(32) NOT NULL,  -- 模板编码
    phone VARCHAR(32) NOT NULL,         -- 手机号
    content TEXT NOT NULL,              -- 实际发送内容
    variables JSON NOT NULL,            -- 变量值
    provider VARCHAR(32) NOT NULL,      -- 供应商
    provider_msg_id VARCHAR(64),        -- 供应商消息ID
    status TINYINT NOT NULL,            -- 状态
    retry_count INT NOT NULL DEFAULT 0, -- 重试次数
    error_code VARCHAR(32),             -- 错误码
    error_msg TEXT,                     -- 错误信息
    created_at TIMESTAMP NOT NULL,
    updated_at TIMESTAMP NOT NULL,
    INDEX `idx_tenant_template` (tenant_id, template_code),
    INDEX `idx_phone_created` (phone, created_at)
);
```

### 3.3 发送失败记录表 (sms_message_failures)
```sql
CREATE TABLE sms_message_failures (
    id BIGINT PRIMARY KEY,
    message_id BIGINT NOT NULL,         -- 消息ID
    provider VARCHAR(32) NOT NULL,      -- 供应商
    error_code VARCHAR(32),             -- 错误码
    error_msg TEXT,                     -- 错误信息
    retry_count INT NOT NULL,           -- 重试次数
    created_at TIMESTAMP NOT NULL,
    INDEX `idx_message` (message_id)
);
```

## 4. 模板设计

### 4.1 模板数据结构

1. **content 字段设计**
```
// 模板内容示例
content: "尊敬的用户，您的验证码是{{code}}，{{expireTime}}分钟内有效。"
```

2. **variables 字段设计（JSON格式）**
```json
{
    "variables": [
        {
            "name": "code",
            "description": "验证码",
            "type": "string",
            "required": true,
            "regex": "^\\d{6}$",
            "example": "123456"
        },
        {
            "name": "expireTime",
            "description": "有效期（分钟）",
            "type": "number",
            "required": true,
            "min": 1,
            "max": 30,
            "example": "5"
        }
    ]
}
```

### 4.2 模板处理接口

```go
// 模板变量定义
type TemplateVariable struct {
    Name        string      `json:"name"`         // 变量名
    Description string      `json:"description"`  // 变量描述
    Type        string      `json:"type"`         // 变量类型
    Required    bool        `json:"required"`     // 是否必填
    Regex       string      `json:"regex"`        // 验证规则
    Min         *float64    `json:"min"`          // 最小值（数字类型）
    Max         *float64    `json:"max"`          // 最大值（数字类型）
    Example     string      `json:"example"`      // 示例值
}

// 模板渲染器接口
type TemplateRenderer interface {
    // 渲染模板
    Render(content string, variables map[string]string) (string, error)
    // 验证变量
    ValidateVariables(templateVars []TemplateVariable, inputVars map[string]string) error
}
```

### 4.3 模板使用场景

1. **验证码模板**
```json
{
    "content": "【XX系统】验证码{{code}}，{{expireTime}}分钟内有效，请勿泄露给他人。",
    "variables": [
        {
            "name": "code",
            "type": "string",
            "regex": "^\\d{6}$",
            "required": true
        },
        {
            "name": "expireTime",
            "type": "number",
            "min": 1,
            "max": 30,
            "required": true
        }
    ]
}
```

2. **预约提醒模板**
```json
{
    "content": "【XX预约】尊敬的{{name}}，提醒您于{{date}}在{{location}}的预约，如需取消请提前联系。",
    "variables": [
        {
            "name": "name",
            "type": "string",
            "required": true
        },
        {
            "name": "date",
            "type": "string",
            "required": true
        },
        {
            "name": "location",
            "type": "string",
            "required": true
        }
    ]
}
```

### 4.4 模板处理流程

1. **变量验证**
   - 检查必填项
   - 类型验证（string/number）
   - 数值范围检查
   - 正则表达式匹配

2. **内容渲染**
   - 变量替换
   - 未替换变量检查
   - 内容完整性验证

3. **错误处理**
   - 变量缺失错误
   - 类型不匹配错误
   - 格式验证错误
   - 渲染失败错误

### 4.5 设计优势

1. 变量定义清晰，支持类型检查
2. 支持灵活的验证规则
3. 易于扩展新的模板类型
4. 便于管理和维护
5. 支持多场景使用
6. 提供完整的错误处理机制

## 5. 核心接口设计

### 5.1 短信服务接口
```go
type SMSService interface {
    // 发送短信
    Send(ctx context.Context, req *SendRequest) (*SendResult, error)
    // 查询发送状态
    GetStatus(ctx context.Context, messageID int64) (*MessageStatus, error)
    // 重试失败的消息
    RetryFailedMessage(ctx context.Context, messageID int64) error
}

type SendRequest struct {
    TenantID    int64
    TemplateCode string
    Phone       string
    Variables   map[string]string
}

type SendResult struct {
    MessageID     int64
    ProviderMsgID string
    Status        MessageStatus
}
```

### 5.2 模板管理接口
```go
type TemplateManager interface {
    CreateTemplate(ctx context.Context, template *Template) error
    UpdateTemplate(ctx context.Context, template *Template) error
    GetTemplate(ctx context.Context, id int64) (*Template, error)
    ListTemplates(ctx context.Context, query *TemplateQuery) ([]*Template, error)
    AuditTemplate(ctx context.Context, id int64, approved bool, remark string) error
}
```

### 5.3 供应商接口
```go
type Provider interface {
    Send(ctx context.Context, msg *Message) (string, error)
    GetStatus(ctx context.Context, providerMsgID string) (MessageStatus, error)
    Name() string
}
```

## 6. 错误处理

### 6.1 错误类型
```go
type ErrorType int

const (
    ErrorTypeSystem    ErrorType = iota // 系统错误
    ErrorTypeProvider                   // 供应商错误
    ErrorTypeTemplate                   // 模板错误
    ErrorTypeValidation                 // 验证错误
)
```

### 6.2 重试策略
- 最大重试次数：3次
- 重试间隔：指数退避（1min, 5min, 30min）
- 仅对特定错误类型进行重试（供应商临时错误）

## 7. 监控指标

### 7.1 基础指标
- 发送总量
- 发送成功率
- 发送延迟
- 重试次数分布

### 7.2 错误统计
- 错误类型分布
- 供应商错误率
- 模板错误率

## 8. 配置管理

### 8.1 供应商配置
```yaml
sms:
  default_provider: "twilio"
  providers:
    twilio:
      account_sid: "${SMS_ACCOUNT_SID}"
      auth_token: "${SMS_AUTH_TOKEN}"
      from_number: "${SMS_FROM_NUMBER}"
  retry:
    max_attempts: 3
    intervals: [60, 300, 1800]
```

## 9. 实施计划

### 9.1 第一阶段（MVP - 2周）
- 基础数据模型实现
- Twilio供应商接入
- 模板CRUD功能
- 基础发送功能
- 失败记录

### 9.2 第二阶段（优化 - 1周）
- 完善错误处理
- 实现重试机制
- 添加基础监控
- 管理界面集成

## 10. 未来扩展

### 10.1 功能扩展
- 多供应商支持
- 智能路由
- 多语言模板
- 批量发送
- 发送速率控制
- 黑名单管理

### 10.2 运营功能
- 成本统计
- 使用量分析
- 模板分析
- 发送统计报表

## 11. 注意事项

### 11.1 安全性
- 敏感配置加密存储
- 手机号码脱敏
- 访问权限控制
- 操作日志记录

### 11.2 性能
- 数据库索引优化
- 并发控制
- 缓存使用
- 异步处理

### 11.3 可维护性
- 完整的日志记录
- 统一的错误处理
- 清晰的代码结构
- 充分的注释说明