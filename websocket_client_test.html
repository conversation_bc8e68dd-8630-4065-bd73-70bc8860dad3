<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WebSocket Client Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .container {
            margin-bottom: 20px;
        }
        .log {
            background-color: #f5f5f5;
            border: 1px solid #ddd;
            padding: 10px;
            height: 300px;
            overflow-y: scroll;
            font-family: monospace;
            font-size: 12px;
        }
        .controls {
            margin: 10px 0;
        }
        input, button, select {
            margin: 5px;
            padding: 5px;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .status.connected {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.disconnected {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .message-type {
            font-weight: bold;
        }
        .message-type.notify { color: #007bff; }
        .message-type.heartbeat { color: #6c757d; }
        .message-type.error { color: #dc3545; }
        .message-type.event { color: #28a745; }
    </style>
</head>
<body>
    <h1>WebSocket Client Test</h1>
    
    <div class="container">
        <h3>Connection</h3>
        <div class="controls">
            <input type="text" id="serverUrl" value="ws://localhost:8082/api/v1/ws" placeholder="WebSocket URL">
            <input type="text" id="token" placeholder="JWT Token (optional)">
            <button onclick="connect()">Connect</button>
            <button onclick="disconnect()">Disconnect</button>
        </div>
        <div id="status" class="status disconnected">Disconnected</div>
    </div>

    <div class="container">
        <h3>Send Message</h3>
        <div class="controls">
            <select id="messageType">
                <option value="heartbeat">Heartbeat</option>
                <option value="notify">Notify</option>
                <option value="event">Event</option>
            </select>
            <input type="text" id="messageEvent" placeholder="Event name" value="ping">
            <input type="text" id="messageData" placeholder="Message data (JSON)" value='{"status": "alive"}'>
            <button onclick="sendMessage()">Send Message</button>
        </div>
    </div>

    <div class="container">
        <h3>Message Log</h3>
        <div class="controls">
            <button onclick="clearLog()">Clear Log</button>
            <label>
                <input type="checkbox" id="autoScroll" checked> Auto Scroll
            </label>
        </div>
        <div id="log" class="log"></div>
    </div>

    <script>
        let ws = null;
        let messageCount = 0;

        function log(message, type = 'info') {
            const logDiv = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            const messageId = ++messageCount;
            
            const logEntry = document.createElement('div');
            logEntry.innerHTML = `<span style="color: #666;">[${timestamp}] #${messageId}</span> ${message}`;
            logDiv.appendChild(logEntry);
            
            if (document.getElementById('autoScroll').checked) {
                logDiv.scrollTop = logDiv.scrollHeight;
            }
        }

        function updateStatus(connected) {
            const statusDiv = document.getElementById('status');
            if (connected) {
                statusDiv.textContent = 'Connected';
                statusDiv.className = 'status connected';
            } else {
                statusDiv.textContent = 'Disconnected';
                statusDiv.className = 'status disconnected';
            }
        }

        function connect() {
            if (ws && ws.readyState === WebSocket.OPEN) {
                log('Already connected', 'warning');
                return;
            }

            const serverUrl = document.getElementById('serverUrl').value;
            const token = document.getElementById('token').value;
            
            let url = serverUrl;
            if (token) {
                url += `?token=${encodeURIComponent(token)}`;
            }

            log(`Connecting to ${url}...`);
            
            try {
                ws = new WebSocket(url);
                
                ws.onopen = function(event) {
                    log('✅ WebSocket connected', 'success');
                    updateStatus(true);
                };
                
                ws.onmessage = function(event) {
                    try {
                        const message = JSON.parse(event.data);
                        const typeClass = `message-type ${message.type}`;
                        log(`📨 <span class="${typeClass}">[${message.type.toUpperCase()}]</span> ${message.event}: ${JSON.stringify(message.data)}`, 'message');
                        
                        // Auto-respond to ping with pong
                        if (message.type === 'heartbeat' && message.event === 'ping') {
                            setTimeout(() => {
                                sendPong();
                            }, 100);
                        }
                    } catch (e) {
                        log(`📨 Raw message: ${event.data}`, 'message');
                    }
                };
                
                ws.onclose = function(event) {
                    log(`❌ WebSocket disconnected (code: ${event.code}, reason: ${event.reason})`, 'error');
                    updateStatus(false);
                };
                
                ws.onerror = function(error) {
                    log(`❌ WebSocket error: ${error}`, 'error');
                    updateStatus(false);
                };
                
            } catch (error) {
                log(`❌ Connection failed: ${error}`, 'error');
            }
        }

        function disconnect() {
            if (ws) {
                ws.close();
                ws = null;
            }
        }

        function sendMessage() {
            if (!ws || ws.readyState !== WebSocket.OPEN) {
                log('❌ Not connected', 'error');
                return;
            }

            const type = document.getElementById('messageType').value;
            const event = document.getElementById('messageEvent').value;
            const dataStr = document.getElementById('messageData').value;
            
            let data;
            try {
                data = JSON.parse(dataStr);
            } catch (e) {
                data = dataStr;
            }

            const message = {
                id: generateId(),
                type: type,
                event: event,
                data: data,
                timestamp: Date.now()
            };

            try {
                ws.send(JSON.stringify(message));
                log(`📤 Sent: <span class="message-type ${type}">[${type.toUpperCase()}]</span> ${event}: ${JSON.stringify(data)}`, 'sent');
            } catch (error) {
                log(`❌ Send failed: ${error}`, 'error');
            }
        }

        function sendPong() {
            if (!ws || ws.readyState !== WebSocket.OPEN) {
                return;
            }

            const message = {
                id: generateId(),
                type: 'heartbeat',
                event: 'pong',
                data: { status: 'alive' },
                timestamp: Date.now()
            };

            try {
                ws.send(JSON.stringify(message));
                log(`📤 Auto-sent pong`, 'sent');
            } catch (error) {
                log(`❌ Pong send failed: ${error}`, 'error');
            }
        }

        function clearLog() {
            document.getElementById('log').innerHTML = '';
            messageCount = 0;
        }

        function generateId() {
            return 'msg_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
        }

        // Initialize
        log('WebSocket client ready. Enter JWT token and click Connect to start.');
        
        // Auto-connect on Enter key in token field
        document.getElementById('token').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                connect();
            }
        });
    </script>
</body>
</html>
