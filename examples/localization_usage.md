# 本地化接口使用示例

本文档提供了如何使用本地化接口的具体示例。

## 前端 JavaScript 使用示例

### 1. 获取完整本地化数据

```javascript
// 获取所有本地化数据
async function getLocalizationData() {
    try {
        const response = await fetch('/localization');
        const data = await response.json();
        
        if (data.success) {
            const { currencies, countries, languages } = data.data.obj;
            
            console.log('货币数量:', currencies.length);
            console.log('国家数量:', countries.length);
            console.log('语言数量:', languages.length);
            
            return data.data.obj;
        } else {
            console.error('获取本地化数据失败:', data.message);
        }
    } catch (error) {
        console.error('请求失败:', error);
    }
}

// 使用示例
getLocalizationData().then(data => {
    if (data) {
        // 填充货币选择器
        populateCurrencySelector(data.currencies);
        // 填充国家选择器
        populateCountrySelector(data.countries);
        // 填充语言选择器
        populateLanguageSelector(data.languages);
    }
});
```

### 2. 获取主要本地化数据（推荐用于快速加载）

```javascript
// 获取主要本地化数据
async function getMajorLocalizationData() {
    try {
        const response = await fetch('/localization/major');
        const data = await response.json();
        
        if (data.success) {
            return data.data.obj;
        }
    } catch (error) {
        console.error('请求失败:', error);
    }
}
```

### 3. 单独获取货币数据

```javascript
// 获取所有货币
async function getCurrencies() {
    try {
        const response = await fetch('/currencies');
        const data = await response.json();
        
        if (data.success) {
            return data.data.list;
        }
    } catch (error) {
        console.error('请求失败:', error);
    }
}

// 创建货币选择器
function populateCurrencySelector(currencies) {
    const selector = document.getElementById('currency-selector');
    
    currencies.forEach(currency => {
        const option = document.createElement('option');
        option.value = currency.code;
        option.textContent = `${currency.code} (${currency.symbol}) - ${currency.name}`;
        selector.appendChild(option);
    });
}
```

### 4. 货币格式化工具

```javascript
class CurrencyFormatter {
    constructor() {
        this.currencies = new Map();
        this.loadCurrencies();
    }
    
    async loadCurrencies() {
        try {
            const response = await fetch('/currencies');
            const data = await response.json();
            
            if (data.success) {
                data.data.list.forEach(currency => {
                    this.currencies.set(currency.code, currency);
                });
            }
        } catch (error) {
            console.error('加载货币数据失败:', error);
        }
    }
    
    formatAmount(amount, currencyCode) {
        const currency = this.currencies.get(currencyCode);
        if (!currency) {
            return `${amount} ${currencyCode}`;
        }
        
        return `${currency.symbol}${amount.toFixed(2)}`;
    }
    
    getCurrencySymbol(currencyCode) {
        const currency = this.currencies.get(currencyCode);
        return currency ? currency.symbol : currencyCode;
    }
}

// 使用示例
const formatter = new CurrencyFormatter();
setTimeout(() => {
    console.log(formatter.formatAmount(100, 'USD')); // $100.00
    console.log(formatter.formatAmount(85.5, 'EUR')); // €85.50
    console.log(formatter.getCurrencySymbol('GBP')); // £
}, 1000); // 等待数据加载
```

### 5. 国家和货币关联

```javascript
// 根据国家获取货币信息
async function getCountryWithCurrency() {
    try {
        const response = await fetch('/countries');
        const data = await response.json();
        
        if (data.success) {
            const countries = data.data.list;
            
            // 创建国家到货币的映射
            const countryToCurrency = new Map();
            countries.forEach(country => {
                countryToCurrency.set(country.code, country.currency);
            });
            
            // 使用示例
            const usCurrency = countryToCurrency.get('US');
            console.log('美国货币:', usCurrency); // {code: "USD", symbol: "$", name: "US Dollar"}
            
            const cnCurrency = countryToCurrency.get('CN');
            console.log('中国货币:', cnCurrency); // {code: "CNY", symbol: "¥", name: "Chinese Yuan"}
            
            return countryToCurrency;
        }
    } catch (error) {
        console.error('请求失败:', error);
    }
}
```

### 6. 多语言支持

```javascript
// 语言选择器
async function setupLanguageSelector() {
    try {
        const response = await fetch('/languages');
        const data = await response.json();
        
        if (data.success) {
            const languages = data.data.list;
            const selector = document.getElementById('language-selector');
            
            languages.forEach(language => {
                const option = document.createElement('option');
                option.value = language.code;
                option.textContent = language.name;
                selector.appendChild(option);
            });
            
            // 监听语言变化
            selector.addEventListener('change', (e) => {
                const selectedLanguage = e.target.value;
                changeLanguage(selectedLanguage);
            });
        }
    } catch (error) {
        console.error('请求失败:', error);
    }
}

function changeLanguage(languageCode) {
    // 这里可以实现语言切换逻辑
    console.log('切换到语言:', languageCode);
    // 例如：加载对应的语言包，更新界面文本等
}
```

## React 使用示例

```jsx
import React, { useState, useEffect } from 'react';

// 自定义 Hook 用于获取本地化数据
function useLocalizationData() {
    const [data, setData] = useState(null);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState(null);
    
    useEffect(() => {
        async function fetchData() {
            try {
                const response = await fetch('/localization/major');
                const result = await response.json();
                
                if (result.success) {
                    setData(result.data.obj);
                } else {
                    setError(result.message);
                }
            } catch (err) {
                setError(err.message);
            } finally {
                setLoading(false);
            }
        }
        
        fetchData();
    }, []);
    
    return { data, loading, error };
}

// 货币选择组件
function CurrencySelector({ onCurrencyChange }) {
    const { data, loading, error } = useLocalizationData();
    
    if (loading) return <div>加载中...</div>;
    if (error) return <div>错误: {error}</div>;
    
    return (
        <select onChange={(e) => onCurrencyChange(e.target.value)}>
            <option value="">选择货币</option>
            {data?.currencies.map(currency => (
                <option key={currency.code} value={currency.code}>
                    {currency.code} ({currency.symbol}) - {currency.name}
                </option>
            ))}
        </select>
    );
}

// 国家选择组件
function CountrySelector({ onCountryChange }) {
    const { data, loading, error } = useLocalizationData();
    
    if (loading) return <div>加载中...</div>;
    if (error) return <div>错误: {error}</div>;
    
    return (
        <select onChange={(e) => onCountryChange(e.target.value)}>
            <option value="">选择国家</option>
            {data?.countries.map(country => (
                <option key={country.code} value={country.code}>
                    {country.name} ({country.currency.code})
                </option>
            ))}
        </select>
    );
}

// 主应用组件
function App() {
    const [selectedCurrency, setSelectedCurrency] = useState('');
    const [selectedCountry, setSelectedCountry] = useState('');
    
    return (
        <div>
            <h1>本地化示例</h1>
            
            <div>
                <label>选择货币:</label>
                <CurrencySelector onCurrencyChange={setSelectedCurrency} />
                <p>已选择: {selectedCurrency}</p>
            </div>
            
            <div>
                <label>选择国家:</label>
                <CountrySelector onCountryChange={setSelectedCountry} />
                <p>已选择: {selectedCountry}</p>
            </div>
        </div>
    );
}

export default App;
```

## 缓存策略建议

```javascript
// 本地化数据缓存管理器
class LocalizationCache {
    constructor() {
        this.cacheKey = 'localization_data';
        this.cacheExpiry = 24 * 60 * 60 * 1000; // 24小时
    }
    
    // 获取缓存数据
    getCachedData() {
        const cached = localStorage.getItem(this.cacheKey);
        if (!cached) return null;
        
        const { data, timestamp } = JSON.parse(cached);
        const now = Date.now();
        
        // 检查是否过期
        if (now - timestamp > this.cacheExpiry) {
            localStorage.removeItem(this.cacheKey);
            return null;
        }
        
        return data;
    }
    
    // 设置缓存数据
    setCachedData(data) {
        const cacheData = {
            data,
            timestamp: Date.now()
        };
        localStorage.setItem(this.cacheKey, JSON.stringify(cacheData));
    }
    
    // 获取本地化数据（带缓存）
    async getLocalizationData() {
        // 先尝试从缓存获取
        let data = this.getCachedData();
        if (data) {
            return data;
        }
        
        // 缓存不存在或已过期，从服务器获取
        try {
            const response = await fetch('/localization/major');
            const result = await response.json();
            
            if (result.success) {
                data = result.data.obj;
                this.setCachedData(data);
                return data;
            }
        } catch (error) {
            console.error('获取本地化数据失败:', error);
        }
        
        return null;
    }
}

// 使用示例
const cache = new LocalizationCache();
cache.getLocalizationData().then(data => {
    if (data) {
        console.log('本地化数据已加载（可能来自缓存）');
        // 使用数据...
    }
});
```

这些示例展示了如何在不同场景下使用本地化接口，包括基本的数据获取、React 组件集成、以及缓存策略等。
