basePath: /api/v1
definitions:
  sms.PhoneNumber:
    properties:
      capability:
        example:
        - '["SMS"'
        - '"Voice"]'
        items:
          $ref: '#/definitions/types.PhoneCapability'
        type: array
      country:
        example: US
        type: string
      phone_number:
        example: "+***********"
        type: string
    type: object
  sms.PurchaseNumberReq:
    properties:
      area_code:
        example: 555
        type: integer
      country:
        example: US
        type: string
      phone_number:
        example: "+***********"
        type: string
    required:
    - country
    - phone_number
    type: object
  types.Account:
    properties:
      account_id:
        example: acc_123456
        type: string
      created_at:
        example: "2023-01-01T12:00:00Z"
        type: string
      email:
        example: <EMAIL>
        type: string
      first_name:
        example: John
        type: string
      last_name:
        example: Doe
        type: string
      phone_number:
        example: "+**********"
        type: string
      updated_at:
        example: "2023-01-02T12:00:00Z"
        type: string
    type: object
  types.AccountEntity:
    properties:
      account_id:
        example: acc_123456
        type: string
      email:
        example: <EMAIL>
        type: string
      first_name:
        example: John
        type: string
      last_name:
        example: Doe
        type: string
      phone_number:
        example: "+**********"
        type: string
    type: object
  types.AppointmentDetail:
    properties:
      actual_end_date:
        example: "2023-07-01"
        type: string
      actual_end_time:
        example: 39300
        type: integer
      actual_start_date:
        example: "2023-07-01"
        type: string
      actual_start_time:
        example: 36300
        type: integer
      appointment_id:
        example: appt_123456789
        type: string
      client_first_name:
        example: John
        type: string
      client_id:
        example: client_123456
        type: string
      client_last_name:
        example: Doe
        type: string
      created_at:
        example: "2023-01-01T12:00:00Z"
        type: string
      location_id:
        example: loc_123456
        type: string
      payment_records:
        items:
          $ref: '#/definitions/types.PaymentRecords'
        type: array
      payment_status:
        allOf:
        - $ref: '#/definitions/types.AppointmentPaymentStatus'
        example: 1
      scheduled_end_date:
        example: "2023-07-01"
        type: string
      scheduled_end_time:
        example: 39600
        type: integer
      scheduled_start_date:
        example: "2023-07-01"
        type: string
      scheduled_start_time:
        example: 36000
        type: integer
      services:
        items:
          $ref: '#/definitions/types.AppointmentService'
        type: array
      status:
        allOf:
        - $ref: '#/definitions/types.AppointmentStatusType'
        example: 2
      tenant_id:
        example: tenant_123456
        type: string
      updated_at:
        example: "2023-01-02T12:00:00Z"
        type: string
    type: object
  types.AppointmentPaymentStatus:
    enum:
    - 1
    - 2
    - 3
    type: integer
    x-enum-comments:
      AppointmentPaymentStatusPaid: Fully paid status, appointment fees have been
        fully paid
      AppointmentPaymentStatusPartialPaid: Partially paid status, appointment fees
        have been partially paid
      AppointmentPaymentStatusUnpaid: Unpaid status, appointment fees have not been
        paid
    x-enum-varnames:
    - AppointmentPaymentStatusUnpaid
    - AppointmentPaymentStatusPartialPaid
    - AppointmentPaymentStatusPaid
  types.AppointmentService:
    properties:
      account_id:
        example: acc_123456
        type: string
      appointment_id:
        example: appt_123456789
        type: string
      appt_service_id:
        example: aptsrv_123456
        type: string
      created_at:
        example: "2023-01-01T12:00:00Z"
        type: string
      duration:
        example: 30
        type: integer
      location_id:
        example: loc_123456
        type: string
      name:
        example: Haircut
        type: string
      origin_price:
        example: 2500
        type: integer
      origin_tax_id:
        example: tax_123456
        type: string
      origin_tax_rate:
        example: 800
        type: integer
      price:
        example: 2500
        type: integer
      service_id:
        example: srv_123456
        type: string
      service_type:
        allOf:
        - $ref: '#/definitions/types.ServiceType'
        example: 1
      snapshot:
        example: '{"name":"Haircut","duration":30,"price":2500}'
        type: string
      target_id:
        example: tgt_123456
        type: string
      target_type:
        allOf:
        - $ref: '#/definitions/types.TargetType'
        example: 1
      tax_rate:
        example: 800
        type: integer
      tenant_id:
        example: tenant_123456
        type: string
      updated_at:
        example: "2023-01-02T12:00:00Z"
        type: string
    type: object
  types.AppointmentStatusType:
    enum:
    - 1
    - 2
    - 3
    - 4
    - 5
    - 6
    - 7
    - 8
    type: integer
    x-enum-comments:
      AppointmentStatusArrived: Arrived status, client has arrived at the location
      AppointmentStatusCancelled: Cancelled status, appointment has been cancelled
      AppointmentStatusCompleted: Completed status, service has been completed
      AppointmentStatusConfirm: Confirmed status, appointment has been confirmed
      AppointmentStatusInService: In Service status, service is currently being provided
      AppointmentStatusNoShow: No Show status, client did not show up for the appointment
      AppointmentStatusUnconfirm: Unconfirmed status, appointment has not been confirmed
        yet
      AppointmentStatusWaitListen: Waiting for response status, waiting for client
        to respond to confirmation call
    x-enum-varnames:
    - AppointmentStatusUnconfirm
    - AppointmentStatusWaitListen
    - AppointmentStatusConfirm
    - AppointmentStatusArrived
    - AppointmentStatusInService
    - AppointmentStatusCompleted
    - AppointmentStatusCancelled
    - AppointmentStatusNoShow
  types.BatchUpdateStaffScheduleOverrideReq:
    properties:
      account_id:
        example:
        - '["acc_123456"]'
        items:
          type: string
        type: array
      dates:
        example:
        - '["2023-07-01"'
        - '"2023-07-02"]'
        items:
          type: string
        type: array
      is_day_off:
        example: false
        type: boolean
      reason:
        example: holiday
        type: string
      working_times:
        items:
          $ref: '#/definitions/types.WorkingTimeItem'
        type: array
    required:
    - account_id
    - dates
    - is_day_off
    - reason
    - working_times
    type: object
  types.BatchUpdateStaffScheduleReq:
    properties:
      account_id:
        example:
        - '["acc_123456"'
        - '"acc_789012"]'
        items:
          type: string
        type: array
      is_day_off:
        example: false
        type: boolean
      weekday:
        items:
          type: integer
        maxItems: 6
        minItems: 0
        type: array
      working_times:
        items:
          $ref: '#/definitions/types.WorkingTimeItem'
        type: array
    required:
    - account_id
    - is_day_off
    - weekday
    - working_times
    type: object
  types.Client:
    properties:
      avatar:
        example: https://example.com/avatar.jpg
        type: string
      birthday:
        example: "1990-01-01"
        type: string
      client_id:
        example: client_2viR3b4cZgmpSORwJ127jHKFZHh
        type: string
      created_at:
        example: "2023-01-01T12:00:00Z"
        type: string
      email:
        example: <EMAIL>
        type: string
      first_name:
        example: John
        type: string
      gender:
        allOf:
        - $ref: '#/definitions/types.Gender'
        example: 1
      language:
        example: en
        type: string
      last_name:
        example: Doe
        type: string
      location_id:
        example: loc_2viQlkAbQuyb04fe9cF04JP2AOj
        type: string
      phone_number:
        example: "+***********"
        type: string
      source:
        example: web
        type: string
      tenant_id:
        example: tenant_2viQlj16EjDj8zLRbgibcidla4i
        type: string
      updated_at:
        example: "2023-01-02T12:00:00Z"
        type: string
    type: object
  types.CreateAppointmentDetailReq:
    properties:
      client_id:
        example: client_123456
        type: string
      scheduled_end_date:
        example: "2023-07-01"
        type: string
      scheduled_end_time:
        example: 39600
        maximum: 86400
        minimum: 0
        type: integer
      scheduled_start_date:
        example: "2023-07-01"
        type: string
      scheduled_start_time:
        example: 36000
        maximum: 86400
        minimum: 0
        type: integer
      services:
        items:
          properties:
            account_id:
              example: acc_123456
              type: string
            service_id:
              example: srv_123456
              type: string
            service_type:
              allOf:
              - $ref: '#/definitions/types.ServiceType'
              example: 1
              maximum: 2
              minimum: 1
            target_id:
              example: client_123456
              type: string
            target_type:
              allOf:
              - $ref: '#/definitions/types.TargetType'
              example: 1
              maximum: 3
              minimum: 1
          required:
          - account_id
          - service_id
          - service_type
          - target_id
          - target_type
          type: object
        type: array
    required:
    - client_id
    - scheduled_end_date
    - scheduled_end_time
    - scheduled_start_date
    - scheduled_start_time
    - services
    type: object
  types.CreateClientReq:
    properties:
      avatar:
        example: https://example.com/avatar.jpg
        type: string
      birthday:
        example: "1990-01-01"
        type: string
      email:
        example: <EMAIL>
        type: string
      first_name:
        example: John
        type: string
      gender:
        allOf:
        - $ref: '#/definitions/types.Gender'
        enum:
        - 0
        - 1
        - 2
        example: 2
      language:
        example: en
        type: string
      last_name:
        example: Doe
        type: string
      password:
        example: password123
        type: string
      phone_number:
        example: "+***********"
        type: string
      source:
        example: web
        type: string
    type: object
  types.CreateHolidayReq:
    properties:
      date:
        example: "2023-07-01"
        type: string
      description:
        example: Independence Day
        type: string
      is_day_off:
        example: false
        type: boolean
      name:
        example: Independence Day
        type: string
    required:
    - date
    - description
    - is_day_off
    type: object
  types.CreatePaymentMethodReq:
    properties:
      name:
        example: Cash
        type: string
      status:
        allOf:
        - $ref: '#/definitions/types.PaymentMethodStatus'
        example: 1
      type:
        allOf:
        - $ref: '#/definitions/types.PaymentMethodTypes'
        example: 1
        maximum: 3
        minimum: 1
    required:
    - name
    - status
    - type
    type: object
  types.CreateServiceCategoryReq:
    properties:
      name:
        example: Haircut Services
        type: string
    required:
    - name
    type: object
  types.CreateServiceReq:
    properties:
      allow_online_booking:
        example: true
        type: boolean
      bookable_separately:
        example: true
        type: boolean
      category_id:
        example: cat_2viQlkAbQuyb04fe9cF04JP2AOj
        type: string
      color:
        example: '#FF5733'
        type: string
      description:
        example: Basic haircut service
        type: string
      duration:
        example: 30
        type: integer
      inclusive:
        example: true
        type: boolean
      name:
        example: Haircut
        type: string
      price:
        example: 2500
        type: integer
      sellable_separately:
        example: true
        type: boolean
      service_type:
        allOf:
        - $ref: '#/definitions/types.ServiceType'
        example: 1
        maximum: 2
        minimum: 1
      status:
        allOf:
        - $ref: '#/definitions/types.Status'
        example: 1
        maximum: 2
        minimum: 1
      tax_id:
        example: tax_2viQlkAbQuyb04fe9cF04JP2AOj
        type: string
    required:
    - category_id
    - duration
    - inclusive
    - name
    - price
    - service_type
    type: object
  types.CreateTaxReq:
    properties:
      name:
        example: Sales Tax
        type: string
      rate:
        example: 800
        maximum: 100000
        minimum: 0
        type: integer
    required:
    - name
    - rate
    type: object
  types.Gender:
    enum:
    - 1
    - 2
    - 3
    type: integer
    x-enum-comments:
      GenderFemale: Female gender
      GenderMale: Male gender
      GenderUnknown: Unknown gender, gender is not specified
    x-enum-varnames:
    - GenderUnknown
    - GenderMale
    - GenderFemale
  types.Holiday:
    properties:
      created_at:
        example: "2023-01-01T12:00:00Z"
        type: string
      date:
        example: "2023-07-01"
        type: string
      description:
        example: Independence Day
        type: string
      holiday_id:
        example: hol_123456
        type: string
      is_day_off:
        example: false
        type: boolean
      location_id:
        example: loc_123456
        type: string
      name:
        example: Independence Day
        type: string
      tenant_id:
        example: tenant_123456
        type: string
      updated_at:
        example: "2023-01-02T12:00:00Z"
        type: string
    type: object
  types.InviteStaffReq:
    properties:
      email:
        example: <EMAIL>
        type: string
      first_name:
        example: John
        type: string
      last_name:
        example: Doe
        type: string
      password:
        description: todo
        example: password123
        minLength: 6
        type: string
      phone_number:
        example: "+***********"
        type: string
    required:
    - email
    - first_name
    - last_name
    type: object
  types.Location:
    properties:
      country:
        example: US
        type: string
      created_at:
        example: "2023-01-01T12:00:00Z"
        type: string
      currency:
        example: USD
        type: string
      location_id:
        example: loc_123456
        type: string
      name:
        example: Downtown Salon
        type: string
      status:
        allOf:
        - $ref: '#/definitions/types.Status'
        example: 1
      tenant_id:
        example: tenant_123456
        type: string
      timezone:
        example: America/New_York
        type: string
      updated_at:
        example: "2023-01-02T12:00:00Z"
        type: string
    type: object
  types.LoginReq:
    properties:
      email:
        example: <EMAIL>
        type: string
      password:
        example: password123
        type: string
    required:
    - email
    - password
    type: object
  types.LoginResp:
    properties:
      account:
        $ref: '#/definitions/types.AccountEntity'
      belong_locations:
        items:
          $ref: '#/definitions/types.LoginRespItem'
        type: array
      default_token:
        $ref: '#/definitions/types.LoginRespItem'
    type: object
  types.LoginRespItem:
    properties:
      access_token:
        example: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
        type: string
      country:
        example: US
        type: string
      currency:
        example: USD
        type: string
      expire_time:
        example: **********
        type: integer
      expires:
        example: 3600
        type: integer
      is_owner:
        example: true
        type: boolean
      location_id:
        example: loc_123456
        type: string
      name:
        example: Downtown Salon
        type: string
      refresh_token:
        example: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
        type: string
      status:
        allOf:
        - $ref: '#/definitions/types.Status'
        example: 1
      tenant_id:
        example: tenant_123456
        type: string
      timezone:
        example: America/New_York
        type: string
    type: object
  types.PaymentMethodStatus:
    enum:
    - 1
    - 2
    type: integer
    x-enum-comments:
      PaymentMethodStatusDisabled: Disabled status, payment method is not available
        for use
      PaymentMethodStatusEnabled: Enabled status, payment method is available for
        use
    x-enum-varnames:
    - PaymentMethodStatusEnabled
    - PaymentMethodStatusDisabled
  types.PaymentMethodTypes:
    enum:
    - 1
    - 2
    - 3
    type: integer
    x-enum-comments:
      PaymentMethodCash: Cash payment method, physical cash payment
      PaymentMethodGiftCard: Gift Card payment method, payment using a gift card
      PaymentMethodOnlinePayment: Online Payment method, electronic payment methods
    x-enum-varnames:
    - PaymentMethodCash
    - PaymentMethodGiftCard
    - PaymentMethodOnlinePayment
  types.PaymentMethods:
    properties:
      created_at:
        example: "2023-01-01T12:00:00Z"
        type: string
      location_id:
        example: loc_123456
        type: string
      method_id:
        example: method_123456
        type: string
      name:
        example: Cash
        type: string
      status:
        allOf:
        - $ref: '#/definitions/types.PaymentMethodStatus'
        example: 1
      tenant_id:
        example: tenant_123456
        type: string
      type:
        allOf:
        - $ref: '#/definitions/types.PaymentMethodTypes'
        example: 1
      updated_at:
        example: "2023-01-02T12:00:00Z"
        type: string
    required:
    - location_id
    - method_id
    - name
    - status
    - tenant_id
    - type
    type: object
  types.PaymentRecordStatus:
    enum:
    - 1
    - 2
    - 3
    - 4
    type: integer
    x-enum-comments:
      PaymentStatusFailed: Failed status, payment has failed
      PaymentStatusPending: Pending status, payment is being processed
      PaymentStatusRefunded: Refunded status, payment has been refunded
      PaymentStatusSuccess: Success status, payment was successful
    x-enum-varnames:
    - PaymentStatusPending
    - PaymentStatusSuccess
    - PaymentStatusRefunded
    - PaymentStatusFailed
  types.PaymentRecordType:
    enum:
    - 1
    - 2
    type: integer
    x-enum-comments:
      TypePayment: Payment type, record of a payment transaction
      TypeRefund: Refund type, record of a refund transaction
    x-enum-varnames:
    - TypePayment
    - TypeRefund
  types.PaymentRecords:
    properties:
      amount:
        example: 10000
        type: integer
      created_at:
        example: "2023-01-01T12:00:00Z"
        type: string
      location_id:
        example: loc_123456
        type: string
      method_id:
        example: method_123456
        type: string
      method_name:
        example: Cash
        type: string
      payment_id:
        example: pay_123456
        type: string
      reference_id:
        example: ref_123456
        type: string
      source_id:
        example: appt_123456789
        type: string
      source_type:
        allOf:
        - $ref: '#/definitions/types.SourceType'
        example: 1
      status:
        allOf:
        - $ref: '#/definitions/types.PaymentRecordStatus'
        example: 2
      tenant_id:
        example: tenant_123456
        type: string
      type:
        allOf:
        - $ref: '#/definitions/types.PaymentRecordType'
        example: 1
      updated_at:
        example: "2023-01-02T12:00:00Z"
        type: string
    required:
    - amount
    - location_id
    - method_id
    - method_name
    - payment_id
    - reference_id
    - source_id
    - source_type
    - status
    - tenant_id
    - type
    type: object
  types.PaymentsReq:
    properties:
      amount:
        example: 10000
        type: integer
      method_id:
        example: method_123456
        type: string
      source_id:
        example: appt_123456789
        type: string
      source_type:
        allOf:
        - $ref: '#/definitions/types.SourceType'
        example: 1
        maximum: 2
        minimum: 1
    required:
    - amount
    - method_id
    - source_id
    - source_type
    type: object
  types.PhoneCapability:
    enum:
    - SMS
    - Voice
    - MMS
    type: string
    x-enum-varnames:
    - PhoneCapabilitySMS
    - PhoneCapabilityVoice
    - PhoneCapabilityMMS
  types.QueryAppointmentGroupByStaffReps:
    properties:
      account_id:
        example: acc_123456
        type: string
      appointment:
        items:
          $ref: '#/definitions/types.AppointmentDetail'
        type: array
      first_name:
        example: John
        type: string
      last_name:
        example: Doe
        type: string
      location_id:
        example: loc_123456
        type: string
      schedules:
        additionalProperties:
          $ref: '#/definitions/types.QueryMultiStaffSchedulesDateResp'
        type: object
      tenant_id:
        example: tenant_123456
        type: string
    type: object
  types.QueryMultiStaffSchedulesDateResp:
    properties:
      account_id:
        example: acc_123456
        type: string
      date:
        description: |-
          AccountFirstName string            `json:"account_first_name" gorm:"column:account_first_name" example:"John"`
          AccountLastName  string            `json:"account_last_name" gorm:"column:account_last_name" example:"Doe"`
        example: "2023-07-01"
        type: string
      is_day_off:
        example: false
        type: boolean
      reason:
        example: holiday
        type: string
      working_times:
        items:
          $ref: '#/definitions/types.WorkingTimeItem'
        type: array
    type: object
  types.QueryStaffSchedulesDateResp:
    properties:
      account_first_name:
        example: John
        type: string
      account_id:
        example: acc_123456
        type: string
      account_last_name:
        example: Doe
        type: string
      working_dates:
        additionalProperties:
          $ref: '#/definitions/types.QueryMultiStaffSchedulesDateResp'
        type: object
    type: object
  types.RefreshReq:
    properties:
      refresh_token:
        example: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
        type: string
    required:
    - refresh_token
    type: object
  types.RefreshResp:
    properties:
      access_token:
        example: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
        type: string
      expire_time:
        example: **********
        type: integer
      expires:
        example: 3600
        type: integer
      location_id:
        example: loc_123456
        type: string
      refresh_token:
        example: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
        type: string
      tenant_id:
        example: tenant_123456
        type: string
    type: object
  types.RegisterReq:
    properties:
      country:
        example: US
        type: string
      currency:
        example: USD
        type: string
      email:
        example: <EMAIL>
        type: string
      first_name:
        example: John
        type: string
      last_name:
        example: Doe
        type: string
      location_name:
        example: Downtown Salon
        type: string
      password:
        example: password123
        minLength: 6
        type: string
      phone_number:
        example: "+***********"
        type: string
      source:
        example: web
        type: string
      timezone:
        example: America/New_York
        type: string
    required:
    - country
    - currency
    - email
    - location_name
    - password
    - timezone
    type: object
  types.RegisterResp:
    properties:
      account_id:
        example: acc_123456
        type: string
      country:
        example: US
        type: string
      currency:
        example: USD
        type: string
      email:
        example: <EMAIL>
        type: string
      first_name:
        example: John
        type: string
      last_name:
        example: Doe
        type: string
      location_id:
        example: loc_123456
        type: string
      location_name:
        example: Downtown Salon
        type: string
      tenant_id:
        example: tenant_123456
        type: string
      timezone:
        example: America/New_York
        type: string
    required:
    - country
    - currency
    type: object
  types.ResetStaffScheduleReq:
    properties:
      account_id:
        example:
        - '["acc_123456"'
        - '"acc_789012"]'
        items:
          type: string
        type: array
    required:
    - account_id
    type: object
  types.SendMessageReq:
    properties:
      client_id:
        example: client_123456
        type: string
      content:
        example: Your appointment is confirmed for tomorrow at 2pm.
        type: string
      to:
        example: "+***********"
        type: string
    required:
    - client_id
    - content
    - to
    type: object
  types.Service:
    properties:
      allow_online_booking:
        example: true
        type: boolean
      bookable_separately:
        example: true
        type: boolean
      category_id:
        example: cat_2viQlkAbQuyb04fe9cF04JP2AOj
        type: string
      color:
        example: '#FF5733'
        type: string
      created_at:
        example: "2023-01-01T12:00:00Z"
        type: string
      description:
        example: Basic haircut service
        type: string
      duration:
        example: 30
        type: integer
      inclusive:
        example: true
        type: boolean
      location_id:
        example: loc_2viQlkAbQuyb04fe9cF04JP2AOj
        type: string
      name:
        example: Haircut
        type: string
      price:
        example: 2500
        type: integer
      sellable_separately:
        example: true
        type: boolean
      service_id:
        example: srv_2viR3b4cZgmpSORwJ127jHKFZHh
        type: string
      service_type:
        allOf:
        - $ref: '#/definitions/types.ServiceType'
        example: 1
      status:
        allOf:
        - $ref: '#/definitions/types.Status'
        example: 1
      tax_id:
        example: tax_2viQlkAbQuyb04fe9cF04JP2AOj
        type: string
      tenant_id:
        example: tenant_2viQlj16EjDj8zLRbgibcidla4i
        type: string
      updated_at:
        example: "2023-01-02T12:00:00Z"
        type: string
    type: object
  types.ServiceCategory:
    properties:
      category_id:
        example: cat_2viQlkAbQuyb04fe9cF04JP2AOj
        type: string
      created_at:
        example: "2023-01-01T12:00:00Z"
        type: string
      location_id:
        example: loc_2viQlkAbQuyb04fe9cF04JP2AOj
        type: string
      name:
        example: Haircut Services
        type: string
      status:
        allOf:
        - $ref: '#/definitions/types.Status'
        example: 1
      tenant_id:
        example: tenant_2viQlj16EjDj8zLRbgibcidla4i
        type: string
      updated_at:
        example: "2023-01-02T12:00:00Z"
        type: string
    type: object
  types.ServiceType:
    enum:
    - 1
    - 2
    type: integer
    x-enum-comments:
      BaseServiceType: Basic service type, a single standalone service
      ComboServiceType: Combo service type, a package containing multiple services
    x-enum-varnames:
    - BaseServiceType
    - ComboServiceType
  types.SourceType:
    enum:
    - 1
    - 2
    type: integer
    x-enum-comments:
      SourceTypeAppointment: Appointment source, payment is for an appointment
      SourceTypeSale: Sale source, payment is for a product sale
    x-enum-varnames:
    - SourceTypeAppointment
    - SourceTypeSale
  types.Staff:
    properties:
      account_id:
        example: acc_123456
        type: string
      email:
        example: <EMAIL>
        type: string
      first_name:
        example: John
        type: string
      is_owner:
        example: true
        type: boolean
      last_name:
        example: Doe
        type: string
      location_id:
        example: loc_123456
        type: string
      phone_number:
        example: "+**********"
        type: string
      status:
        allOf:
        - $ref: '#/definitions/types.Status'
        example: 1
      tenant_id:
        example: tenant_123456
        type: string
    type: object
  types.StaffScheduleHours:
    properties:
      account_id:
        example: acc_123456
        type: string
      first_name:
        example: John
        type: string
      is_owner:
        example: true
        type: boolean
      last_name:
        example: Doe
        type: string
      schedule:
        items:
          $ref: '#/definitions/types.StaffScheduleItem'
        type: array
    type: object
  types.StaffScheduleItem:
    properties:
      is_day_off:
        example: false
        type: boolean
      weekday:
        example: 1
        type: integer
      working_times:
        items:
          $ref: '#/definitions/types.WorkingTimeItem'
        type: array
    type: object
  types.Status:
    enum:
    - 1
    - 2
    type: integer
    x-enum-comments:
      StatusActive: Active status, record is currently active and in use
      StatusInactive: Inactive status, record is currently not active
    x-enum-varnames:
    - StatusActive
    - StatusInactive
  types.TargetType:
    enum:
    - 1
    - 2
    - 3
    type: integer
    x-enum-comments:
      TargetTypeClient: Client type, service is targeted at the primary client
      TargetTypePet: Pet type, service is targeted at a pet
      TargetTypeSubClient: Sub-client type, service is targeted at a secondary client
    x-enum-varnames:
    - TargetTypeClient
    - TargetTypeSubClient
    - TargetTypePet
  types.Tax:
    properties:
      created_at:
        example: "2023-01-01T12:00:00Z"
        type: string
      location_id:
        example: loc_123456
        type: string
      name:
        example: Sales Tax
        type: string
      rate:
        example: 800
        type: integer
      tax_id:
        example: tax_123456
        type: string
      tenant_id:
        example: tenant_123456
        type: string
      updated_at:
        example: "2023-01-02T12:00:00Z"
        type: string
    type: object
  types.Tenant:
    properties:
      country:
        example: US
        type: string
      created_at:
        example: "2023-01-01T12:00:00Z"
        type: string
      currency:
        example: USD
        type: string
      email:
        example: <EMAIL>
        type: string
      name:
        example: Beauty Salon
        type: string
      owner_id:
        example: acc_123456
        type: string
      status:
        allOf:
        - $ref: '#/definitions/types.Status'
        example: 1
      tenant_id:
        example: tenant_123456
        type: string
      timezone:
        example: America/New_York
        type: string
      updated_at:
        example: "2023-01-02T12:00:00Z"
        type: string
    type: object
  types.TwilioSMSInboundWebhook:
    properties:
      AccountSid:
        example: AC123456789abcdef
        type: string
      ApiVersion:
        example: "2010-04-01"
        type: string
      Body:
        example: Hello, I'd like to reschedule my appointment.
        type: string
      From:
        example: "+***********"
        type: string
      MessageSid:
        example: SM123456789abcdef
        type: string
      MessagingServiceSid:
        example: MG123456789abcdef
        type: string
      NumMedia:
        example: "0"
        type: string
      SmsSid:
        example: SM123456789abcdef
        type: string
      SmsStatus:
        description: 仅发送时才有
        example: received
        type: string
      To:
        example: "+***********"
        type: string
    type: object
  types.TwilioSendStatusCallback:
    properties:
      AccountSid:
        example: AC123456789abcdef
        type: string
      ApiVersion:
        example: "2010-04-01"
        type: string
      Body:
        example: Your appointment is confirmed for tomorrow at 2pm.
        type: string
      Direction:
        example: outbound-api
        type: string
      ErrorCode:
        example: "30001"
        type: string
      ErrorMessage:
        example: Queue overflow
        type: string
      From:
        example: "+***********"
        type: string
      MessageSid:
        example: SM123456789abcdef
        type: string
      MessageStatus:
        description: queued, sent, delivered, undelivered, failed
        example: delivered
        type: string
      MessagingServiceSid:
        example: MG123456789abcdef
        type: string
      NumSegments:
        example: "1"
        type: string
      Price:
        example: "-0.00750"
        type: string
      PriceUnit:
        example: USD
        type: string
      SmsSid:
        example: SM123456789abcdef
        type: string
      To:
        example: "+***********"
        type: string
    type: object
  types.UpdateAccountReq:
    properties:
      first_name:
        example: John
        type: string
      last_name:
        example: Doe
        type: string
      phone_number:
        example: "+***********"
        type: string
    type: object
  types.UpdateAppointmentServiceReq:
    properties:
      account_id:
        example: staff_456
        type: string
      duration:
        example: 4500
        type: integer
      price:
        example: 6000
        minimum: 0
        type: integer
      target_id:
        example: client_123
        type: string
      target_type:
        allOf:
        - $ref: '#/definitions/types.TargetType'
        example: 1
        minimum: 0
      tax_rate:
        example: 1000
        maximum: 100000
        minimum: 0
        type: integer
    required:
    - account_id
    - duration
    - price
    - target_id
    type: object
  types.UpdateAppointmentStatusReq:
    properties:
      status:
        allOf:
        - $ref: '#/definitions/types.AppointmentStatusType'
        example: 2
        maximum: 7
        minimum: 0
    required:
    - status
    type: object
  types.UpdateAppointmentTimeReq:
    properties:
      actual_end_date:
        example: "2023-07-01"
        type: string
      actual_end_time:
        example: 39300
        maximum: 86400
        minimum: 0
        type: integer
      actual_start_date:
        example: "2023-07-01"
        type: string
      actual_start_time:
        example: 36300
        maximum: 86400
        minimum: 0
        type: integer
      scheduled_end_date:
        example: "2023-07-01"
        type: string
      scheduled_end_time:
        example: 39600
        maximum: 86400
        minimum: 0
        type: integer
      scheduled_start_date:
        example: "2023-07-01"
        type: string
      scheduled_start_time:
        example: 36000
        maximum: 86400
        minimum: 0
        type: integer
    type: object
  types.UpdateClientReq:
    properties:
      avatar:
        example: https://example.com/avatar.jpg
        type: string
      birthday:
        example: "1990-01-01"
        type: string
      email:
        example: <EMAIL>
        type: string
      first_name:
        example: John
        type: string
      gender:
        allOf:
        - $ref: '#/definitions/types.Gender'
        enum:
        - 1
        - 2
        - 3
        example: 2
      language:
        example: en
        type: string
      last_name:
        example: Doe
        type: string
      phone_number:
        example: "+***********"
        type: string
      source:
        example: web
        type: string
    type: object
  types.UpdateHolidayReq:
    properties:
      date:
        example: "2023-07-01"
        type: string
      description:
        example: Independence Day
        type: string
      is_day_off:
        example: false
        type: boolean
      name:
        example: Independence Day
        type: string
    required:
    - date
    - description
    - is_day_off
    type: object
  types.UpdateLocationReq:
    properties:
      country:
        example: US
        type: string
      currency:
        example: USD
        type: string
      name:
        example: Downtown Salon
        type: string
      timezone:
        example: America/New_York
        type: string
    type: object
  types.UpdatePaymentMethodReq:
    properties:
      name:
        example: Cash
        type: string
      status:
        allOf:
        - $ref: '#/definitions/types.PaymentMethodStatus'
        example: 1
      type:
        allOf:
        - $ref: '#/definitions/types.PaymentMethodTypes'
        example: 1
        maximum: 3
        minimum: 1
    required:
    - name
    - status
    - type
    type: object
  types.UpdateServiceCategoryReq:
    properties:
      name:
        example: Haircut Services
        type: string
    required:
    - name
    type: object
  types.UpdateServiceReq:
    properties:
      allow_online_booking:
        example: true
        type: boolean
      bookable_separately:
        example: true
        type: boolean
      category_id:
        example: cat_123456
        type: string
      color:
        example: '#FF5733'
        type: string
      description:
        example: Basic haircut service
        type: string
      duration:
        example: 30
        type: integer
      inclusive:
        example: true
        type: boolean
      name:
        example: Haircut
        type: string
      price:
        example: 2500
        type: integer
      sellable_separately:
        example: true
        type: boolean
      service_type:
        allOf:
        - $ref: '#/definitions/types.ServiceType'
        example: 1
        maximum: 2
        minimum: 1
      status:
        allOf:
        - $ref: '#/definitions/types.Status'
        example: 1
        maximum: 2
        minimum: 1
      tax_id:
        example: tax_123456
        type: string
    required:
    - category_id
    - duration
    - inclusive
    - name
    - price
    - service_type
    type: object
  types.UpdateServiceStaffReq:
    properties:
      account_ids:
        example:
        - '["acc_123456"'
        - '"acc_234567"]'
        items:
          type: string
        type: array
    required:
    - account_ids
    type: object
  types.UpdateTaxReq:
    properties:
      name:
        example: Sales Tax
        type: string
      rate:
        example: 8
        maximum: 100000
        minimum: 0
        type: number
    type: object
  types.UpdateTenantReq:
    properties:
      country:
        example: US
        type: string
      currency:
        example: USD
        type: string
      name:
        example: Beauty Salon
        type: string
      timezone:
        example: America/New_York
        type: string
    type: object
  types.WorkingTimeItem:
    properties:
      end_time:
        example: 64800
        type: integer
      start_time:
        example: 32400
        type: integer
    type: object
  writer.ErrResp:
    properties:
      code:
        example: 40000
        type: integer
      data:
        additionalProperties:
          type: string
        type: object
      message:
        example: error message
        type: string
      success:
        example: false
        type: boolean
    type: object
  writer.Resp:
    properties:
      code:
        example: 0
        type: integer
      data: {}
      message:
        example: success/error message
        type: string
      success:
        example: true
        type: boolean
    type: object
host: localhost:8081
info:
  contact:
    email: <EMAIL>
    name: API Support
    url: http://www.example.com/support
  description: This is the API documentation for the Pebble application.
  license:
    name: Apache 2.0
    url: http://www.apache.org/licenses/LICENSE-2.0.html
  termsOfService: http://swagger.io/terms/
  title: Pebble API
  version: "1.0"
paths:
  /accounts:
    get:
      consumes:
      - application/json
      description: Get detailed information about the current account
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/writer.Resp'
            - properties:
                data:
                  properties:
                    obj:
                      $ref: '#/definitions/types.Account'
                  type: object
              type: object
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/writer.ErrResp'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/writer.ErrResp'
      security:
      - ApiKeyAuth: []
      summary: Query account information
      tags:
      - accounts
    put:
      consumes:
      - application/json
      description: Update the current account's information
      parameters:
      - description: Account update request
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/types.UpdateAccountReq'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/writer.Resp'
            - properties:
                data:
                  type: object
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/writer.ErrResp'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/writer.ErrResp'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/writer.ErrResp'
      security:
      - ApiKeyAuth: []
      summary: Update account information
      tags:
      - accounts
  /appointments:
    get:
      consumes:
      - application/json
      description: Get a list of all appointments with optional filtering
      parameters:
      - description: Filter by client ID
        in: query
        name: client_id
        type: string
      - description: Filter by staff ID
        in: query
        name: staff_id
        type: string
      - description: Filter by appointment status
        in: query
        name: status
        type: integer
      - description: Filter by start date (YYYY-MM-DD)
        in: query
        name: start_date
        type: string
      - description: Filter by end date (YYYY-MM-DD)
        in: query
        name: end_date
        type: string
      - description: Page number
        in: query
        name: page
        type: integer
      - description: Page size
        in: query
        name: size
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/writer.Resp'
            - properties:
                data:
                  properties:
                    list:
                      items:
                        $ref: '#/definitions/types.AppointmentDetail'
                      type: array
                  type: object
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/writer.ErrResp'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/writer.ErrResp'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/writer.ErrResp'
      security:
      - ApiKeyAuth: []
      summary: List all appointments
      tags:
      - appointments
    post:
      consumes:
      - application/json
      description: Create a new appointment with the provided information
      parameters:
      - description: Appointment information
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/types.CreateAppointmentDetailReq'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/writer.Resp'
            - properties:
                data:
                  properties:
                    obj:
                      $ref: '#/definitions/types.AppointmentDetail'
                  type: object
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/writer.ErrResp'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/writer.ErrResp'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/writer.ErrResp'
      security:
      - ApiKeyAuth: []
      summary: Create a new appointment
      tags:
      - appointments
  /appointments/{appointment_id}:
    delete:
      consumes:
      - application/json
      description: Delete an appointment by ID
      parameters:
      - description: Appointment ID
        in: path
        name: appointment_id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/writer.Resp'
            - properties:
                data:
                  type: object
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/writer.ErrResp'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/writer.ErrResp'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/writer.ErrResp'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/writer.ErrResp'
      security:
      - ApiKeyAuth: []
      summary: Delete an appointment
      tags:
      - appointments
    get:
      consumes:
      - application/json
      description: Get detailed information about a specific appointment
      parameters:
      - description: Appointment ID
        in: path
        name: appointment_id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/writer.Resp'
            - properties:
                data:
                  properties:
                    obj:
                      $ref: '#/definitions/types.AppointmentDetail'
                  type: object
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/writer.ErrResp'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/writer.ErrResp'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/writer.ErrResp'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/writer.ErrResp'
      security:
      - ApiKeyAuth: []
      summary: Get appointment details
      tags:
      - appointments
  /appointments/{appointment_id}/services/{appt_service_id}:
    put:
      consumes:
      - application/json
      description: Update a specific service within an appointment
      parameters:
      - description: Appointment ID
        in: path
        name: appointment_id
        required: true
        type: string
      - description: Appointment Service ID
        in: path
        name: appt_service_id
        required: true
        type: string
      - description: Updated service information
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/types.UpdateAppointmentServiceReq'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/writer.Resp'
            - properties:
                data:
                  type: object
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/writer.ErrResp'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/writer.ErrResp'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/writer.ErrResp'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/writer.ErrResp'
      security:
      - ApiKeyAuth: []
      summary: Update appointment service
      tags:
      - appointments
  /appointments/{appointment_id}/status:
    put:
      consumes:
      - application/json
      description: Update the status of an existing appointment
      parameters:
      - description: Appointment ID
        in: path
        name: appointment_id
        required: true
        type: string
      - description: Updated status information
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/types.UpdateAppointmentStatusReq'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/writer.Resp'
            - properties:
                data:
                  type: object
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/writer.ErrResp'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/writer.ErrResp'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/writer.ErrResp'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/writer.ErrResp'
      security:
      - ApiKeyAuth: []
      summary: Update appointment status
      tags:
      - appointments
  /appointments/{appointment_id}/time:
    put:
      consumes:
      - application/json
      description: Update the time details of an existing appointment
      parameters:
      - description: Appointment ID
        in: path
        name: appointment_id
        required: true
        type: string
      - description: Updated appointment time information
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/types.UpdateAppointmentTimeReq'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/writer.Resp'
            - properties:
                data:
                  type: object
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/writer.ErrResp'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/writer.ErrResp'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/writer.ErrResp'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/writer.ErrResp'
      security:
      - ApiKeyAuth: []
      summary: Update appointment time
      tags:
      - appointments
  /appointments/group-by-staff:
    get:
      consumes:
      - application/json
      description: Get a list of appointments grouped by staff members with optional
        date filtering
      parameters:
      - description: Filter by start date (YYYY-MM-DD)
        in: query
        name: start_date
        type: string
      - description: Filter by end date (YYYY-MM-DD)
        in: query
        name: end_date
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/writer.Resp'
            - properties:
                data:
                  properties:
                    list:
                      items:
                        $ref: '#/definitions/types.QueryAppointmentGroupByStaffReps'
                      type: array
                  type: object
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/writer.ErrResp'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/writer.ErrResp'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/writer.ErrResp'
      security:
      - ApiKeyAuth: []
      summary: List appointments grouped by staff
      tags:
      - appointments
  /auth/login:
    post:
      consumes:
      - application/json
      description: Authenticate a user and return access token
      parameters:
      - description: Login credentials
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/types.LoginReq'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/writer.Resp'
            - properties:
                data:
                  properties:
                    obj:
                      $ref: '#/definitions/types.LoginResp'
                  type: object
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/writer.ErrResp'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/writer.ErrResp'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/writer.ErrResp'
      summary: User login
      tags:
      - auth
  /auth/refresh:
    post:
      consumes:
      - application/json
      description: Refresh an expired access token using a refresh token
      parameters:
      - description: Refresh token
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/types.RefreshReq'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/writer.Resp'
            - properties:
                data:
                  properties:
                    obj:
                      $ref: '#/definitions/types.RefreshResp'
                  type: object
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/writer.ErrResp'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/writer.ErrResp'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/writer.ErrResp'
      summary: Refresh access token
      tags:
      - auth
  /auth/register:
    post:
      consumes:
      - application/json
      description: Register a new user with the provided information
      parameters:
      - description: Registration information
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/types.RegisterReq'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/writer.Resp'
            - properties:
                data:
                  properties:
                    obj:
                      $ref: '#/definitions/types.RegisterResp'
                  type: object
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/writer.ErrResp'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/writer.ErrResp'
      summary: Register a new user
      tags:
      - auth
  /auth/validate:
    post:
      consumes:
      - application/json
      description: Validate if the current token is valid
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/writer.Resp'
            - properties:
                data:
                  properties:
                    obj:
                      additionalProperties:
                        type: string
                      type: object
                  type: object
              type: object
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/writer.ErrResp'
      security:
      - ApiKeyAuth: []
      summary: Validate token
      tags:
      - auth
  /clients:
    get:
      consumes:
      - application/json
      description: Get a list of all clients with optional filtering
      parameters:
      - description: Filter by client first name
        example: '"John"'
        in: query
        name: first_name
        type: string
      - description: Filter by client last name
        example: '"Doe"'
        in: query
        name: last_name
        type: string
      - description: Filter by client email
        example: '"<EMAIL>"'
        in: query
        name: email
        type: string
      - description: Filter by client phone number
        example: '"+***********"'
        in: query
        name: phone_number
        type: string
      - description: Filter by client gender (1-unknown, 2-male, 3-female)
        example: 2
        in: query
        name: gender
        type: integer
      - description: Filter by client birthday (YYYY-MM-DD)
        example: '"1990-01-01"'
        in: query
        name: birthday
        type: string
      - description: Filter by client source
        example: '"web"'
        in: query
        name: source
        type: string
      - description: Search by keyword in name, email, or phone
        example: '"john"'
        in: query
        name: keyword
        type: string
      - description: 'Page number (min: 1)'
        example: 1
        in: query
        name: page
        type: integer
      - description: 'Page size (min: 1, max: 100)'
        example: 20
        in: query
        name: page_size
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/writer.Resp'
            - properties:
                data:
                  properties:
                    list:
                      items:
                        $ref: '#/definitions/types.Client'
                      type: array
                  type: object
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/writer.ErrResp'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/writer.ErrResp'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/writer.ErrResp'
      security:
      - ApiKeyAuth: []
      summary: List all clients
      tags:
      - clients
    post:
      consumes:
      - application/json
      description: Create a new client with the provided information
      parameters:
      - description: Client information
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/types.CreateClientReq'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/writer.Resp'
            - properties:
                data:
                  properties:
                    obj:
                      $ref: '#/definitions/types.Client'
                  type: object
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/writer.ErrResp'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/writer.ErrResp'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/writer.ErrResp'
      security:
      - ApiKeyAuth: []
      summary: Create a new client
      tags:
      - clients
  /clients/{client_id}:
    delete:
      consumes:
      - application/json
      description: Delete a client by ID
      parameters:
      - description: Client ID
        in: path
        name: client_id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/writer.Resp'
            - properties:
                data:
                  type: object
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/writer.ErrResp'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/writer.ErrResp'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/writer.ErrResp'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/writer.ErrResp'
      security:
      - ApiKeyAuth: []
      summary: Delete a client
      tags:
      - clients
    get:
      consumes:
      - application/json
      description: Get detailed information about a specific client
      parameters:
      - description: Client ID
        in: path
        name: client_id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/writer.Resp'
            - properties:
                data:
                  properties:
                    obj:
                      $ref: '#/definitions/types.Client'
                  type: object
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/writer.ErrResp'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/writer.ErrResp'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/writer.ErrResp'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/writer.ErrResp'
      security:
      - ApiKeyAuth: []
      summary: Get client details
      tags:
      - clients
    put:
      consumes:
      - application/json
      description: Update an existing client's information
      parameters:
      - description: Client ID
        in: path
        name: client_id
        required: true
        type: string
      - description: Updated client information
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/types.UpdateClientReq'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/writer.Resp'
            - properties:
                data:
                  type: object
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/writer.ErrResp'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/writer.ErrResp'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/writer.ErrResp'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/writer.ErrResp'
      security:
      - ApiKeyAuth: []
      summary: Update client information
      tags:
      - clients
  /connections/messages:
    post:
      consumes:
      - application/json
      description: Send an SMS message to a client
      parameters:
      - description: Message information
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/types.SendMessageReq'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/writer.Resp'
            - properties:
                data:
                  type: object
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/writer.ErrResp'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/writer.ErrResp'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/writer.ErrResp'
      security:
      - ApiKeyAuth: []
      summary: Send SMS message
      tags:
      - connections
  /connections/phone-numbers:
    delete:
      consumes:
      - application/json
      description: Release a previously purchased phone number
      parameters:
      - description: Phone number to release
        in: body
        name: request
        required: true
        schema:
          type: object
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/writer.Resp'
            - properties:
                data:
                  type: object
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/writer.ErrResp'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/writer.ErrResp'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/writer.ErrResp'
      security:
      - ApiKeyAuth: []
      summary: Release a phone number
      tags:
      - connections
    get:
      consumes:
      - application/json
      description: Search for available phone numbers in a specific country and area
        code
      parameters:
      - description: Country code (e.g., US)
        example: '"US"'
        in: query
        name: country
        required: true
        type: string
      - description: Area code (e.g., 555)
        example: 555
        in: query
        name: area_code
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/writer.Resp'
            - properties:
                data:
                  properties:
                    list:
                      items:
                        $ref: '#/definitions/sms.PhoneNumber'
                      type: array
                  type: object
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/writer.ErrResp'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/writer.ErrResp'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/writer.ErrResp'
      security:
      - ApiKeyAuth: []
      summary: Search for available phone numbers
      tags:
      - connections
    post:
      consumes:
      - application/json
      description: Purchase a new phone number for SMS messaging
      parameters:
      - description: Purchase parameters
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/sms.PurchaseNumberReq'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/writer.Resp'
            - properties:
                data:
                  properties:
                    obj:
                      $ref: '#/definitions/sms.PhoneNumber'
                  type: object
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/writer.ErrResp'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/writer.ErrResp'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/writer.ErrResp'
      security:
      - ApiKeyAuth: []
      summary: Purchase a phone number
      tags:
      - connections
  /holidays:
    get:
      consumes:
      - application/json
      description: QueryHolidaysList
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/writer.Resp'
            - properties:
                data:
                  properties:
                    list:
                      items:
                        $ref: '#/definitions/types.Holiday'
                      type: array
                  type: object
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/writer.ErrResp'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/writer.ErrResp'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/writer.ErrResp'
      summary: QueryHolidaysList
      tags:
      - Holidays
    post:
      consumes:
      - application/json
      description: BatchCreateHoliday
      parameters:
      - description: req
        in: body
        name: req
        required: true
        schema:
          items:
            $ref: '#/definitions/types.CreateHolidayReq'
          type: array
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/writer.Resp'
            - properties:
                data:
                  properties:
                    list:
                      items:
                        $ref: '#/definitions/types.Holiday'
                      type: array
                  type: object
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/writer.ErrResp'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/writer.ErrResp'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/writer.ErrResp'
      summary: BatchCreateHoliday
      tags:
      - Holidays
  /holidays/{holiday_id}:
    delete:
      consumes:
      - application/json
      description: DeleteHoliday
      parameters:
      - description: holiday_id
        in: path
        name: holiday_id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/writer.Resp'
            - properties:
                data:
                  type: object
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/writer.ErrResp'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/writer.ErrResp'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/writer.ErrResp'
      summary: DeleteHoliday
      tags:
      - Holidays
    put:
      consumes:
      - application/json
      description: UpdateHoliday
      parameters:
      - description: holiday_id
        in: path
        name: holiday_id
        required: true
        type: string
      - description: req
        in: body
        name: req
        required: true
        schema:
          $ref: '#/definitions/types.UpdateHolidayReq'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/writer.Resp'
            - properties:
                data:
                  type: object
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/writer.ErrResp'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/writer.ErrResp'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/writer.ErrResp'
      summary: UpdateHoliday
      tags:
      - Holidays
  /locations:
    get:
      consumes:
      - application/json
      description: Get detailed information about the current location
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/writer.Resp'
            - properties:
                data:
                  properties:
                    obj:
                      $ref: '#/definitions/types.Location'
                  type: object
              type: object
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/writer.ErrResp'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/writer.ErrResp'
      security:
      - ApiKeyAuth: []
      summary: Get location details
      tags:
      - locations
    put:
      consumes:
      - application/json
      description: Update the current location's information
      parameters:
      - description: Updated location information
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/types.UpdateLocationReq'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/writer.Resp'
            - properties:
                data:
                  type: object
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/writer.ErrResp'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/writer.ErrResp'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/writer.ErrResp'
      security:
      - ApiKeyAuth: []
      summary: Update location information
      tags:
      - locations
  /locations/bind-phone:
    post:
      consumes:
      - application/json
      description: Purchase and bind a phone number to the current location
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/writer.Resp'
            - properties:
                data:
                  properties:
                    obj:
                      additionalProperties: true
                      type: object
                  type: object
              type: object
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/writer.ErrResp'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/writer.ErrResp'
      security:
      - ApiKeyAuth: []
      summary: Bind phone number to location
      tags:
      - locations
  /payment-methods:
    get:
      consumes:
      - application/json
      description: Get a list of all payment methods
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/writer.Resp'
            - properties:
                data:
                  properties:
                    list:
                      items:
                        $ref: '#/definitions/types.PaymentMethods'
                      type: array
                  type: object
              type: object
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/writer.ErrResp'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/writer.ErrResp'
      security:
      - ApiKeyAuth: []
      summary: List all payment methods
      tags:
      - payment-methods
    post:
      consumes:
      - application/json
      description: Create a new payment method with the provided information
      parameters:
      - description: Payment method information
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/types.CreatePaymentMethodReq'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/writer.Resp'
            - properties:
                data:
                  properties:
                    obj:
                      $ref: '#/definitions/types.PaymentMethods'
                  type: object
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/writer.ErrResp'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/writer.ErrResp'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/writer.ErrResp'
      security:
      - ApiKeyAuth: []
      summary: Create a new payment method
      tags:
      - payment-methods
  /payment-methods/{method_id}:
    delete:
      consumes:
      - application/json
      description: Delete a payment method by ID
      parameters:
      - description: Payment Method ID
        in: path
        name: method_id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/writer.Resp'
            - properties:
                data:
                  type: object
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/writer.ErrResp'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/writer.ErrResp'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/writer.ErrResp'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/writer.ErrResp'
      security:
      - ApiKeyAuth: []
      summary: Delete a payment method
      tags:
      - payment-methods
    get:
      consumes:
      - application/json
      description: Get detailed information about a specific payment method
      parameters:
      - description: Payment Method ID
        in: path
        name: method_id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/writer.Resp'
            - properties:
                data:
                  properties:
                    obj:
                      $ref: '#/definitions/types.PaymentMethods'
                  type: object
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/writer.ErrResp'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/writer.ErrResp'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/writer.ErrResp'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/writer.ErrResp'
      security:
      - ApiKeyAuth: []
      summary: Get payment method details
      tags:
      - payment-methods
    put:
      consumes:
      - application/json
      description: Update an existing payment method with the provided information
      parameters:
      - description: Payment Method ID
        in: path
        name: method_id
        required: true
        type: string
      - description: Updated payment method information
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/types.UpdatePaymentMethodReq'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/writer.Resp'
            - properties:
                data:
                  type: object
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/writer.ErrResp'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/writer.ErrResp'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/writer.ErrResp'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/writer.ErrResp'
      security:
      - ApiKeyAuth: []
      summary: Update payment method
      tags:
      - payment-methods
  /payments:
    post:
      consumes:
      - application/json
      description: Process payment for a specific source (appointment or sale)
      parameters:
      - description: Payment information
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/types.PaymentsReq'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/writer.Resp'
            - properties:
                data:
                  type: object
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/writer.ErrResp'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/writer.ErrResp'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/writer.ErrResp'
      security:
      - ApiKeyAuth: []
      summary: Process payment
      tags:
      - payments
  /service-categories:
    get:
      consumes:
      - application/json
      description: Get a list of all service categories
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/writer.Resp'
            - properties:
                data:
                  properties:
                    list:
                      items:
                        $ref: '#/definitions/types.ServiceCategory'
                      type: array
                  type: object
              type: object
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/writer.ErrResp'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/writer.ErrResp'
      security:
      - ApiKeyAuth: []
      summary: List all service categories
      tags:
      - service-categories
    post:
      consumes:
      - application/json
      description: Create a new service category with the provided information
      parameters:
      - description: Service category information
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/types.CreateServiceCategoryReq'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/writer.Resp'
            - properties:
                data:
                  properties:
                    obj:
                      $ref: '#/definitions/types.ServiceCategory'
                  type: object
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/writer.ErrResp'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/writer.ErrResp'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/writer.ErrResp'
      security:
      - ApiKeyAuth: []
      summary: Create a new service category
      tags:
      - service-categories
  /service-categories/{category_id}:
    delete:
      consumes:
      - application/json
      description: Delete a service category by ID
      parameters:
      - description: Category ID
        in: path
        name: category_id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/writer.Resp'
            - properties:
                data:
                  type: object
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/writer.ErrResp'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/writer.ErrResp'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/writer.ErrResp'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/writer.ErrResp'
      security:
      - ApiKeyAuth: []
      summary: Delete a service category
      tags:
      - service-categories
    get:
      consumes:
      - application/json
      description: Get detailed information about a specific service category
      parameters:
      - description: Category ID
        in: path
        name: category_id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/writer.Resp'
            - properties:
                data:
                  properties:
                    obj:
                      $ref: '#/definitions/types.ServiceCategory'
                  type: object
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/writer.ErrResp'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/writer.ErrResp'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/writer.ErrResp'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/writer.ErrResp'
      security:
      - ApiKeyAuth: []
      summary: Get service category details
      tags:
      - service-categories
    put:
      consumes:
      - application/json
      description: Update an existing service category's information
      parameters:
      - description: Category ID
        in: path
        name: category_id
        required: true
        type: string
      - description: Updated service category information
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/types.UpdateServiceCategoryReq'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/writer.Resp'
            - properties:
                data:
                  type: object
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/writer.ErrResp'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/writer.ErrResp'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/writer.ErrResp'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/writer.ErrResp'
      security:
      - ApiKeyAuth: []
      summary: Update service category
      tags:
      - service-categories
  /services:
    get:
      consumes:
      - application/json
      description: Get a list of all services
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/writer.Resp'
            - properties:
                data:
                  properties:
                    list:
                      items:
                        $ref: '#/definitions/types.Service'
                      type: array
                  type: object
              type: object
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/writer.ErrResp'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/writer.ErrResp'
      security:
      - ApiKeyAuth: []
      summary: List all services
      tags:
      - services
    post:
      consumes:
      - application/json
      description: Create a new service with the provided information
      parameters:
      - description: Service information
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/types.CreateServiceReq'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/writer.Resp'
            - properties:
                data:
                  properties:
                    obj:
                      $ref: '#/definitions/types.Service'
                  type: object
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/writer.ErrResp'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/writer.ErrResp'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/writer.ErrResp'
      security:
      - ApiKeyAuth: []
      summary: Create a new service
      tags:
      - services
  /services/{service_id}:
    delete:
      consumes:
      - application/json
      description: Delete a service by ID
      parameters:
      - description: Service ID
        in: path
        name: service_id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/writer.Resp'
            - properties:
                data:
                  type: object
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/writer.Resp'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/writer.Resp'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/writer.Resp'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/writer.Resp'
      security:
      - ApiKeyAuth: []
      summary: Delete a service
      tags:
      - services
    get:
      consumes:
      - application/json
      description: Get detailed information about a specific service
      parameters:
      - description: Service ID
        in: path
        name: service_id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/writer.Resp'
            - properties:
                data:
                  properties:
                    obj:
                      $ref: '#/definitions/types.Service'
                  type: object
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/writer.ErrResp'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/writer.ErrResp'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/writer.ErrResp'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/writer.ErrResp'
      security:
      - ApiKeyAuth: []
      summary: Get service details
      tags:
      - services
    put:
      consumes:
      - application/json
      description: Update an existing service's information
      parameters:
      - description: Service ID
        in: path
        name: service_id
        required: true
        type: string
      - description: Updated service information
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/types.UpdateServiceReq'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/writer.Resp'
            - properties:
                data:
                  type: object
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/writer.Resp'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/writer.Resp'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/writer.Resp'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/writer.Resp'
      security:
      - ApiKeyAuth: []
      summary: Update service
      tags:
      - services
  /services/{service_id}/staffs:
    put:
      consumes:
      - application/json
      description: Update the staff members assigned to a service
      parameters:
      - description: Service ID
        in: path
        name: service_id
        required: true
        type: string
      - description: Staff assignment information
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/types.UpdateServiceStaffReq'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/writer.Resp'
            - properties:
                data:
                  type: object
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/writer.ErrResp'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/writer.ErrResp'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/writer.ErrResp'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/writer.ErrResp'
      security:
      - ApiKeyAuth: []
      summary: Update service staff assignments
      tags:
      - services
  /staffs:
    get:
      consumes:
      - application/json
      description: Get a list of all staff members with optional filtering
      parameters:
      - description: Filter by staff name
        in: query
        name: name
        type: string
      - description: Filter by staff email
        in: query
        name: email
        type: string
      - description: Page number
        in: query
        name: page
        type: integer
      - description: Page size
        in: query
        name: size
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/writer.Resp'
            - properties:
                data:
                  properties:
                    obj:
                      items:
                        $ref: '#/definitions/types.Staff'
                      type: array
                  type: object
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/writer.Resp'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/writer.Resp'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/writer.Resp'
      security:
      - ApiKeyAuth: []
      summary: List all staff members
      tags:
      - staffs
    post:
      consumes:
      - application/json
      description: Invite a new staff member to join the organization
      parameters:
      - description: Staff invitation information
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/types.InviteStaffReq'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/writer.Resp'
            - properties:
                data:
                  properties:
                    obj:
                      $ref: '#/definitions/types.Staff'
                  type: object
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/writer.Resp'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/writer.Resp'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/writer.Resp'
      security:
      - ApiKeyAuth: []
      summary: Invite a new staff member
      tags:
      - staffs
  /staffs/{account_id}:
    delete:
      consumes:
      - application/json
      description: Remove a staff member from the organization
      parameters:
      - description: Account ID
        in: path
        name: account_id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/writer.Resp'
            - properties:
                data:
                  type: object
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/writer.ErrResp'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/writer.ErrResp'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/writer.ErrResp'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/writer.ErrResp'
      security:
      - ApiKeyAuth: []
      summary: Remove a staff member
      tags:
      - staffs
    get:
      consumes:
      - application/json
      description: Get detailed information about a specific staff member
      parameters:
      - description: Account ID
        in: path
        name: account_id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/writer.Resp'
            - properties:
                data:
                  properties:
                    obj:
                      $ref: '#/definitions/types.Staff'
                  type: object
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/writer.ErrResp'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/writer.ErrResp'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/writer.ErrResp'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/writer.ErrResp'
      security:
      - ApiKeyAuth: []
      summary: Get staff member details
      tags:
      - staffs
  /taxes:
    get:
      consumes:
      - application/json
      description: Get a list of all taxes
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/writer.Resp'
            - properties:
                data:
                  properties:
                    list:
                      items:
                        $ref: '#/definitions/types.Tax'
                      type: array
                  type: object
              type: object
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/writer.ErrResp'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/writer.ErrResp'
      security:
      - ApiKeyAuth: []
      summary: List all taxes
      tags:
      - taxes
    post:
      consumes:
      - application/json
      description: Create a new tax with the provided information
      parameters:
      - description: Tax information
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/types.CreateTaxReq'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/writer.Resp'
            - properties:
                data:
                  properties:
                    obj:
                      $ref: '#/definitions/types.Tax'
                  type: object
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/writer.ErrResp'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/writer.ErrResp'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/writer.ErrResp'
      security:
      - ApiKeyAuth: []
      summary: Create a new tax
      tags:
      - taxes
  /taxes/{tax_id}:
    delete:
      consumes:
      - application/json
      description: Delete a tax by ID
      parameters:
      - description: Tax ID
        in: path
        name: tax_id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/writer.Resp'
            - properties:
                data:
                  type: object
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/writer.ErrResp'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/writer.ErrResp'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/writer.ErrResp'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/writer.ErrResp'
      security:
      - ApiKeyAuth: []
      summary: Delete a tax
      tags:
      - taxes
    get:
      consumes:
      - application/json
      description: Get detailed information about a specific tax
      parameters:
      - description: Tax ID
        in: path
        name: tax_id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/writer.Resp'
            - properties:
                data:
                  properties:
                    obj:
                      $ref: '#/definitions/types.Tax'
                  type: object
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/writer.ErrResp'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/writer.ErrResp'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/writer.Resp'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/writer.Resp'
      security:
      - ApiKeyAuth: []
      summary: Get tax details
      tags:
      - taxes
    put:
      consumes:
      - application/json
      description: Update an existing tax's information
      parameters:
      - description: Tax ID
        in: path
        name: tax_id
        required: true
        type: string
      - description: Updated tax information
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/types.UpdateTaxReq'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/writer.Resp'
            - properties:
                data:
                  type: object
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/writer.ErrResp'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/writer.ErrResp'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/writer.ErrResp'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/writer.ErrResp'
      security:
      - ApiKeyAuth: []
      summary: Update tax information
      tags:
      - taxes
  /tenants:
    get:
      consumes:
      - application/json
      description: Get detailed information about the current tenant
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/writer.Resp'
            - properties:
                data:
                  properties:
                    obj:
                      $ref: '#/definitions/types.Tenant'
                  type: object
              type: object
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/writer.ErrResp'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/writer.ErrResp'
      security:
      - ApiKeyAuth: []
      summary: Get tenant details
      tags:
      - tenants
    put:
      consumes:
      - application/json
      description: Update the current tenant's information
      parameters:
      - description: Updated tenant information
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/types.UpdateTenantReq'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/writer.Resp'
            - properties:
                data:
                  type: object
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/writer.ErrResp'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/writer.ErrResp'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/writer.ErrResp'
      security:
      - ApiKeyAuth: []
      summary: Update tenant information
      tags:
      - tenants
  /twilio-callback/messageing/tenant/{tenant_id}/location/{location_id}/inbound:
    post:
      consumes:
      - application/json
      description: Webhook for Twilio to report incoming SMS messages
      parameters:
      - description: Tenant ID
        in: path
        name: tenant_id
        required: true
        type: string
      - description: Location ID
        in: path
        name: location_id
        required: true
        type: string
      - description: Inbound SMS information
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/types.TwilioSMSInboundWebhook'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/writer.Resp'
            - properties:
                data:
                  type: object
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/writer.ErrResp'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/writer.ErrResp'
      summary: Twilio SMS inbound webhook
      tags:
      - twilio-callback
  /twilio-callback/messageing/tenant/{tenant_id}/location/{location_id}/status:
    post:
      consumes:
      - application/json
      description: Webhook for Twilio to report SMS delivery status
      parameters:
      - description: Tenant ID
        in: path
        name: tenant_id
        required: true
        type: string
      - description: Location ID
        in: path
        name: location_id
        required: true
        type: string
      - description: Status callback information
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/types.TwilioSendStatusCallback'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/writer.Resp'
            - properties:
                data:
                  type: object
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/writer.ErrResp'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/writer.ErrResp'
      summary: Twilio SMS status callback
      tags:
      - twilio-callback
  /working-schedules:
    get:
      consumes:
      - application/json
      description: Retrieve the working hours schedule for all staff members including
        their weekly working time configuration
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/writer.Resp'
            - properties:
                data:
                  properties:
                    list:
                      items:
                        $ref: '#/definitions/types.StaffScheduleHours'
                      type: array
                  type: object
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/writer.ErrResp'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/writer.ErrResp'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/writer.ErrResp'
      security:
      - ApiKeyAuth: []
      summary: Get staff schedule hours
      tags:
      - working-schedules
    put:
      consumes:
      - application/json
      description: 'Update multiple staff schedules for specific weekdays. Weekday
        values: 0=Sunday, 1=Monday, 2=Tuesday, 3=Wednesday, 4=Thursday, 5=Friday,
        6=Saturday. Working times are in seconds from midnight (e.g., 32400=9:00 AM,
        64800=6:00 PM).'
      parameters:
      - description: Staff schedule update information
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/types.BatchUpdateStaffScheduleReq'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/writer.Resp'
            - properties:
                data:
                  type: object
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/writer.ErrResp'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/writer.ErrResp'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/writer.ErrResp'
      security:
      - ApiKeyAuth: []
      summary: Batch update staff schedules
      tags:
      - working-schedules
  /working-schedules/dates:
    get:
      consumes:
      - application/json
      description: Get staff schedules for a specific date range and staff member,
        including any schedule overrides for specific dates. Returns a map where keys
        are dates (YYYY-MM-DD) and values contain the schedule details for that date.
      parameters:
      - description: Start date (YYYY-MM-DD format)
        in: query
        name: start_date
        required: true
        type: string
      - description: End date (YYYY-MM-DD format)
        in: query
        name: end_date
        required: true
        type: string
      - description: Staff account ID
        in: query
        name: account_id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/writer.Resp'
            - properties:
                data:
                  properties:
                    obj:
                      additionalProperties:
                        $ref: '#/definitions/types.QueryStaffSchedulesDateResp'
                      type: object
                  type: object
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/writer.ErrResp'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/writer.ErrResp'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/writer.ErrResp'
      security:
      - ApiKeyAuth: []
      summary: Query staff schedules by date
      tags:
      - working-schedules
    put:
      consumes:
      - application/json
      description: Update multiple staff schedules for specific dates (overrides).
        This creates schedule overrides that take precedence over regular weekly schedules
        for the specified dates. Working times are in seconds from midnight (e.g.,
        32400=9:00 AM, 64800=6:00 PM).
      parameters:
      - description: Staff schedule date override information
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/types.BatchUpdateStaffScheduleOverrideReq'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/writer.Resp'
            - properties:
                data:
                  type: object
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/writer.ErrResp'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/writer.ErrResp'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/writer.ErrResp'
      security:
      - ApiKeyAuth: []
      summary: Batch update staff schedules for specific dates
      tags:
      - working-schedules
  /working-schedules/reset:
    post:
      consumes:
      - application/json
      description: Reset staff schedules to default working hours (Monday-Friday 9:00
        AM to 6:00 PM, weekends off). This will override any existing custom schedules
        for the specified staff members.
      parameters:
      - description: Staff IDs to reset schedules for
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/types.ResetStaffScheduleReq'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/writer.Resp'
            - properties:
                data:
                  type: object
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/writer.ErrResp'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/writer.ErrResp'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/writer.ErrResp'
      security:
      - ApiKeyAuth: []
      summary: Reset staff schedules
      tags:
      - working-schedules
securityDefinitions:
  ApiKeyAuth:
    in: header
    name: Authorization
    type: apiKey
swagger: "2.0"
