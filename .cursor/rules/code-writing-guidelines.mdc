# 代码编写规范

在执行任何代码编写任务之前，你必须遵循以下准则，以确保代码质量、一致性和可维护性。

## 1. 准备阶段：理解与分析

### 1.1. 需求与方案理解
- **浏览技术方案**：在开始编码前，必须仔细阅读和理解 `docs/` 目录下相关的技术方案文档。
- **浏览任务计划**：同时，查阅 `.cursor/plans/` 目录中与当前任务相关的具体执行计划。
- **确认执行内容**：确保你对需要实现的功能、修改的范围和技术选型有清晰、完整的认识。

### 1.2. 代码库熟悉
- **浏览相关代码**：全面浏览项目中所有与任务相关的代码。这包括但不限于：
  - 功能将要影响的模块。
  - 数据模型的定义与使用。
  - 相关的工具函数或服务。
- **把握整体架构**：通过代码浏览，理解现有代码的设计模式、分层结构和核心逻辑。

## 2. 编码阶段：风格与实践

### 2.1. 保持代码风格一致
- **在现有目录中编码**：
  - 当你在一个已经存在的目录中修改或添加文件时，必须参考该目录下其他文件的代码。
  - 重点关注：命名约定、函数/方法结构、注释风格、错误处理方式等。
  - 你的目标是让新代码看起来就像是原作者写的。
- **在新目录中编码**：
  - 如果任务要求在一个全新的目录中创建代码，你必须参考该目录的**同级别其他目录**下的代码。
  - 例如，在 `internal/webapp/services/`下创建一个新功能目录时，应参考 `internal/webapp/services/appointment/` 或 `internal/webapp/services/client/` 等现有目录的结构和代码风格。

### 2.2. 遵循最佳实践
- **遵循 Go 语言规范**：所有 Go 代码都必须遵循官方和社区的最佳实践，例如 `go fmt` 格式化、有效的命名约定和错误处理模式。
- **参考现有实现**：优先复用项目中已有的函数、组件或模式，避免不必要的重复开发。
- **代码质量**：编写清晰、简洁、可读性强的代码，并添加必要的注释来解释复杂的逻辑。

## 3. 验证阶段

- **自我审查**：完成编码后，重新审视你的代码，确保其完全符合技术方案和上述所有规范。
description:
globs:
alwaysApply: false
---
