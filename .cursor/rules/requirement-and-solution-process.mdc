---
description: 
globs: 
alwaysApply: true
---
# 需求与方案制定流程

1. **需求分析前置步骤**：
   - 在开始任何需求分析或方案设计前，必须先浏览项目中所有相关代码，确保对现有实现有充分了解。
   - 当方案中使用到第三方平台、工具或者库时，使用 context7 查看下最新文档，不能使用context7 则联网搜索最新官方文档，一定要确保准确性
   - 参考业界最佳实践，结合项目实际情况，提出合理的技术方案。

2. **方案制定要求**：
   - 必须给出至少一个完整的实现方案，必要时可给出多个可选方案，并对每个方案的优缺点进行详细分析。
   - 方案需结合项目现状、可维护性、扩展性、性能等多维度进行评估。

3. **需求澄清机制**：
   - 如遇需求不明确或存在歧义，必须主动向需求方（用户）提出问题，待需求澄清、确认后再进入方案设计和开发阶段。

4. **需求拆解与计划**：
   - 对于每一个需求，需将整体需求使用To-dos拆解为若干可执行的子任务，并将详细的执行计划存储在 `.cursor/plans/xxx.mdc` 文件中。
   - 每个子任务需明确目标、输入输出等。
   - 每个任务以及子任务完成都要自我验证一遍，确保完全没有问题。

5. **执行前确认机制**：
   - 禁止在未获确认的情况下直接进行代码实现或变更。

6. **方案透明与可追溯**：
   - 所有需求分析、方案制定、执行计划、用户确认等过程，均需在 `.cursor/plans` 目录中有据可查，便于后续追溯和复盘。