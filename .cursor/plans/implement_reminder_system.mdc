# 执行计划：提醒与通知系统 (V1)

## 目标

本计划的目标是指导开发人员（或AI）完成提醒与通知系统V1的实现。V1版本的功能范围严格限定为 **预约前提醒** 和 **客户回访提醒**。

## 前置条件

- 技术方案已在 `docs/reminder_and_notification_plan.md` 中最终确定。
- 开发环境已准备就绪，可访问数据库和代码库。

---

## Phase 1: 数据库与数据模型准备 (Database & Models)

**目标**: 建立数据存储基础，并在代码中创建对应的数据结构。

- [ ] **Task 1.1: 创建 `message_templates` 表**
    - **操作**: 在项目使用的数据库中，执行以下SQL语句。
    - **SQL**:
      ```sql
      CREATE TABLE `message_templates` (
        `id` bigint NOT NULL AUTO_INCREMENT,
        `template_id` varchar(64) NOT NULL,
        `tenant_id` varchar(64) NOT NULL,
        `location_id` varchar(64) NOT NULL DEFAULT '' COMMENT '空字符串表示租户级别的默认模板。指定ID则为该门店的专属模板，会覆盖默认值。',
        `name` varchar(100) NOT NULL COMMENT '模板名称，例如：24小时预约提醒',
        `event_type` varchar(50) NOT NULL COMMENT '事件类型，例如：appointment_reminder, client_recall',
        `channel_type` varchar(20) NOT NULL DEFAULT 'sms' COMMENT '发送渠道，例如：sms, email',
        `subject` varchar(255) DEFAULT NULL COMMENT '消息主题 (用于邮件渠道)',
        `content` TEXT NOT NULL COMMENT '模板正文，可包含占位符，如 {{client_name}}',
        `trigger_config_days` int NOT NULL COMMENT '时间偏移量（天）。对于预约提醒，表示"事件发生前X天"；对于客户召回，表示"最后一次光临后X天"。',
        `status` TINYINT NOT NULL DEFAULT 1 COMMENT '1-激活, 2-禁用。允许商家开启或关闭特定的提醒规则。',
        `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
        `updated_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        `deleted_at` TIMESTAMP NULL DEFAULT NULL,
        PRIMARY KEY (`id`),
        UNIQUE KEY `uk_template_id` (`template_id`),
        KEY `idx_tenant_location_event_channel` (`tenant_id`, `location_id`, `event_type`, `channel_type`)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
      ```
    - **验证**: 确认表已创建，并且所有字段和索引与定义一致。

- [ ] **Task 1.2: 创建 `notification_logs` 表**
    - **操作**: 在数据库中执行以下SQL语句。
    - **SQL**:
      ```sql
      CREATE TABLE `notification_logs` (
        `id` bigint NOT NULL AUTO_INCREMENT,
        `tenant_id` varchar(64) NOT NULL,
        `location_id` varchar(64) NOT NULL,
        `trigger_event_type` varchar(50) NOT NULL COMMENT '触发通知的事件类型，例如：appointment_reminder',
        `trigger_event_id` varchar(64) NOT NULL COMMENT '触发事件的唯一ID，例如：appointment_id 或 client_id',
        `template_id` varchar(64) NOT NULL,
        `channel_type` varchar(20) NOT NULL COMMENT '发送渠道，例如：sms, email',
        `message_record_id` varchar(64) NOT NULL COMMENT '对应渠道日志中的消息ID (例如：sms_message_records.message_id)',
        `sent_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
        PRIMARY KEY (`id`),
        UNIQUE KEY `uk_event_template` (`trigger_event_id`, `template_id`),
        KEY `idx_tenant_location` (`tenant_id`, `location_id`)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
      ```
    - **验证**: 确认表已创建成功。

- [ ] **Task 1.3: 创建 Go 数据模型 (Structs)**
    - **操作**:
        1. 在 `internal/webapp/models/sqlserver/message` 目录下创建新文件 `message_template.go`，定义与 `message_templates` 表对应的 `MessageTemplate` 结构体，并包含 GORM 或其他数据库映射的 tag。
        2. 在 `internal/webapp/models/sqlserver/message` 目录下创建新文件 `notification_log.go`，定义与 `notification_logs` 表对应的 `NotificationLog` 结构体。
        3. 在 `internal/webapp/types/message` 目录下创建对应的数据传输对象（DTOs），用于API的请求和响应。
    - **验证**: 结构体字段与数据库表字段完全对应，类型正确。

## Phase 2: 后端 API 开发 (Backend API)

**目标**: 提供一套完整的 RESTful API，供前端管理消息模板。

- [ ] **Task 2.1: 创建 `MessageTemplate` 服务层**
    - **操作**:
        1. 在 `internal/webapp/services/message` 目录下创建 `message_template.go` 文件。
        2. 在其中定义 `MessageTemplateService` 接口和其实现。
        3. 实现模板的 `Create`, `Get`, `List`, `Update`, `Delete` 等核心业务逻辑函数。这些函数将直接与数据库交互。
    - **验证**: 单元测试覆盖主要业务逻辑。

- [ ] **Task 2.2: 创建 `MessageTemplate` 处理器 (Handler)**
    - **操作**:
        1. 在 `internal/webapp/handler/message` 目录下创建 `message_template.go` 文件。
        2. 在其中定义 `MessageTemplateHandler`，并实现处理HTTP请求的函数。
        3. 这些函数负责解析和验证传入的HTTP请求，调用 `MessageTemplateService` 中的方法，并构造HTTP响应。
    - **验证**: API能够通过工具（如 Postman）成功调用。

- [ ] **Task 2.3: 注册 API 路由**
    - **操作**: 在 `internal/webapp/router.go` 文件中，添加 Phase 2.2 中创建的API端点，将其与对应的处理器函数绑定。
    - **API 列表**:
        - `GET /api/tenants/{tenant_id}/message-templates`
        - `POST /api/tenants/{tenant_id}/message-templates`
        - `POST /api/tenants/{tenant_id}/locations/{location_id}/message-templates`
        - `PUT /api/tenants/{tenant_id}/message-templates/{template_id}`
        - `DELETE /api/tenants/{tenant_id}/message-templates/{template_id}`
    - **验证**: 启动服务后，所有新路由均可访问并返回正确的状态码（即使是错误请求）。

## Phase 3: 后端核心逻辑与接口暴露 (Worker Logic & API)

**目标**: 实现两个核心提醒任务的业务逻辑，并通过内部HTTP接口将其暴露，以便通过外部定时任务（如 Linux Cron）触发。

- [ ] **Task 3.1: 实现 `AppointmentReminder` 核心服务**
    - **操作**:
        1. 在 `internal/webapp/services/workers` 目录下创建 `appointment_reminder_service.go`。
        2. 在该文件中实现一个 `TriggerReminders()` 函数，包含技术方案中描述的**预约提醒任务**的核心逻辑。
        3. 逻辑要点：获取模板 -> 计算时间窗口 -> 查询预约 -> 检查日志 -> 调用通知服务 -> 写回日志。
    - **验证**: 编写单元测试，模拟不同场景，验证逻辑的正确性。

- [ ] **Task 3.2: 实现 `ClientRecall` 核心服务**
    - **操作**:
        1. 在 `internal/webapp/services/workers` 目录下创建 `client_recall_service.go`。
        2. 实现一个 `TriggerRecalls()` 函数，包含技术方案中描述的**客户回访任务**的核心逻辑。
        3. 逻辑要点：获取模板 -> 计算目标日期 -> 聚合查询客户 -> 检查日志 -> 调用通知服务 -> 写回日志。
    - **验证**: 编写单元测试，模拟不同客户消费历史，验证查询逻辑的准确性。

- [ ] **Task 3.3: 创建并暴露 Worker 的 HTTP 接口**
    - **操作**:
        1. 在 `internal/webapp/handler/workers` 目录下创建 `handlers.go`。
        2. 创建两个处理器函数，分别调用 Task 3.1 和 Task 3.2 中实现的服务逻辑。
        3. 在 `internal/webapp/router.go` 中注册两个新的 **内部** API 路由。建议使用 `POST` 方法，并添加安全校验（如基于IP白名单或一个固定的 `X-Internal-Secret` 请求头），防止外部恶意调用。
        4. **建议路由**:
            - `POST /api/internal/workers/trigger-appointment-reminders`
            - `POST /api/internal/workers/trigger-client-recalls`
    - **验证**: 启动服务后，通过工具（如 `curl` 或 Postman）携带正确的安全凭证调用这两个接口，观察应用日志，确认对应的任务逻辑被成功触发并执行。

---
**计划完成**
description:
globs:
alwaysApply: false
---
