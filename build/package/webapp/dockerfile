FROM golang:1.23 as builder
WORKDIR /app

COPY . .

ARG VERSION
ARG GIT_COMMIT
ARG BUILD_DATE

ENV GO_LDFLAGS="-X pebble/pkg/version.Version=${VERSION} \
                -X pebble/pkg/version.GitCommit=${GIT_COMMIT} \
                -X pebble/pkg/version.BuildDate=${BUILD_DATE}"

RUN CGO_ENABLED=0 go build -ldflags "${GO_LDFLAGS}" -o webapp ./cmd/webapp

FROM alpine:latest
WORKDIR /root/
COPY --from=builder /app/webapp .
EXPOSE 8081
CMD ["/root/webapp"]