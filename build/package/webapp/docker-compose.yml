version: '3.9'

services:
  mysql:
    image: mysql:latest
    container_name: mysql-test
    environment:
      MYSQL_ROOT_PASSWORD: root
      MYSQL_DATABASE: test_db
      MYSQL_USER: test_user
      MYSQL_PASSWORD: 123456
      TZ: UTC
    command: --default-time-zone='+00:00'
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql
    networks:
      - app_net
    restart: unless-stopped

  app:
    build:
      context: ../../..
      dockerfile: build/package/webapp/dockerfile
    container_name: webapp-test
    ports:
      - "8081:8081"
    environment:
      DB_HOST: mysql
      DB_PORT: 3306
      DB_USER: test_user
      DB_PASSWORD: 123456
      DB_NAME: test_db
      DB_TIMEZONE: UTC
    depends_on:
      - mysql
    volumes:
      - ../../../configs:/root/configs
      - ../../../logs:/root/logs
    networks:
      - app_net
    restart: unless-stopped

volumes:
  mysql_data:

networks:
  app_net:
    driver: bridge