VERSION_PACKAGE = pebble/pkg/version

VERSION := $(shell git describe --tags --always --match='v*')
GIT_COMMIT:=$(shell git rev-parse HEAD)
BUILD_DATE:=$(shell date -u +'%Y-%m-%dT%H:%M:%SZ')
GO_LDFLAGS += -X $(VERSION_PACKAGE).Version=$(VERSION) \
	-X $(VERSION_PACKAGE).GitCommit=$(GIT_COMMIT) \
	-X $(VERSION_PACKAGE).BuildDate=$(BUILD_DATE)

GO_BUILD_FLAGS += -ldflags "$(GO_LDFLAGS)"
GO_TEST_FLAGS ?= -v -race -coverprofile=coverage.out

ROOT_DIR := $(abspath $(shell pwd))
BUILD_DIR := $(ROOT_DIR)/build
DOCKER_BUILD_DIR := $(ROOT_DIR)/build/package
GOPATH := $(shell go env GOPATH)

.PHONY: all webapp swagger swagger-fmt swagger-serve help
all: update webapp

update:
	git pull

webapp:
	@echo "Building webapp..."
	@CGO_ENABLED=0 go build $(GO_BUILD_FLAGS) -o $(BUILD_DIR)/webapp cmd/webapp/main.go
	@echo "webapp process (PID: $$(pgrep webapp))"
	@pkill webapp || true
	@echo "Running webapp..."
	@nohup ./build/webapp 2>logs/error.log &
	@sleep 1
	@echo "New webapp process started (PID: $$(pgrep webapp))"

webapp-run:
	@echo "webapp process (PID: $$(pgrep webapp))"
	@pkill webapp || true
	@echo "Running webapp..."
	@nohup ./build/webapp 2>logs/error.log &
	@sleep 1
	@echo "New webapp process started (PID: $$(pgrep webapp))"


# webapp-images:
# 	@echo "Building webapp docker images..."
# 	@docker build --build-arg VERSION=$(VERSION) \
#              --build-arg GIT_COMMIT=$(GIT_COMMIT) \
#              --build-arg BUILD_DATE=$(BUILD_DATE) \
#              -t webapp:$(VERSION) \
# 			 -f $(DOCKER_BUILD_DIR)/webapp/dockerfile .
# 	# @echo "Running webapp ..."
# 	# @docker run -d -p 8081:8081 \
#     #        -v $(ROOT_DIR)/configs:/root/configs \
# 	# 	   -v $(ROOT_DIR)/logs:/root/logs \
# 	# 	   --name webapp \
#     #        webapp:$(VERSION)

# webapp-up:
# 	@docker-compose -f $(DOCKER_BUILD_DIR)/webapp/docker-compose.yml up --build

# webapp-build:
# 	@docker-compose -f $(DOCKER_BUILD_DIR)/webapp/docker-compose.yml build

# webapp-down:
# 	@docker-compose -f $(DOCKER_BUILD_DIR)/webapp/docker-compose.yml down

# webapp-ps:
# 	@docker-compose -f $(DOCKER_BUILD_DIR)/webapp/docker-compose.yml ps


clean:
	@echo "Cleaning up..."
	@rm -rf $(BUILD_DIR)/webapp
	@rm -f coverage.out

test:
	@echo "Running tests..."
	@go test $(GO_TEST_FLAGS) ./...

swagger: swagger-webapp

swagger-webapp:
	@echo "Generating Swagger documentation..."
	@if ! command -v swag &> /dev/null; then \
		echo "Installing swag..."; \
		go install github.com/swaggo/swag/cmd/swag@latest; \
	fi
	@mkdir -p api/webapp
	@$(GOPATH)/bin/swag init -g cmd/webapp/main.go -o api/webapp
	@echo "Swagger documentation generated in api/webapp/ directory"
	@echo "View the documentation at http://localhost:8081/swagger/index.html when the server is running"

swagger-fmt:
	@echo "Formatting Swagger comments..."
	@if ! command -v swag &> /dev/null; then \
		echo "Installing swag..."; \
		go install github.com/swaggo/swag/cmd/swag@latest; \
	fi
	@$(GOPATH)/bin/swag fmt
	@echo "Swagger comments formatted"

swagger-serve: swagger
	@echo "Starting server to view Swagger documentation..."
	@go run cmd/webapp/main.go

help:
	@echo "Available commands:"
	@echo "  make all              - Update code from git and build webapp"
	@echo "  make update           - Update code from git"
	@echo "  make webapp           - Build webapp"
	@echo "  make webapp-run       - Run webapp"
	@echo "  make clean            - Clean up build artifacts"
	@echo "  make test             - Run tests"
	@echo "  make swagger          - Generate Swagger documentation to api/webapp directory"
	@echo "  make swagger-webapp   - Same as swagger, generate Swagger documentation to api/webapp directory"
	@echo "  make swagger-fmt      - Format Swagger comments"
	@echo "  make swagger-serve    - Generate Swagger documentation and start server"
	@echo "  make help             - Show this help message"
