#!/bin/bash

# backup.sh - Performs backup operations for the application
# This includes backing up configuration files, logs, and optionally a database.

# -----------------------------------------------------------------------------
# Configuration (override via environment variables)
# -----------------------------------------------------------------------------

# Required
if [ -z "${APP_NAME}" ] || [ -z "${REMOTE_BASE_DIR}" ]; then
    echo "[ERROR] APP_NAME or REMOTE_BASE_DIR is not set."
    exit 1
fi

SCRIPT_DIR="${REMOTE_BASE_DIR}/scripts/webapp"
LOG_DIR="${LOG_DIR:-${REMOTE_BASE_DIR}/logs}"
CONFIG_DIR="${CONFIG_DIR:-${REMOTE_BASE_DIR}/configs}"

# Backup parameters
BACKUP_DIR="${BACKUP_DIR:-backups}"
CONFIG_BACKUP_ENABLED="${CONFIG_BACKUP_ENABLED:-true}"
LOG_BACKUP_ENABLED="${LOG_BACKUP_ENABLED:-true}"
DB_BACKUP_ENABLED="${DB_BACKUP_ENABLED:-false}"
BACKUP_RETENTION_DAYS="${BACKUP_RETENTION_DAYS:-30}"

DB_NAME="${DB_NAME:-}"
DB_USER="${DB_USER:-}"
DB_PASSWORD="${DB_PASSWORD:-}"

# -----------------------------------------------------------------------------

# Construct full paths
BACKUP_DIR_FULL="${REMOTE_BASE_DIR}/${BACKUP_DIR}/${APP_NAME}"
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")

# --- Helper Functions ---

# Function to log messages
log_backup() {
    echo "$(date +'%Y-%m-%d %H:%M:%S') - $1"
}

# --- Main Backup Logic ---

log_backup "Starting backup process for ${APP_NAME}..."

# Create the backup directory if it doesn't exist
mkdir -p "${BACKUP_DIR_FULL}"
log_backup "Backup directory ensured at ${BACKUP_DIR_FULL}"

# 1. Backup configuration files
if [ "${CONFIG_BACKUP_ENABLED}" = true ]; then
    log_backup "Backing up configuration files..."
    if [ -d "${REMOTE_BASE_DIR}/${CONFIG_DIR}" ]; then
        tar -czf "${BACKUP_DIR_FULL}/config_backup_${TIMESTAMP}.tar.gz" -C "${REMOTE_BASE_DIR}" "${CONFIG_DIR}"
        log_backup "Configuration backup created."
    else
        log_backup "Configuration directory not found. Skipping config backup."
    fi
fi

# 2. Backup log files
if [ "${LOG_BACKUP_ENABLED}" = true ]; then
    log_backup "Backing up log files..."
    if [ -d "${LOG_DIR}" ]; then
        tar -czf "${BACKUP_DIR_FULL}/logs_backup_${TIMESTAMP}.tar.gz" -C "${REMOTE_BASE_DIR}" "${LOG_DIR}"
        log_backup "Log backup created."
    else
        log_backup "Log directory not found. Skipping log backup."
    fi
fi

# 3. Backup database (if enabled)
if [ "${DB_BACKUP_ENABLED}" = true ]; then
    log_backup "Backing up database..."
    if [ -n "${DB_NAME}" ]; then
        # Example for MySQL. Adjust command for your database (e.g., pg_dump for PostgreSQL).
        mysqldump -u "${DB_USER}" -p"${DB_PASSWORD}" "${DB_NAME}" | gzip > "${BACKUP_DIR_FULL}/db_backup_${TIMESTAMP}.sql.gz"
        log_backup "Database backup for '${DB_NAME}' created."
    else
        log_backup "DB_NAME is not set. Skipping database backup."
    fi
fi

# 4. Clean up old backups
log_backup "Cleaning up old backups (older than ${BACKUP_RETENTION_DAYS} days)..."
if command -v find >/dev/null; then
    find "${BACKUP_DIR_FULL}" -type f -name "*.tar.gz" -mtime +${BACKUP_RETENTION_DAYS} -exec rm -f {} \;
    find "${BACKUP_DIR_FULL}" -type f -name "*.sql.gz" -mtime +${BACKUP_RETENTION_DAYS} -exec rm -f {} \;
    log_backup "Cleanup complete."
else
    log_backup "Warning: 'find' command not found. Skipping cleanup of old backups."
fi

log_backup "Backup process for ${APP_NAME} finished."

exit 0 