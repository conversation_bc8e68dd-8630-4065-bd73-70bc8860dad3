#!/bin/bash

# setup_cron.sh - Configure monitoring cron job for webapp
# This script sets up a cron job to periodically run the monitoring script
# It checks if the cron job already exists before adding it

set -e

# Get the directory of the current script
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" && pwd )"

# Navigate to project root (two levels up from scripts/webapp)
PROJECT_ROOT="$(cd "${SCRIPT_DIR}/../.." && pwd)"

# -----------------------------------------------------------------------------
# Configuration (override via environment variables)
# -----------------------------------------------------------------------------

APP_NAME="${APP_NAME:-webapp-local}"
CRON_SCHEDULE="${CRON_SCHEDULE:-*/5 * * * *}"
BACKUP_SCHEDULE="${BACKUP_SCHEDULE:-0 2 * * *}"
LOG_DIR_NAME="${LOG_DIR_NAME:-logs}"
CRON_LOG_NAME="${CRON_LOG_NAME:-cron.log}"

# Define full paths with project root
LOG_DIR="${PROJECT_ROOT}/${LOG_DIR_NAME}"
CRON_LOG="${LOG_DIR}/${CRON_LOG_NAME}"
MONITOR_SCRIPT="${SCRIPT_DIR}/monitor.sh"
BACKUP_SCRIPT="${SCRIPT_DIR}/backup.sh"
SYSTEM_HEALTH_SCRIPT="${SCRIPT_DIR}/system_health.sh"

# Ensure log directory exists
mkdir -p "${LOG_DIR}"

# Make sure the scripts are executable
chmod +x "${SCRIPT_DIR}"/*.sh

# Create the monitoring cron job entry
MONITOR_CRON_JOB="${CRON_SCHEDULE} ${MONITOR_SCRIPT} >> /dev/null 2>&1"

# Create the backup cron job entry
BACKUP_CRON_JOB="${BACKUP_SCHEDULE} ${BACKUP_SCRIPT} >> /dev/null 2>&1"

# Setup monitoring cron job
setup_monitoring_cron() {
  # Check if cron job already exists
  if crontab -l 2>/dev/null | grep -q "${MONITOR_SCRIPT}"; then
    echo "Cron job for ${APP_NAME} monitoring already exists"
  else
    echo "Setting up cron job for ${APP_NAME} monitoring"
    
    # Get existing crontab or create empty one
    (crontab -l 2>/dev/null || echo "") | grep -v "${MONITOR_SCRIPT}" > /tmp/crontab.tmp
    
    # Add our cron job
    echo "${MONITOR_CRON_JOB}" >> /tmp/crontab.tmp
    
    # Install the new crontab
    crontab /tmp/crontab.tmp
    rm /tmp/crontab.tmp
    
    echo "Monitoring cron job installed successfully"
  fi
}

# Setup backup cron job
setup_backup_cron() {
  # Check if backup script exists
  if [ ! -f "${BACKUP_SCRIPT}" ]; then
    echo "Backup script not found. Skipping backup cron job setup."
    return 0
  fi
  
  # Check if cron job already exists
  if crontab -l 2>/dev/null | grep -q "${BACKUP_SCRIPT}"; then
    echo "Cron job for ${APP_NAME} backup already exists"
  else
    echo "Setting up cron job for ${APP_NAME} backup"
    
    # Get existing crontab or create empty one
    (crontab -l 2>/dev/null || echo "") | grep -v "${BACKUP_SCRIPT}" > /tmp/crontab.tmp
    
    # Add our cron job
    echo "${BACKUP_CRON_JOB}" >> /tmp/crontab.tmp
    
    # Install the new crontab
    crontab /tmp/crontab.tmp
    rm /tmp/crontab.tmp
    
    echo "Backup cron job installed successfully"
  fi
}

# Verify cron jobs
verify_cron_jobs() {
  local VERIFICATION_STATUS=0
  
  # Verify monitoring cron job
  if crontab -l 2>/dev/null | grep -q "${MONITOR_SCRIPT}"; then
    echo "Verified: Cron job for ${APP_NAME} monitoring is active"
    crontab -l | grep "${MONITOR_SCRIPT}"
  else
    echo "ERROR: Failed to set up monitoring cron job"
    VERIFICATION_STATUS=1
  fi
  
  # Verify backup cron job if backup script exists
  if [ -f "${BACKUP_SCRIPT}" ]; then
    if crontab -l 2>/dev/null | grep -q "${BACKUP_SCRIPT}"; then
      echo "Verified: Cron job for ${APP_NAME} backup is active"
      crontab -l | grep "${BACKUP_SCRIPT}"
    else
      echo "ERROR: Failed to set up backup cron job"
      VERIFICATION_STATUS=1
    fi
  fi
  
  return ${VERIFICATION_STATUS}
}

# Main function
main() {
  echo "Setting up cron jobs for ${APP_NAME}..."
  
  # Setup monitoring cron job
  setup_monitoring_cron
  
  # Setup backup cron job
  setup_backup_cron
  
  # Verify cron jobs
  verify_cron_jobs
  
  echo "Cron job setup completed for ${APP_NAME}"
}

# Run the main function
main

exit $? 