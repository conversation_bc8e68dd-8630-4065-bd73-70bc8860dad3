#!/bin/bash

# setup_cron.sh.tpl - Template for setting up monitoring and backup cron jobs for the application.
# This script ensures that cron jobs are environment-specific and do not conflict.

# Environment-specific configuration - these will be replaced by the CI/CD pipeline
APP_NAME="__APP_NAME__"
REMOTE_BASE_DIR="__REMOTE_BASE_DIR__"

# Derived paths
LOG_DIR="${REMOTE_BASE_DIR}/logs"
CRON_LOG_FILE="${LOG_DIR}/cron.log"
SCRIPTS_DIR="${REMOTE_BASE_DIR}/scripts/webapp"
MONITOR_SCRIPT_PATH="${SCRIPTS_DIR}/monitor.sh"
BACKUP_SCRIPT_PATH="${SCRIPTS_DIR}/backup.sh"

# Cron scheduling (override via environment variables if needed)
CRON_SCHEDULE="${CRON_SCHEDULE:-*/5 * * * *}"
BACKUP_SCHEDULE="${BACKUP_SCHEDULE:-0 2 * * *}"

# Unique identifiers for cron jobs to prevent duplicates and allow updates
MONITOR_JOB_ID="monitor_${APP_NAME}"
BACKUP_JOB_ID="backup_${APP_NAME}"

# --- Helper Function ---
# Function to add or update a cron job
add_or_update_cron_job() {
    local job_id="$1"
    local schedule="$2"
    local command_to_run="$3"
    
    # Get current crontab content, filtering out the job if it already exists
    local current_crontab=$(crontab -l 2>/dev/null | grep -v "# ${job_id}")
    
    # Add the new or updated job to the crontab content
    local new_crontab=$(printf "%s\n%s\n" "${current_crontab}" "# ${job_id}" "${schedule} ${command_to_run}")
    
    # Install the new crontab
    echo "${new_crontab}" | crontab -
    echo "Cron job '${job_id}' has been set."
}

# --- Main Logic ---

echo "--- Setting up cron jobs for ${APP_NAME} ---"

# Ensure log directory and cron log file exist
mkdir -p "${LOG_DIR}"
touch "${CRON_LOG_FILE}"

# 1. Setup Monitoring Cron Job
echo "Setting up monitoring job..."
# The command passes environment variables to the script to ensure it acts on the correct instance
MONITOR_COMMAND="env APP_NAME='${APP_NAME}' REMOTE_BASE_DIR='${REMOTE_BASE_DIR}' ${MONITOR_SCRIPT_PATH} >> ${CRON_LOG_FILE} 2>&1"
add_or_update_cron_job "${MONITOR_JOB_ID}" "${CRON_SCHEDULE}" "${MONITOR_COMMAND}"

# 2. Setup Backup Cron Job
# The backup script should also be made environment-aware
echo "Setting up backup job..."
BACKUP_COMMAND="env APP_NAME='${APP_NAME}' REMOTE_BASE_DIR='${REMOTE_BASE_DIR}' ${BACKUP_SCRIPT_PATH} >> ${CRON_LOG_FILE} 2>&1"
add_or_update_cron_job "${BACKUP_JOB_ID}" "${BACKUP_SCHEDULE}" "${BACKUP_COMMAND}"

echo "--- Cron job setup for ${APP_NAME} completed. ---"
crontab -l 