#!/bin/bash

# ==============================================================================
# Webapp Local Deployment Script (Multi-Environment)
# ==============================================================================
# This script automates the build and local deployment of the webapp for
# different environments (dev, test, prod).
#
# Usage:
#   ./deploy.sh [environment]
#
# Parameters:
#   environment: The target environment. Can be 'dev', 'test', or 'prod'.
#                If not provided, defaults to 'dev'.
#
# It performs the following steps:
#   1. Determines the target environment.
#   2. Gathers version information from Git.
#   3. Compiles the Go application with a name corresponding to the environment.
#   4. Stops any currently running instance of the application for that env.
#   5. Starts the new version, passing the environment name as a flag.
#   6. Verifies that the new process has started successfully.
# ==============================================================================

# Exit immediately if a command exits with a non-zero status.
set -e

# --- Environment Setup ---
ENVIRONMENT=${1:-dev} # Default to 'dev' if no argument is provided

# Validate the environment
if [[ "${ENVIRONMENT}" != "dev" && "${ENVIRONMENT}" != "test" && "${ENVIRONMENT}" != "prod" ]]; then
    echo "❌ Error: Invalid environment specified."
    echo "Usage: $0 [dev|test|prod]"
    exit 1
fi

echo "🚀 Starting deployment for environment: [${ENVIRONMENT}]"
echo ""

# --- Configuration ---
if [[ "${ENVIRONMENT}" == "prod" ]]; then
  APP_NAME="webapp"
else
  APP_NAME="webapp-${ENVIRONMENT}"
fi

VERSION_PACKAGE="pebble/pkg/version"
MAIN_GO_FILE="cmd/webapp/main.go"
LOG_DIR="logs"
ERROR_LOG="${LOG_DIR}/${APP_NAME}_error.log"

# --- Dynamic Build Variables ---
echo "Gathering version information from Git..."
VERSION=$(git describe --tags --always --match='v*')
GIT_COMMIT=$(git rev-parse HEAD)
BUILD_DATE=$(date -u +'%Y-%m-%dT%H:%M:%SZ')

GO_LDFLAGS="-X '${VERSION_PACKAGE}.Version=${VERSION}' \
-X '${VERSION_PACKAGE}.GitCommit=${GIT_COMMIT}' \
-X '${VERSION_PACKAGE}.BuildDate=${BUILD_DATE}'"
echo "  - Version: ${VERSION}"
echo "  - Git Commit: ${GIT_COMMIT}"
echo "  - Build Date: ${BUILD_DATE}"

# --- Paths ---
ROOT_DIR=$(pwd)
BUILD_DIR="${ROOT_DIR}/build"
BINARY_PATH="${BUILD_DIR}/${APP_NAME}"

# --- Main Logic ---
echo ""
echo "----------------------------------------"
echo "Step 1: Building application: ${APP_NAME}"
echo "----------------------------------------"
CGO_ENABLED=0 go build -ldflags "${GO_LDFLAGS}" -o "${BINARY_PATH}" "${MAIN_GO_FILE}"
echo "Build complete. Binary is ready at: ${BINARY_PATH}"
echo ""

echo "----------------------------------------"
echo "Step 2: Restarting application: ${APP_NAME}"
echo "----------------------------------------"
if pgrep -f "${BINARY_PATH}" > /dev/null; then
    echo "Found running '${APP_NAME}' process (PID: $(pgrep -f "${BINARY_PATH}")). Stopping it..."
    pkill -f "${BINARY_PATH}"
    sleep 1
    echo "Old process stopped."
else
    echo "No running '${APP_NAME}' process found. Proceeding with startup."
fi

mkdir -p "${LOG_DIR}"

echo "Starting new version of '${APP_NAME}'..."
# Pass the environment to the application using the --env flag
nohup "${BINARY_PATH}" --env "${ENVIRONMENT}" > /dev/null 2> "${ERROR_LOG}" &

sleep 1

echo ""
echo "----------------------------------------"
echo "Step 3: Verifying deployment for ${APP_NAME}"
echo "----------------------------------------"
if pgrep -f "${BINARY_PATH}" > /dev/null; then
    echo "✅ Success! '${APP_NAME}' for [${ENVIRONMENT}] environment started successfully."
    echo "   - Process ID (PID): $(pgrep -f "${BINARY_PATH}")"
    echo "   - Error logs are being written to: ${ERROR_LOG}"
else
    echo "❌ Error: Failed to start '${APP_NAME}' for [${ENVIRONMENT}] environment."
    echo "   Please check the error log for details: ${ERROR_LOG}"
    exit 1
fi
echo "" 