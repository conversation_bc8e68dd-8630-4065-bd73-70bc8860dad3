#!/bin/bash

# init.sh.tpl - Template for application initialization script
# This script is responsible for setting up and starting the application.
# It ensures that the application is stopped before starting a new version
# and logs the deployment process.

# Environment-specific configuration - these will be replaced by the CI/CD pipeline
APP_NAME="__APP_NAME__"
REMOTE_BASE_DIR="__REMOTE_BASE_DIR__"

# Fixed paths and configurations, derived from the base directory
LOG_DIR="${REMOTE_BASE_DIR}/logs"
APP_LOG="${LOG_DIR}/${APP_NAME}.log"
ERROR_LOG="${LOG_DIR}/${APP_NAME}_error.log"
CONFIG_DIR="${REMOTE_BASE_DIR}/configs"
CONFIG_FILE="${CONFIG_DIR}/webapp.yaml"
BUILD_DIR="${REMOTE_BASE_DIR}/build"
BINARY_PATH="${BUILD_DIR}/${APP_NAME}"

# --- Helper Functions ---

# Function to log messages
log() {
    echo "$(date +'%Y-%m-%d %H:%M:%S') - $1" | tee -a "${LOG_DIR}/deploy.log"
}

# Function to check if a command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# --- Pre-flight Checks ---

# Ensure log directory exists
mkdir -p "${LOG_DIR}"
log "Log directory ensured at ${LOG_DIR}"

# Check for required commands
if ! command_exists pgrep || ! command_exists kill; then
    log "Error: Required commands 'pgrep' or 'kill' not found. Exiting."
    exit 1
fi

# --- Main Logic ---

log "====== Starting deployment for ${APP_NAME} ======"
log "Deployment directory: ${REMOTE_BASE_DIR}"
log "Binary path: ${BINARY_PATH}"
log "Config file: ${CONFIG_FILE}"

# Extract port from the configuration file to ensure it's the single source of truth
APP_PORT=$(sed -n 's/^[[:space:]]*port:[[:space:]]*\([0-9]\{4,5\}\).*/\1/p' "${CONFIG_FILE}")

if [[ -z "$APP_PORT" ]]; then
    log "Error: Could not extract port from ${CONFIG_FILE}. Exiting."
    exit 1
fi
log "Using port ${APP_PORT} extracted from config file."

# Make binary executable
if [ -f "${BINARY_PATH}" ]; then
    chmod +x "${BINARY_PATH}"
    log "Made binary executable: ${BINARY_PATH}"
else
    log "Error: Application binary not found at ${BINARY_PATH}. Deployment failed."
    exit 1
fi

# Stop any currently running instance of the application
log "Checking for running instances of ${APP_NAME}..."
if pgrep -f "${BINARY_PATH}" >/dev/null; then
    log "Stopping existing ${APP_NAME} process..."
    # Attempt graceful shutdown
    pkill -f "${BINARY_PATH}"
    sleep 5 # Wait for graceful shutdown

    # Force kill if still running
    if pgrep -f "${BINARY_PATH}" >/dev/null; then
        log "Process did not stop gracefully. Force killing..."
        pkill -9 -f "${BINARY_PATH}"
        sleep 2
    fi
    log "Old process stopped."
else
    log "No running instances of ${APP_NAME} found. Proceeding with startup."
fi

# Determine environment from APP_NAME (expects suffix like -dev or -test)
APP_ENV=""
if [[ "${APP_NAME}" == *-dev ]]; then
    APP_ENV="dev"
elif [[ "${APP_NAME}" == *-test ]]; then
    APP_ENV="test"
fi

# Start the application with correct flags
log "Starting ${APP_NAME} on port ${APP_PORT}..."
if [[ -n "${APP_ENV}" ]]; then
    nohup "${BINARY_PATH}" --config "${CONFIG_FILE}" --env "${APP_ENV}" > "${APP_LOG}" 2> "${ERROR_LOG}" &
else
    nohup "${BINARY_PATH}" --config "${CONFIG_FILE}" > "${APP_LOG}" 2> "${ERROR_LOG}" &
fi

sleep 3 # Give it a moment to start

# Verify that the application has started
if pgrep -f "${BINARY_PATH}" >/dev/null; then
    log "${APP_NAME} started successfully. PID: $(pgrep -f ${BINARY_PATH})"
    log "====== Deployment for ${APP_NAME} finished successfully ======"
    exit 0
else
    log "Error: Failed to start ${APP_NAME}. Check error log at ${ERROR_LOG}."
    log "====== Deployment for ${APP_NAME} failed ======"
    exit 1
fi 