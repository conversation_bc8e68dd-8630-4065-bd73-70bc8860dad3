#!/bin/bash

# monitor.sh - Health monitoring script for webapp
# This script checks if the webapp is running and restarts it if necessary
# It also performs basic health checks and logs the results

set -e

# -----------------------------------------------------------------------------
# Configuration (all values can be overridden via environment variables)
# -----------------------------------------------------------------------------

# Required environment variables (should be injected by cron job / CI)
if [ -z "${APP_NAME}" ] || [ -z "${REMOTE_BASE_DIR}" ]; then
    echo "[ERROR] APP_NAME or REMOTE_BASE_DIR is not set."
    exit 1
fi

# Derived directories
SCRIPT_DIR="${REMOTE_BASE_DIR}/scripts/webapp"
LOG_DIR="${LOG_DIR:-${REMOTE_BASE_DIR}/logs}"
CONFIG_DIR="${CONFIG_DIR:-${REMOTE_BASE_DIR}/configs}"
BUILD_DIR="${BUILD_DIR:-${REMOTE_BASE_DIR}/build}"

# Monitoring parameters
MONITOR_LOG="${MONITOR_LOG:-monitor.log}"
APP_PORT="${APP_PORT:-8080}"
HEALTH_CHECK_ENDPOINT="${HEALTH_CHECK_ENDPOINT:-/health}"
HEALTH_CHECK_TIMEOUT="${HEALTH_CHECK_TIMEOUT:-10}"

# Construct config file path
CONFIG_FILE="${CONFIG_DIR}/webapp.yaml"

# Dynamically extract port from config file, this is the source of truth
if [[ -f "$CONFIG_FILE" ]]; then
    APP_PORT_FROM_CONFIG=$(sed -n 's/^[[:space:]]*port:[[:space:]]*\([0-9]\{4,5\}\).*/\1/p' "${CONFIG_FILE}")
    if [[ -n "$APP_PORT_FROM_CONFIG" ]]; then
        APP_PORT=$APP_PORT_FROM_CONFIG
    else
        echo "Warning: Could not parse port from $CONFIG_FILE. Falling back to default." >> "${LOG_DIR}/${MONITOR_LOG}"
    fi
else
    echo "Warning: Config file $CONFIG_FILE not found. Falling back to default port." >> "${LOG_DIR}/${MONITOR_LOG}"
fi


# Construct full paths based on the now-defined variables
MONITOR_LOG_FILE="${LOG_DIR}/${MONITOR_LOG}"
BINARY_PATH="${BUILD_DIR}/${APP_NAME}"
HEALTH_CHECK_URL="http://localhost:${APP_PORT}${HEALTH_CHECK_ENDPOINT}"

# Ensure log directory exists
mkdir -p "${LOG_DIR}"

# Timestamp for logs
TIMESTAMP=$(date "+%Y-%m-%d %H:%M:%S")

# Log function
log() {
  echo "[${TIMESTAMP}] $1" >> "${MONITOR_LOG_FILE}"
  echo "$1"
}

# Check if process is running
check_process() {
  if ! pgrep -f "${BINARY_PATH}" > /dev/null; then
    log "WARNING: ${APP_NAME} is not running. Attempting to restart..."
    
    # Run the environment-specific init script to restart the application
    INIT_SCRIPT_SUFFIX="${APP_NAME#webapp-}"
    INIT_SCRIPT_PATH="${SCRIPT_DIR}/init_${INIT_SCRIPT_SUFFIX}.sh"

    if [[ -f "${INIT_SCRIPT_PATH}" ]]; then
        bash "${INIT_SCRIPT_PATH}"
    else
        log "ERROR: Restart script ${INIT_SCRIPT_PATH} not found!"
        return 1
    fi
    
    # Check if restart was successful
    if ! pgrep -f "${BINARY_PATH}" > /dev/null; then
      log "ERROR: Failed to restart ${APP_NAME}"
      return 1
    else
      log "SUCCESS: ${APP_NAME} has been restarted"
    fi
  else
    log "INFO: ${APP_NAME} process is running"
  fi
  
  return 0
}

# Check application health via HTTP endpoint
check_app_health() {
  # Process is running, now check API health if curl is available
  if command -v curl > /dev/null; then
    HTTP_STATUS=$(curl -s -o /dev/null -w "%{http_code}" "${HEALTH_CHECK_URL}" || echo "000")
    
    if [ "${HTTP_STATUS}" = "200" ]; then
      log "INFO: ${APP_NAME} is running and health check passed"
    else
      log "WARNING: ${APP_NAME} is running but health check failed with status ${HTTP_STATUS}. Restarting..."
      
      # Run the environment-specific init script to restart the application
      INIT_SCRIPT_SUFFIX="${APP_NAME#webapp-}"
      INIT_SCRIPT_PATH="${SCRIPT_DIR}/init_${INIT_SCRIPT_SUFFIX}.sh"

      if [[ -f "${INIT_SCRIPT_PATH}" ]]; then
          bash "${INIT_SCRIPT_PATH}"
      else
          log "ERROR: Restart script ${INIT_SCRIPT_PATH} not found!"
          return 1
      fi
      
      # Verify restart was successful
      sleep ${HEALTH_CHECK_TIMEOUT}
      HTTP_STATUS=$(curl -s -o /dev/null -w "%{http_code}" "${HEALTH_CHECK_URL}" || echo "000")
      
      if [ "${HTTP_STATUS}" = "200" ]; then
        log "SUCCESS: ${APP_NAME} has been restarted and health check now passes"
      else
        log "ERROR: ${APP_NAME} was restarted but health check still fails with status ${HTTP_STATUS}"
        return 1
      fi
    fi
  else
    # If curl is not available, just check if process exists
    log "INFO: ${APP_NAME} is running (basic check only, curl not available for health check)"
  fi
  
  return 0
}

# Run system health check
check_system_health() {
  log "INFO: Running system health check..."
  
  # Run the system health check script
  if [ -f "${SCRIPT_DIR}/system_health.sh" ]; then
    bash "${SCRIPT_DIR}/system_health.sh"
    if [ $? -ne 0 ]; then
      log "WARNING: System health check detected issues with the system resources"
      return 1
    fi
  else
    log "WARNING: System health check script not found (${SCRIPT_DIR}/system_health.sh)"
  fi
  
  return 0
}

# Main function to run all monitoring checks
main() {
  log "Starting application monitoring..."
  
  # Track overall monitoring status
  MONITOR_STATUS=0
  
  # Check if the application process is running
  if ! check_process; then
    MONITOR_STATUS=1
  else
    # If process is running, check the application health
    if ! check_app_health; then
      MONITOR_STATUS=1
    fi
  fi
  
  # # Check system health
  # if ! check_system_health; then
  #   MONITOR_STATUS=1
  # fi
  
  if [ ${MONITOR_STATUS} -eq 0 ]; then
    log "Monitoring completed successfully. Application and system are healthy."
  else
    log "Monitoring completed with warnings or errors. Please check the logs for details."
  fi
  
  return ${MONITOR_STATUS}
}

# Run the main function
main

exit $? 