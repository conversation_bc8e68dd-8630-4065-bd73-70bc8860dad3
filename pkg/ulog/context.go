package ulog

import (
	"context"

	"go.uber.org/zap"
)

type LogCtx<PERSON>ey struct{}

const (
	TraceIDKey = "trace_id"
)

func wrapLogger(ctx context.Context) *zap.Logger {
	v := ctx.Value(LogCtxKey{})
	if v == nil {
		return globalLogger
	}
	l, ok := v.(*zap.Logger)
	if !ok {
		return globalLogger
	}

	return l
}

func ContextWithFields(ctx context.Context, fields ...zap.Field) context.Context {
	l := wrapLogger(ctx)
	newLog := l.With(fields...)
	return context.WithValue(ctx, LogCtxKey{}, newLog)
}

func NewContext(newCtx, oldCtx context.Context) context.Context {
	l := wrapLogger(oldCtx)
	return context.WithValue(newCtx, LogCtxKey{}, l)
}
