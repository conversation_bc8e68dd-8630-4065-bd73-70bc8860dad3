package mandrill

import (
	"fmt"

	"github.com/go-resty/resty/v2"
)

type EmailParams struct {
	FromEmail string
	FromName  string
	ToEmails  []string
	Subject   string
	TextBody  string // optional
	HTMLBody  string // optional
}

type EmailResult struct {
	Email        string `json:"email"`
	Status       string `json:"status"`
	RejectReason string `json:"reject_reason,omitempty"`
	QueuedReason string `json:"queued_reason,omitempty"`
	Id           string `json:"_id,omitempty"`
}

func (c *MandrillClient) SendEmail(params EmailParams) ([]EmailResult, error) {
	client := resty.New()

	var recipients []map[string]string
	for _, email := range params.ToEmails {
		recipients = append(recipients, map[string]string{
			"email": email,
			"type":  "to",
		})
	}

	body := map[string]interface{}{
		"key": c.<PERSON>ey,
		"message": map[string]interface{}{
			"from_email":   params.FromEmail,
			"from_name":    params.FromName,
			"to":           recipients,
			"subject":      params.Subject,
			"text":         params.TextBody,
			"html":         params.HTMLBody,
			"track_clicks": true,
			"track_opens":  true,
		},
	}

	var result []EmailResult
	resp, err := client.R().
		SetHeader("Content-Type", "application/json").
		SetBody(body).
		SetResult(&result).
		Post("https://mandrillapp.com/api/1.0/messages/send")

	if err != nil {
		return nil, fmt.Errorf("HTTP error: %v", err)
	}

	if resp.IsError() {
		return nil, fmt.Errorf("Mandrill error: %s", resp.String())
	}

	return result, nil
}

type MergeVar struct {
	Name    string `json:"name"`
	Content string `json:"content"`
}

type Recipient struct {
	Email string     `json:"email"`
	Vars  []MergeVar `json:"vars"`
}

type BatchEmailParams struct {
	FromEmail  string
	FromName   string
	Subject    string
	HTMLBody   string
	TextBody   string
	Recipients []Recipient
}

func (c *MandrillClient) SendBatchEmail(params BatchEmailParams) ([]EmailResult, error) {
	client := resty.New()

	var toList []map[string]string
	var mergeVars []map[string]interface{}

	for _, r := range params.Recipients {
		toList = append(toList, map[string]string{
			"email": r.Email,
			"type":  "to",
		})

		vars := []map[string]string{}
		for _, v := range r.Vars {
			vars = append(vars, map[string]string{
				"name":    v.Name,
				"content": v.Content,
			})
		}

		mergeVars = append(mergeVars, map[string]interface{}{
			"rcpt": r.Email,
			"vars": vars,
		})
	}

	body := map[string]interface{}{
		"key": c.APIKey,
		"message": map[string]interface{}{
			"from_email":     params.FromEmail,
			"from_name":      params.FromName,
			"to":             toList,
			"subject":        params.Subject,
			"html":           params.HTMLBody,
			"text":           params.TextBody,
			"merge":          true,
			"merge_language": "handlebars", // or "mailchimp"
			"merge_vars":     mergeVars,
			"track_opens":    true,
			"track_clicks":   true,
		},
	}

	var result []EmailResult
	resp, err := client.R().
		SetHeader("Content-Type", "application/json").
		SetBody(body).
		SetResult(&result).
		Post("https://mandrillapp.com/api/1.0/messages/send")

	if err != nil {
		return nil, fmt.Errorf("HTTP error: %v", err)
	}

	if resp.IsError() {
		return nil, fmt.Errorf("Mandrill error: %s", resp.String())
	}

	return result, nil
}
