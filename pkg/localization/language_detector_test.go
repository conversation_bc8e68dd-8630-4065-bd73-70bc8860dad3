package localization

import (
	"net/http"
	"testing"
)

func TestParseAcceptLanguage(t *testing.T) {
	tests := []struct {
		name     string
		input    string
		expected []LanguagePreference
	}{
		{
			name:  "Empty string",
			input: "",
			expected: []LanguagePreference{},
		},
		{
			name:  "Single language",
			input: "en-US",
			expected: []LanguagePreference{
				{Language: "en-US", Quality: 1.0},
			},
		},
		{
			name:  "Multiple languages with quality",
			input: "en-US,zh-CN;q=0.9,en;q=0.8",
			expected: []LanguagePreference{
				{Language: "en-US", Quality: 1.0},
				{Language: "zh-CN", Quality: 0.9},
				{Language: "en", Quality: 0.8},
			},
		},
		{
			name:  "Complex header",
			input: "zh-CN,zh;q=0.9,en;q=0.8,en-US;q=0.7",
			expected: []LanguagePreference{
				{Language: "zh-CN", Quality: 1.0},
				{Language: "zh", Quality: 0.9},
				{Language: "en", Quality: 0.8},
				{Language: "en-US", Quality: 0.7},
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := ParseAcceptLanguage(tt.input)
			if len(result) != len(tt.expected) {
				t.Errorf("Expected %d preferences, got %d", len(tt.expected), len(result))
				return
			}
			
			for i, expected := range tt.expected {
				if result[i].Language != expected.Language || result[i].Quality != expected.Quality {
					t.Errorf("Expected %+v, got %+v", expected, result[i])
				}
			}
		})
	}
}

func TestDetectLanguageFromRequest(t *testing.T) {
	supportedLanguages := []string{"en-US", "zh-CN"}
	defaultLanguage := "en-US"

	tests := []struct {
		name               string
		acceptLanguage     string
		expectedLanguage   string
	}{
		{
			name:               "No Accept-Language header",
			acceptLanguage:     "",
			expectedLanguage:   "en-US",
		},
		{
			name:               "Exact match - English",
			acceptLanguage:     "en-US",
			expectedLanguage:   "en-US",
		},
		{
			name:               "Exact match - Chinese",
			acceptLanguage:     "zh-CN",
			expectedLanguage:   "zh-CN",
		},
		{
			name:               "Base language match",
			acceptLanguage:     "en-GB,en;q=0.9",
			expectedLanguage:   "en-US",
		},
		{
			name:               "Chinese with quality",
			acceptLanguage:     "zh-CN,zh;q=0.9,en;q=0.8",
			expectedLanguage:   "zh-CN",
		},
		{
			name:               "Unsupported language fallback",
			acceptLanguage:     "fr-FR,de-DE;q=0.9",
			expectedLanguage:   "en-US",
		},
		{
			name:               "Mixed supported and unsupported",
			acceptLanguage:     "fr-FR,zh-CN;q=0.8,de-DE;q=0.6",
			expectedLanguage:   "zh-CN",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			req, _ := http.NewRequest("GET", "/", nil)
			if tt.acceptLanguage != "" {
				req.Header.Set("Accept-Language", tt.acceptLanguage)
			}
			
			result := DetectLanguageFromRequest(req, supportedLanguages, defaultLanguage)
			if result != tt.expectedLanguage {
				t.Errorf("Expected %s, got %s", tt.expectedLanguage, result)
			}
		})
	}
}

func TestNormalizeLanguageCode(t *testing.T) {
	tests := []struct {
		input    string
		expected string
	}{
		{"en", "en-US"},
		{"en-US", "en-US"},
		{"en_US", "en-US"},
		{"EN-US", "en-US"},
		{"zh", "zh-CN"},
		{"zh-CN", "zh-CN"},
		{"zh_CN", "zh-CN"},
		{"zh-Hans", "zh-CN"},
		{"fr", "en-US"},
		{"unknown", "en-US"},
	}

	for _, tt := range tests {
		t.Run(tt.input, func(t *testing.T) {
			result := NormalizeLanguageCode(tt.input)
			if result != tt.expected {
				t.Errorf("Expected %s, got %s", tt.expected, result)
			}
		})
	}
}