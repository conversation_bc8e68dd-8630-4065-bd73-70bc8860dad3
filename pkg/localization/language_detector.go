package localization

import (
	"net/http"
	"sort"
	"strconv"
	"strings"
)

// LanguagePreference represents a language preference with its quality value
type LanguagePreference struct {
	Language string
	Quality  float64
}

// ParseAcceptLanguage parses the Accept-Language header and returns sorted language preferences
func ParseAcceptLanguage(acceptLanguage string) []LanguagePreference {
	if acceptLanguage == "" {
		return []LanguagePreference{}
	}

	var preferences []LanguagePreference
	
	// Split by comma
	parts := strings.Split(acceptLanguage, ",")
	
	for _, part := range parts {
		part = strings.TrimSpace(part)
		if part == "" {
			continue
		}

		// Split by semicolon to separate language and quality
		langParts := strings.Split(part, ";")
		language := strings.TrimSpace(langParts[0])
		quality := 1.0 // Default quality

		// Parse quality value if present
		if len(langParts) > 1 {
			for _, param := range langParts[1:] {
				param = strings.TrimSpace(param)
				if strings.HasPrefix(param, "q=") {
					if q, err := strconv.ParseFloat(param[2:], 64); err == nil {
						quality = q
					}
				}
			}
		}

		preferences = append(preferences, LanguagePreference{
			Language: language,
			Quality:  quality,
		})
	}

	// Sort by quality (descending)
	sort.Slice(preferences, func(i, j int) bool {
		return preferences[i].Quality > preferences[j].Quality
	})

	return preferences
}

// DetectLanguageFromRequest detects the preferred language from HTTP request
// Returns the first supported language or the default language if none supported
func DetectLanguageFromRequest(r *http.Request, supportedLanguages []string, defaultLanguage string) string {
	if r == nil {
		return defaultLanguage
	}

	acceptLanguage := r.Header.Get("Accept-Language")
	if acceptLanguage == "" {
		return defaultLanguage
	}

	preferences := ParseAcceptLanguage(acceptLanguage)
	supportedMap := make(map[string]bool)
	for _, lang := range supportedLanguages {
		supportedMap[lang] = true
	}

	// Try to find exact match first
	for _, pref := range preferences {
		if supportedMap[pref.Language] {
			return pref.Language
		}
	}

	// Try to find language code match (e.g., "en-US" -> "en")
	for _, pref := range preferences {
		if strings.Contains(pref.Language, "-") {
			baseLanguage := strings.Split(pref.Language, "-")[0]
			if supportedMap[baseLanguage] {
				return baseLanguage
			}
		}
	}

	return defaultLanguage
}

// GetSupportedLanguages returns the list of supported languages for message templates
func GetSupportedLanguages() []string {
	return []string{"en-US", "zh-CN"}
}

// GetDefaultLanguage returns the default language for message templates
func GetDefaultLanguage() string {
	return "en-US"
}

// NormalizeLanguageCode normalizes language codes to our supported format
func NormalizeLanguageCode(langCode string) string {
	switch strings.ToLower(langCode) {
	case "en", "en-us", "en_us":
		return "en-US"
	case "zh", "zh-cn", "zh_cn", "zh-hans":
		return "zh-CN"
	default:
		return GetDefaultLanguage()
	}
}