package localization

import (
	"archive/zip"
	"fmt"
	"path/filepath"
	"runtime"
	"sort"
	"strings"
	"sync"
	"time"
)

type TimezoneInfo struct {
	Name        string `json:"name"`
	Offset      string `json:"offset"`
	DisplayName string `json:"displayName"`
}

var (
	cachedTimezones []TimezoneInfo
	cacheLock       sync.Mutex
	cacheTimestamp  time.Time
)

const cacheDuration = 24 * time.Hour

// isZoneinfoFile checks if a given file path from the zoneinfo.zip is a valid, loadable timezone.
// It filters out directories and non-timezone metadata files.
func isZoneinfoFile(path string) bool {
	// These are metadata or non-timezone files in the zoneinfo database.
	excludedPrefixes := []string{"posix/", "right/", "Etc/GMT", "GMT"}
	for _, prefix := range excludedPrefixes {
		if strings.HasPrefix(path, prefix) {
			return false
		}
	}
	// Skip directories
	if strings.HasSuffix(path, "/") {
		return false
	}
	return true
}

// GetTimezones returns a cached, sorted list of major IANA timezones, using only the Go standard library.
func GetTimezones() ([]TimezoneInfo, error) {
	cacheLock.Lock()
	defer cacheLock.Unlock()

	// Return cached data if it's still fresh
	if time.Since(cacheTimestamp) < cacheDuration && cachedTimezones != nil {
		return cachedTimezones, nil
	}

	zoneinfoPath := filepath.Join(runtime.GOROOT(), "lib/time/zoneinfo.zip")
	zipReader, err := zip.OpenReader(zoneinfoPath)
	if err != nil {
		return nil, fmt.Errorf("failed to open zoneinfo.zip: %w", err)
	}
	defer zipReader.Close()

	var timezones []TimezoneInfo
	for _, file := range zipReader.File {
		tzName := file.Name
		if !isZoneinfoFile(tzName) {
			continue
		}

		loc, err := time.LoadLocation(tzName)
		if err != nil {
			// This check is a safeguard, though `isZoneinfoFile` should prevent most of these.
			continue
		}

		// Get the current time in the location to correctly calculate the current offset, respecting DST.
		t := time.Now().In(loc)
		_, offsetSeconds := t.Zone()
		offsetHours := offsetSeconds / 3600
		offsetMinutes := (offsetSeconds % 3600) / 60

		offsetStr := fmt.Sprintf("%+03d:%02d", offsetHours, offsetMinutes)
		displayName := fmt.Sprintf("(UTC%s) %s", offsetStr, tzName)

		timezones = append(timezones, TimezoneInfo{
			Name:        tzName,
			Offset:      offsetStr,
			DisplayName: displayName,
		})
	}

	// Sort the list alphabetically by timezone name for a consistent and user-friendly order.
	sort.Slice(timezones, func(i, j int) bool {
		return timezones[i].Name < timezones[j].Name
	})

	// Update cache
	cachedTimezones = timezones
	cacheTimestamp = time.Now()

	return cachedTimezones, nil
}
