package localization

// Language represents a language with its code and name
type Language struct {
	Code string `json:"code"` // ISO 639-1 language code
	Name string `json:"name"` // Language name
}

// Common languages with their ISO 639-1 codes
var Languages = map[string]Language{
	"en":  {Code: "en", Name: "English"},
	"zh":  {Code: "zh", Name: "Chinese"},
	"es":  {Code: "es", Name: "Spanish"},
	"hi":  {Code: "hi", Name: "Hindi"},
	"ar":  {Code: "ar", Name: "Arabic"},
	"pt":  {Code: "pt", Name: "Portuguese"},
	"bn":  {Code: "bn", Name: "Bengali"},
	"ru":  {Code: "ru", Name: "Russian"},
	"ja":  {Code: "ja", Name: "Japanese"},
	"pa":  {Code: "pa", Name: "Punjabi"},
	"de":  {Code: "de", Name: "German"},
	"jv":  {Code: "jv", Name: "Javanese"},
	"wu":  {Code: "wu", Name: "Wu Chinese"},
	"ms":  {Code: "ms", Name: "Malay"},
	"te":  {Code: "te", Name: "Telugu"},
	"vi":  {Code: "vi", Name: "Vietnamese"},
	"ko":  {Code: "ko", Name: "Korean"},
	"fr":  {Code: "fr", Name: "French"},
	"mr":  {Code: "mr", Name: "Marathi"},
	"ta":  {Code: "ta", Name: "Tamil"},
	"ur":  {Code: "ur", Name: "Urdu"},
	"tr":  {Code: "tr", Name: "Turkish"},
	"it":  {Code: "it", Name: "Italian"},
	"th":  {Code: "th", Name: "Thai"},
	"gu":  {Code: "gu", Name: "Gujarati"},
	"jin": {Code: "jin", Name: "Jin Chinese"},
	"pl":  {Code: "pl", Name: "Polish"},
	"kn":  {Code: "kn", Name: "Kannada"},
	"ml":  {Code: "ml", Name: "Malayalam"},
	"or":  {Code: "or", Name: "Odia"},
	"my":  {Code: "my", Name: "Burmese"},
	"uk":  {Code: "uk", Name: "Ukrainian"},
	"bh":  {Code: "bh", Name: "Bihari"},
	"su":  {Code: "su", Name: "Sundanese"},
	"uz":  {Code: "uz", Name: "Uzbek"},
	"sd":  {Code: "sd", Name: "Sindhi"},
	"am":  {Code: "am", Name: "Amharic"},
	"nl":  {Code: "nl", Name: "Dutch"},
	"yo":  {Code: "yo", Name: "Yoruba"},
	"rw":  {Code: "rw", Name: "Kinyarwanda"},
	"mg":  {Code: "mg", Name: "Malagasy"},
	"si":  {Code: "si", Name: "Sinhala"},
	"km":  {Code: "km", Name: "Khmer"},
	"tk":  {Code: "tk", Name: "Turkmen"},
	"as":  {Code: "as", Name: "Assamese"},
	"ne":  {Code: "ne", Name: "Nepali"},
	"so":  {Code: "so", Name: "Somali"},
	"ku":  {Code: "ku", Name: "Kurdish"},
	"ha":  {Code: "ha", Name: "Hausa"},
	"ig":  {Code: "ig", Name: "Igbo"},
	"az":  {Code: "az", Name: "Azerbaijani"},
	"kk":  {Code: "kk", Name: "Kazakh"},
	"ky":  {Code: "ky", Name: "Kyrgyz"},
	"tg":  {Code: "tg", Name: "Tajik"},
	"ps":  {Code: "ps", Name: "Pashto"},
	"fa":  {Code: "fa", Name: "Persian"},
	"he":  {Code: "he", Name: "Hebrew"},
	"sw":  {Code: "sw", Name: "Swahili"},
	"zu":  {Code: "zu", Name: "Zulu"},
	"af":  {Code: "af", Name: "Afrikaans"},
	"sq":  {Code: "sq", Name: "Albanian"},
	"be":  {Code: "be", Name: "Belarusian"},
	"bg":  {Code: "bg", Name: "Bulgarian"},
	"ca":  {Code: "ca", Name: "Catalan"},
	"hr":  {Code: "hr", Name: "Croatian"},
	"cs":  {Code: "cs", Name: "Czech"},
	"da":  {Code: "da", Name: "Danish"},
	"et":  {Code: "et", Name: "Estonian"},
	"fi":  {Code: "fi", Name: "Finnish"},
	"ka":  {Code: "ka", Name: "Georgian"},
	"el":  {Code: "el", Name: "Greek"},
	"hu":  {Code: "hu", Name: "Hungarian"},
	"is":  {Code: "is", Name: "Icelandic"},
	"ga":  {Code: "ga", Name: "Irish"},
	"lv":  {Code: "lv", Name: "Latvian"},
	"lt":  {Code: "lt", Name: "Lithuanian"},
	"mk":  {Code: "mk", Name: "Macedonian"},
	"mt":  {Code: "mt", Name: "Maltese"},
	"no":  {Code: "no", Name: "Norwegian"},
	"ro":  {Code: "ro", Name: "Romanian"},
	"sr":  {Code: "sr", Name: "Serbian"},
	"sk":  {Code: "sk", Name: "Slovak"},
	"sl":  {Code: "sl", Name: "Slovenian"},
	"sv":  {Code: "sv", Name: "Swedish"},
	"cy":  {Code: "cy", Name: "Welsh"},
	"eu":  {Code: "eu", Name: "Basque"},
	"gl":  {Code: "gl", Name: "Galician"},
	"lb":  {Code: "lb", Name: "Luxembourgish"},
	"rm":  {Code: "rm", Name: "Romansh"},
	"fo":  {Code: "fo", Name: "Faroese"},
	"kl":  {Code: "kl", Name: "Greenlandic"},
	"gd":  {Code: "gd", Name: "Scottish Gaelic"},
	"br":  {Code: "br", Name: "Breton"},
	"co":  {Code: "co", Name: "Corsican"},
	"oc":  {Code: "oc", Name: "Occitan"},
	"sc":  {Code: "sc", Name: "Sardinian"},
	"vec": {Code: "vec", Name: "Venetian"},
	"lmo": {Code: "lmo", Name: "Lombard"},
	"pms": {Code: "pms", Name: "Piedmontese"},
	"lij": {Code: "lij", Name: "Ligurian"},
	"nap": {Code: "nap", Name: "Neapolitan"},
	"scn": {Code: "scn", Name: "Sicilian"},
}

// LanguageOrder defines the order of languages
var LanguageOrder = []string{
	"en", "zh", "es", "hi", "ar", "pt", "bn", "ru", "ja", "pa", "de", "jv", "wu", "ms", "te", "vi", "ko", "fr", "mr", "ta", "ur", "tr", "it", "th", "gu", "jin", "pl", "kn", "ml", "or", "my", "uk", "bh", "su", "uz", "sd", "am", "nl", "yo", "rw", "mg", "si", "km", "tk", "as", "ne", "so", "ku", "ha", "ig", "az", "kk", "ky", "tg", "ps", "fa", "he", "sw", "zu", "af", "sq", "be", "bg", "ca", "hr", "cs", "da", "et", "fi", "ka", "el", "hu", "is", "ga", "lv", "lt", "mk", "mt", "no", "ro", "sr", "sk", "sl", "sv", "cy", "eu", "gl", "lb", "rm", "fo", "kl", "gd", "br", "co", "oc", "sc", "vec", "lmo", "pms", "lij", "nap", "scn",
	// Add more language codes here as needed, in your preferred order
}

// GetLanguage returns a language by its code
func GetLanguage(code string) (Language, bool) {
	language, exists := Languages[code]
	return language, exists
}

// GetAllLanguages returns all available languages as a slice
func GetAllLanguages() []Language {
	languages := make([]Language, 0, len(LanguageOrder))
	for _, code := range LanguageOrder {
		if language, exists := Languages[code]; exists {
			languages = append(languages, language)
		}
	}
	return languages
}

// GetMajorLanguages returns commonly used major languages
func GetMajorLanguages() []Language {
	majorCodes := []string{"en", "zh", "es", "hi", "ar", "pt", "bn", "ru", "ja", "de", "fr", "ko", "it", "tr", "th"}
	languages := make([]Language, 0, len(majorCodes))
	for _, code := range majorCodes {
		if language, exists := Languages[code]; exists {
			languages = append(languages, language)
		}
	}
	return languages
}

// IsValidLanguageCode checks if a language code is valid
func IsValidLanguageCode(code string) bool {
	_, exists := Languages[code]
	return exists
}
