package paystream

import (
	"errors"
	"fmt"

	"github.com/go-resty/resty/v2"
	"github.com/stripe/stripe-go/v80"
)

type RefundReq struct {
	Amount       int64  `json:"amount"`
	StripeItemId string `json:"stripe_item_id"`
	StripeUserId string `json:"stripe_user_id"`
}

type RefundRes struct {
	StripeItemId string `json:"stripe_item_id"`
}

func Refund(request RefundReq) (*RefundRes, error) {
	var result ObjResp[RefundRes]
	resp, err := resty.New().R().
		SetHeaders(Header()).
		SetBody(request).
		SetResult(&result).Post(Url("/stripe-api/proxy/refund"))
	if err != nil {
		return nil, err
	}
	if resp.IsError() {
		return nil, fmt.Errorf("Refund error: %s", resp.String())
	}
	if result.Code != 200 {
		return nil, fmt.Errorf("Refund error: %s", result.Message)
	}

	return &result.Data.Obj, nil
}

type CreatePaymentIntentWithAccountReq struct {
	TerminalType     int8   `json:"terminal_type"`
	ApplicationFee   int64  `json:"application_fee"`
	Amount           int64  `json:"amount"`
	Currency         string `json:"currency"`
	StripeCustomerId string `json:"stripe_customer_id"`
	StripeUserId     string `json:"stripe_user_id"`
}

func CreatePaymentIntentWithAccount(request CreatePaymentIntentWithAccountReq) (*stripe.PaymentIntent, error) {
	var result ObjResp[stripe.PaymentIntent]
	resp, err := resty.New().R().
		SetHeaders(Header()).
		SetBody(request).
		SetResult(&result).Post(Url("/stripe-api/proxy/payment-intent-with-account"))
	if err != nil {
		return nil, err
	}
	if resp.IsError() {
		return nil, fmt.Errorf("CreatePaymentIntentWithAccount error: %s", resp.String())
	}
	if result.Code != 200 {
		return nil, fmt.Errorf("CreatePaymentIntentWithAccount error: %s", result.Message)
	}

	return &result.Data.Obj, nil
}

type CreatePaymentByCardWithAccountReq struct {
	ApplicationFee   int64  `json:"application_fee"`
	Amount           int64  `json:"amount"`
	Currency         string `json:"currency"`
	StripeCustomerId string `json:"stripe_customer_id"`
	PaymentMethod    string `json:"payment_method"`
	StripeUserId     string `json:"stripe_user_id"`
}

func CreatePaymentByCardWithAccount(request CreatePaymentByCardWithAccountReq) (map[string]interface{}, error) {
	var result ObjResp[map[string]interface{}]

	resp, err := resty.New().R().
		SetHeaders(Header()).
		SetBody(request).
		SetResult(&result).Post(Url("/stripe-api/proxy/payment-by-card-with-account"))
	if err != nil {
		return nil, err
	}
	if resp.IsError() {
		return nil, fmt.Errorf("CreatePaymentByCardWithAccount error: %s", resp.String())
	}
	if result.Code != 200 {
		return nil, fmt.Errorf("CreatePaymentByCardWithAccount error: %s", result.Message)
	}

	return result.Data.Obj, nil
}

type CreateCustomerWithAccountReq struct {
	FirstName     string `json:"first_name"`
	LastName      string `json:"last_name"`
	Email         string `json:"email"`
	StripeAccount string `json:"stripe_account"`
}

type CreateCustomerWithAccountRes struct {
	ID string `json:"id"`
}

func CreateCustomerWithAccount(request CreateCustomerWithAccountReq) (*CreateCustomerWithAccountRes, error) {
	var result ObjResp[CreateCustomerWithAccountRes]
	resp, err := resty.New().R().
		SetHeaders(Header()).
		SetBody(request).
		SetResult(&result).Post(Url("/stripe-api/proxy/create-customer-with-account"))
	if err != nil {
		return nil, err
	}
	if resp.IsError() {
		return nil, fmt.Errorf("CreateCustomerWithAccount error: %s", resp.String())
	}
	if result.Code != 200 {
		return nil, fmt.Errorf("CreateCustomerWithAccount error: %s", result.Message)
	}

	return &result.Data.Obj, nil
}

type GetPaymentMethodReq struct {
	PaymentMethod string `json:"payment_method"`
	StripeAccount string `json:"stripe_account"`
}

func GetPaymentMethod(req GetPaymentMethodReq) (map[string]interface{}, error) {
	var result ObjResp[map[string]interface{}]
	resp, err := resty.New().R().
		SetHeaders(Header()).
		SetResult(&result).Get(Url(fmt.Sprintf("/stripe-api/proxy/get-payment-method?payment_method=%s&stripe_account=%s", req.PaymentMethod, req.StripeAccount)))
	if err != nil {
		return nil, err
	}
	if resp.IsError() {
		return nil, fmt.Errorf("GetPaymentMethod error: %s", resp.String())
	}
	if result.Code != 200 {
		return nil, fmt.Errorf("GetPaymentMethod error: %s", result.Message)
	}

	return result.Data.Obj, nil
}

type GetPaymentIntentReq struct {
	StripePaymentIntentId string `json:"stripe_payment_intent_id"`
	StripeAccount         string `json:"stripe_account"`
}

type GetPaymentIntentRes struct {
	Amount       int64  `json:"amount"`
	TipAmount    int64  `json:"tip_amount"`
	ClientSecret string `json:"client_secret"`
}

func GetPaymentIntent(req GetPaymentIntentReq) (*GetPaymentIntentRes, error) {
	var result ObjResp[GetPaymentIntentRes]
	resp, err := resty.New().R().
		SetHeaders(Header()).
		SetResult(&result).Get(Url(fmt.Sprintf("/stripe-api/proxy/get-payment-intent?stripe_payment_intent_id=%s&stripe_account=%s", req.StripePaymentIntentId, req.StripeAccount)))
	if err != nil {
		return nil, err
	}
	if resp.IsError() {
		return nil, fmt.Errorf("GetPaymentIntent error: %s", resp.String())
	}
	if result.Code != 200 {
		return nil, fmt.Errorf("GetPaymentIntent error: %s", result.Message)
	}

	return &result.Data.Obj, nil
}

type CustomerAttachPaymentMethodWithAccountReq struct {
	StripeCustomerId string `json:"stripe_customer_id"`
	PaymentMethod    string `json:"payment_method"`
	StripeAccount    string `json:"stripe_account"`
}

func CustomerAttachPaymentMethodWithAccount(request CustomerAttachPaymentMethodWithAccountReq) (map[string]interface{}, error) {
	var result ObjResp[map[string]interface{}]

	resp, err := resty.New().R().
		SetHeaders(Header()).
		SetBody(request).
		SetResult(&result).Post(Url("/stripe-api/proxy/customer-attach-payment-method-with-account"))
	if err != nil {
		return nil, err
	}
	if resp.IsError() {
		return nil, fmt.Errorf("CustomerAttachPaymentMethodWithAccount error: %s", resp.String())
	}
	if result.Code != 200 {
		return nil, fmt.Errorf("CustomerAttachPaymentMethodWithAccount error: %s", result.Message)
	}

	return result.Data.Obj, nil
}

type CreateSetUpIntentReq struct {
	StripeAccount string `json:"stripe_account"`
}

type CreateSetUpIntentRes struct {
	ClientSecret string `json:"client_secret"`
}

func CreateSetUpIntent(request CreateSetUpIntentReq) (*CreateSetUpIntentRes, error) {
	var result ObjResp[CreateSetUpIntentRes]
	resp, err := resty.New().R().
		SetHeaders(Header()).
		SetBody(request).
		SetResult(&result).Post(Url("/stripe-api/proxy/create-setup-intent"))
	if err != nil {
		return nil, err
	}
	if resp.IsError() {
		return nil, fmt.Errorf("CreateSetUpIntent error: %s", resp.String())
	}
	if result.Code != 200 {
		return nil, fmt.Errorf("CreateSetUpIntent error: %s", result.Message)
	}

	return &result.Data.Obj, nil
}

type CreateLocationReq struct {
	StripeAccount string `json:"stripe_account"`
	Name          string `json:"name"`
	Address1      string `json:"address_1"`
	City          string `json:"city"`
	State         string `json:"state"`
	Postcode      string `json:"postcode"`
	Country       string `json:"country"`
}

type CreateLocationRes struct {
	Id string `json:"id"`
}

func CreateLocation(request CreateLocationReq) (*CreateLocationRes, error) {
	var result ObjResp[CreateLocationRes]
	resp, err := resty.New().R().
		SetHeaders(Header()).
		SetBody(request).
		SetResult(&result).Post(Url("/stripe-api/proxy/create-location"))
	if err != nil {
		return nil, err
	}
	if resp.IsError() {
		return nil, fmt.Errorf("CreateLocation error: %s", resp.String())
	}
	if result.Code != 200 {
		return nil, errors.New(result.Message)
	}

	return &result.Data.Obj, nil
}

type RegisterReaderReq struct {
	LocationId       string `json:"location_id"`
	RegistrationCode string `json:"registration_code"`
	Name             string `json:"name"`
	StripeAccount    string `json:"stripe_account"`
}

type RegisterReaderRes struct {
	LocationId   string `json:"location_id"`
	LocationName string `json:"location_name"`
	ReaderId     string `json:"reader_id"`
	DeviceType   string `json:"device_type"`
	SerialNumber string `json:"serial_number"`
}

func RegisterReader(request RegisterReaderReq) (*RegisterReaderRes, error) {
	var result ObjResp[RegisterReaderRes]
	resp, err := resty.New().R().
		SetHeaders(Header()).
		SetBody(request).
		SetResult(&result).Post(Url("/stripe-api/proxy/register-reader"))
	if err != nil {
		return nil, err
	}
	if resp.IsError() {
		return nil, fmt.Errorf("RegisterReader error: %s", resp.String())
	}
	if result.Code != 200 {
		return nil, errors.New(result.Message)
	}

	return &result.Data.Obj, nil
}

type ProcessPaymentIntentReq struct {
	ReaderId        string `json:"reader_id"`
	PaymentIntentId string `json:"payment_intent_id"`
	StripeAccount   string `json:"stripe_account"`
}

type ProcessPaymentIntentRes struct {
	Id string `json:"id"`
}

func ProcessPaymentIntent(request ProcessPaymentIntentReq) (*ProcessPaymentIntentRes, error) {
	var result ObjResp[ProcessPaymentIntentRes]
	resp, err := resty.New().R().
		SetHeaders(Header()).
		SetBody(request).
		SetResult(&result).Post(Url("/stripe-api/proxy/process-payment-intent"))
	if err != nil {
		return nil, err
	}
	if resp.IsError() {
		return nil, fmt.Errorf("ProcessPaymentIntent error: %s", resp.String())
	}
	if result.Code != 200 {
		return nil, fmt.Errorf("ProcessPaymentIntent error: %s", result.Message)
	}

	return &result.Data.Obj, nil
}

type CancelReaderActionReq struct {
	ReaderId      string `json:"reader_id"`
	StripeAccount string `json:"stripe_account"`
}

func CancelReaderAction(request CancelReaderActionReq) error {
	var result ObjResp[map[string]interface{}]
	resp, err := resty.New().R().
		SetHeaders(Header()).
		SetBody(request).
		SetResult(&result).Post(Url("/stripe-api/proxy/cancel-action"))
	if err != nil {
		return err
	}
	if resp.IsError() {
		return fmt.Errorf("CancelReaderAction error: %s", resp.String())
	}
	if result.Code != 200 {
		return fmt.Errorf("CancelReaderAction error: %s", result.Message)
	}

	return nil
}

func GetReaderList(connectedAccount string) (*stripe.TerminalReaderList, error) {
	var result struct {
		CommonResp
		Data struct {
			List stripe.TerminalReaderList `json:"list"` // todo
		} `json:"data"`
	}
	resp, err := resty.New().R().
		SetHeaders(Header()).
		SetResult(&result).Get(Url(fmt.Sprintf("/stripe-api/proxy/readers?stripe_account=%s", connectedAccount)))
	if err != nil {
		return nil, err
	}
	if resp.IsError() {
		return nil, fmt.Errorf("GetReaderList error: %s", resp.String())
	}

	if result.Code != 200 {
		return nil, fmt.Errorf("GetReaderList error: %s", result.Message)
	}

	return &result.Data.List, nil
}

type ConfigurationTipsReq struct {
	StripeAccount string  `json:"stripe_account"`
	Percentages   []int64 `json:"percentages"`
}

func ConfigurationTips(request ConfigurationTipsReq) error {
	var result ObjResp[map[string]interface{}]
	resp, err := resty.New().R().
		SetHeaders(Header()).
		SetBody(request).
		SetResult(&result).Post(Url("/stripe-api/proxy/readers/configuration-tips"))
	if err != nil {
		return err
	}
	if resp.IsError() {
		return fmt.Errorf("ConfigurationTips error: %s", resp.String())
	}
	if result.Code != 200 {
		return fmt.Errorf("ConfigurationTips error: %s", result.Message)
	}

	return nil
}

type GetConnectionTokensRes struct {
	Secret string `json:"secret"`
}

func GetConnectionToken(stripeAccount string) (*GetConnectionTokensRes, error) {
	var result ObjResp[GetConnectionTokensRes]
	resp, err := resty.New().R().
		SetHeaders(Header()).
		SetResult(&result).Get(Url(fmt.Sprintf("/stripe-api/proxy/connection-token?stripe_account=%s", stripeAccount)))
	if err != nil {
		return nil, err
	}
	if resp.IsError() {
		return nil, fmt.Errorf("GetConnectionToken error: %s", resp.String())
	}
	if result.Code != 200 {
		return nil, fmt.Errorf("GetConnectionToken error: %s", result.Message)
	}

	return &result.Data.Obj, nil
}
