package paystream

import (
	"fmt"

	"github.com/go-resty/resty/v2"
)

func GetPublicKey() (string, error) {
	type GetPublicKeyRes struct {
		Key string `json:"key"`
	}
	var result ObjResp[GetPublicKeyRes]
	resp, err := resty.New().R().
		SetHeaders(Header()).
		SetResult(&result).Get(Url("/stripe-api/public-key"))
	if err != nil {
		return "", err
	}
	if resp.IsError() {
		return "", fmt.<PERSON><PERSON><PERSON>("GetPublicKey error: %s", resp.String())
	}
	if result.Code != 200 {
		return "", fmt.Errorf("GetPublicKey error: %s", result.Message)
	}

	return result.Data.Obj.Key, nil
}

type CreateStripeBalanceReq struct {
	Email        string `json:"email"`
	Country      string `json:"country"`
	Currency     string `json:"currency"`
	BusinessName string `json:"business_name"`
	BusinessId   int64  `json:"business_id"`
}

type StripeBalances struct {
	Id           int64  `json:"id" gorm:"column:id"`
	AppClient    string `json:"app_client"`
	BusinessId   int64  `json:"business_id" gorm:"column:business_id"`
	StripeUserId string `json:"stripe_user_id" gorm:"column:stripe_user_id"`
	StripeBalancesEntity
	Status     int8  `json:"status" gorm:"column:status;default:1" `
	CreateTime int64 `json:"create_time" gorm:"column:create_time"`
	UpdateTime int64 `json:"update_time" gorm:"column:update_time"`
}

type StripeBalancesEntity struct {
	Balance              int64  `json:"balance" gorm:"column:balance"`
	PayingOutBalance     int64  `json:"paying_out_balance" gorm:"column:paying_out_balance"`
	CanPayoutBalance     int64  `json:"can_payout_balance" gorm:"column:can_payout_balance"`
	AvailableSoonBalance int64  `json:"available_soon_balance" gorm:"-"`
	MerchantBalance      int64  `json:"merchant_balance" gorm:"column:merchant_balance"`
	HoldingBalance       int64  `json:"holding_balance" gorm:"column:holding_balance"`
	BalanceVersion       int64  `json:"balance_version" gorm:"column:balance_version"`
	Currency             string `json:"currency" gorm:"column:currency"`
}

func CreateStripeBalance(request CreateStripeBalanceReq) (*StripeBalances, error) {
	var result ObjResp[StripeBalances]
	resp, err := resty.New().R().
		SetHeaders(Header()).
		SetBody(request).
		SetResult(&result).Post(Url("/stripe-api/balance"))
	if err != nil {
		return nil, err
	}
	if resp.IsError() {
		return nil, fmt.Errorf("CreateStripeBalance error: %s", resp.String())
	}
	if result.Code != 200 {
		return nil, fmt.Errorf("CreateStripeBalance error: %s", result.Message)
	}

	return &result.Data.Obj, nil
}

func GetStripeBalanceByBusinessId(businessId int64) (*StripeBalances, error) {
	var result ObjResp[StripeBalances]
	resp, err := resty.New().R().
		SetHeaders(Header()).
		SetResult(&result).Get(Url(fmt.Sprintf("/stripe-api/balance/%d", businessId)))
	if err != nil {
		return nil, err
	}
	if resp.IsError() {
		return nil, fmt.Errorf("GetStripeBalanceByBusinessId error: %s", resp.String())
	}
	if result.Code != 200 {
		return nil, fmt.Errorf("GetStripeBalanceByBusinessId error: %s", result.Message)
	}

	return &result.Data.Obj, nil
}

type GetStripeBalancePayoutLogsReq struct {
	BusinessId int64 `json:"business_id"`
	Page       int64 `json:"page"`
	PageSize   int64 `json:"page_size"`
}

type GetStripeBalancePayoutLogsRes struct {
	Id         int64  `json:"id"`
	Amount     int64  `json:"amount"`
	Currency   string `json:"currency"`
	Method     int8   `json:"method"`
	Status     int8   `json:"status"`
	CreateTime int64  `json:"create_time"`
	EstTime    int64  `json:"est_time"`
}

func GetStripeBalancePayoutLogs(req GetStripeBalancePayoutLogsReq) ([]GetStripeBalancePayoutLogsRes, int64, error) {
	var result ListResp[GetStripeBalancePayoutLogsRes]
	resp, err := resty.New().R().
		SetHeaders(Header()).
		SetResult(&result).Get(Url(fmt.Sprintf("/stripe-api/balance/%d/payout_records?page=%d&page_size=%d", req.BusinessId, req.Page, req.PageSize)))
	if err != nil {
		return nil, 0, err
	}

	if resp.IsError() {
		return nil, 0, fmt.Errorf("GetStripeBalancePayoutLogs error: %s", resp.String())
	}

	if result.Code != 200 {
		return nil, 0, fmt.Errorf("GetStripeBalancePayoutLogs error: %s", result.Message)
	}

	return result.Data.List, result.Data.Total, nil
}

type GetStripeBalancePaymentLogsReq struct {
	BusinessId int64 `json:"business_id"`
	Page       int64 `json:"page"`
	PageSize   int64 `json:"page_size"`
}

type GetStripeBalancePaymentLogsRes struct {
	Id           int64  `json:"id"`
	StripeItemId string `json:"stripe_item_id"`
	Amount       int64  `json:"amount"`
	RefundAmount int64  `json:"refund_amount"`
	Currency     string `json:"currency"`
	OrderId      int64  `json:"order_id"`
	OrderType    int8   `json:"order_type"`
	Status       int8   `json:"status"`
	Method       int8   `json:"method"`
	CustomerId   int64  `json:"customer_id"`
	CustomerName string `json:"customer_name"`
	CreateTime   int64  `json:"create_time"`
}

func GetStripeBalancePaymentLogs(req GetStripeBalancePaymentLogsReq) ([]GetStripeBalancePaymentLogsRes, int64, error) {
	var result ListResp[GetStripeBalancePaymentLogsRes]
	resp, err := resty.New().R().
		SetHeaders(Header()).
		SetResult(&result).Get(Url(fmt.Sprintf("/stripe-api/balance/%d/payment_records?page=%d&page_size=%d", req.BusinessId, req.Page, req.PageSize)))
	if err != nil {
		return nil, 0, err
	}

	if resp.IsError() {
		return nil, 0, fmt.Errorf("GetStripeBalancePaymentLogs error: %s", resp.String())
	}

	if result.Code != 200 {
		return nil, 0, fmt.Errorf("GetStripeBalancePaymentLogs error: %s", result.Message)
	}

	return result.Data.List, result.Data.Total, nil
}

type UpdateStripeBalanceAndPaymentLogsReq struct {
	Amount                int64  `json:"amount"`
	PaymentId             string `json:"payment_id"`
	OrderId               int64  `json:"order_id"`
	OrderType             int8   `json:"order_type"`
	CustomerId            int64  `json:"customer_id"`
	ApplicationFee        int64  `json:"application_fee"`
	ApplicationFeePercent int64  `json:"application_fee_percent"`
	PaymentFee            int64  `json:"payment_fee"`
	PaymentFeePercent     int64  `json:"payment_fee_percent"`
	PaymentBaseFee        int64  `json:"payment_base_fee"`
	Currency              string `json:"currency"`
	StripeUserId          string `json:"stripe_user_id"`
	BusinessId            int64  `json:"business_id"`
	LocationId            int64  `json:"location_id"`
	LastAccountId         int64  `json:"last_account_id"`
}

func UpdateStripeBalanceAndPaymentLogs(request UpdateStripeBalanceAndPaymentLogsReq) error {
	var result ObjResp[map[string]interface{}]
	resp, err := resty.New().R().
		SetHeaders(Header()).
		SetBody(request).
		SetResult(&result).Post(Url(fmt.Sprintf("/stripe-api/balance/%d/payments", request.BusinessId)))
	if err != nil {
		return err
	}

	if resp.IsError() {
		return fmt.Errorf("UpdateStripeBalanceAndPaymentLogs error: %s", resp.String())
	}

	if result.Code != 200 {
		return fmt.Errorf("UpdateStripeBalanceAndPaymentLogs error: %s", result.Message)
	}

	return nil
}

type StripeBalancePayoutReq struct {
	Amount        int64  `json:"amount"`
	StripeUserId  string `json:"stripe_user_id"`
	PayoutFee     int64  `json:"payout_fee"`
	Currency      string `json:"currency"`
	BusinessId    int64  `json:"business_id"`
	LocationId    int64  `json:"location_id"`
	LastAccountId int64  `json:"last_account_id"`
}

func StripeBalancePayout(request StripeBalancePayoutReq) error {
	var result ObjResp[map[string]interface{}]
	resp, err := resty.New().R().
		SetHeaders(Header()).
		SetBody(request).
		SetResult(&result).Post(Url(fmt.Sprintf("/stripe-api/balance/%d/payouts", request.BusinessId)))
	if err != nil {
		return err
	}

	if resp.IsError() {
		return fmt.Errorf("StripeBalancePayout error: %s", resp.String())
	}

	if result.Code != 200 {
		return fmt.Errorf("StripeBalancePayout error: %s", result.Message)
	}

	return nil
}

type UpdateStripeRefundBalanceReq struct {
	Amount        int64  `json:"amount"`
	PaymentId     string `json:"payment_id"`
	RefundId      string `json:"refund_id"`
	Currency      string `json:"currency"`
	StripeUserId  string `json:"stripe_user_id"`
	BusinessId    int64  `json:"business_id"`
	LocationId    int64  `json:"location_id"`
	LastAccountId int64  `json:"last_account_id"`
	// ApptId     int64  `json:"appt_id"`
	// TicketId   int64  `json:"ticket_id"`
	// SaleId     int64  `json:"sale_id"`
	// CustomerId int64  `json:"customer_id"`
}

func StripeBalanceRefund(request UpdateStripeRefundBalanceReq) error {
	var result ObjResp[map[string]interface{}]
	resp, err := resty.New().R().
		SetHeaders(Header()).
		SetBody(request).
		SetResult(&result).Post(Url(fmt.Sprintf("/stripe-api/balance/%d/refunds", request.BusinessId)))
	if err != nil {
		return err
	}

	if resp.IsError() {
		return fmt.Errorf("StripeBalanceRefund error: %s", resp.String())
	}

	if result.Code != 200 {
		return fmt.Errorf("StripeBalanceRefund error: %s", result.Message)
	}

	return nil
}

type GetConnectedAccountLinkRes struct {
	URL string `json:"url"`
}

func GetStripeConnectLink(businessId int64) (*GetConnectedAccountLinkRes, error) {
	var result ObjResp[GetConnectedAccountLinkRes]
	resp, err := resty.New().R().
		SetHeaders(Header()).
		SetResult(&result).Get(Url(fmt.Sprintf("/stripe-api/balance/%d/connect_links", businessId)))
	if err != nil {
		return nil, err
	}

	if resp.IsError() {
		return nil, fmt.Errorf("GetStripeConnectLink error: %s", resp.String())
	}

	if result.Code != 200 {
		return nil, fmt.Errorf("GetStripeConnectLink error: %s", result.Message)
	}

	return &result.Data.Obj, nil
}

type GetConnectedAccountStatusRes struct {
	PayoutsEnabled  int8 `json:"payouts_enabled" gorm:"-"`
	PaymentsEnabled int8 `json:"payments_enabled" gorm:"-"`
}

func GetStripeConnectStatus(businessId int64) (*GetConnectedAccountStatusRes, error) {
	var result ObjResp[GetConnectedAccountStatusRes]
	resp, err := resty.New().R().
		SetHeaders(Header()).
		SetResult(&result).Get(Url(fmt.Sprintf("/stripe-api/balance/%d/connect_status", businessId)))
	if err != nil {
		return nil, err
	}

	if resp.IsError() {
		return nil, fmt.Errorf("GetStripeConnectStatus error: %s", resp.String())
	}

	if result.Code != 200 {
		return nil, fmt.Errorf("GetStripeConnectStatus error: %s", result.Message)
	}

	return &result.Data.Obj, nil
}
