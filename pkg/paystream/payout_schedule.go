package paystream

import (
	"fmt"

	"github.com/go-resty/resty/v2"
)

const (
	//0-manual 1-monthly 2-weekly 3-daily
	Manual  = 0
	Monthly = 1
	Weekly  = 2
	Daily   = 3
)

type PayoutSchduleParams struct {
	Method       int64  `json:"method"`
	MethodParams int64  `json:"method_params"`
	Timezone     string `json:"timezone"`
	PayoutFee    int64  `json:"payout_fee"`
}

func UpdatePayoutSchedule(businessId int64, params PayoutSchduleParams) error {
	var result CommonResp
	resp, err := resty.New().R().
		SetHeaders(Header()).
		SetBody(params).
		SetResult(&result).Put(Url(fmt.Sprintf("/stripe-api/payout-schedules/%d", businessId)))
	if err != nil {
		return err
	}
	if resp.IsError() {
		return fmt.Errorf("UpdatePayoutSchedule error: %s", resp.String())
	}
	if result.Code != 200 {
		return fmt.Errorf("UpdatePayoutSchedule error: %s", result.Message)
	}

	return nil
}

type PayoutSchedule struct {
	Id               int64  `json:"id"`
	StripeUserId     string `json:"stripe_user_id"`
	BusinessID       int64  `json:"business_id"`
	Method           int64  `json:"method"`
	MethodParams     int64  `json:"method_params"`
	Timezone         string `json:"timezone"`
	NextScheduleTime int64  `json:"next_schedule_time"`
	PayoutFee        int64  `json:"payout_fee"`
	AppClient        string `json:"app_client"`
	CreateTime       int64  `json:"create_time"`
	UpdateTime       int64  `json:"update_time"`
}

func GetPayoutSchedule(businessId int64) (*PayoutSchedule, error) {

	var result ObjResp[PayoutSchedule]
	resp, err := resty.New().R().
		SetHeaders(Header()).
		SetResult(&result).Get(Url(fmt.Sprintf("/stripe-api/payout-schedules/%d", businessId)))
	if err != nil {
		return nil, err
	}
	if resp.IsError() {
		return nil, fmt.Errorf("GetPayoutSchedule error: %s", resp.String())
	}
	if result.Code != 200 {
		return nil, fmt.Errorf("GetPayoutSchedule error: %s", result.Message)
	}

	if result.Code != 200 {
		return nil, fmt.Errorf("GetPayoutSchedule error: %s", result.Message)
	}

	return &result.Data.Obj, nil
}
