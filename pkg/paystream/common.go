package paystream

import (
	"encoding/base64"
	"fmt"
)

func Header() map[string]string {
	clientName := config.AppClient
	clientPassword := config.AppPassword

	header := map[string]string{}
	msgStr := fmt.Sprintf("%s:%s", clientName, clientPassword)
	msg := []byte(msgStr)
	uid := base64.URLEncoding.EncodeToString(msg)

	header["Authorization"] = "Basic " + uid
	header["Content-Type"] = "application/json"

	return header
}

func Url(path string) string {
	return fmt.Sprintf("%s%s", config.Host, path)
}
