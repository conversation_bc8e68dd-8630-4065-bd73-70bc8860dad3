package paystream

type CommonResp struct {
	Code      int    `json:"code"`
	Message   string `json:"message"`
	Timestamp int64  `json:"timestamp"`
}

type ObjResp[T any] struct {
	CommonResp
	Data Obj[T] `json:"data"`
}

type Obj[T any] struct {
	Obj T `json:"obj"`
}

type ListResp[T any] struct {
	CommonResp
	Data List[T] `json:"data"`
}

type List[T any] struct {
	List  []T   `json:"list"`
	Total int64 `json:"total"`
}

const TerminalType = 1

const (
	OfflineStatus = 0
	OnlineStatus  = 1
)
