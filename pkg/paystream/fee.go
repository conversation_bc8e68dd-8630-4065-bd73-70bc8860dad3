package paystream

type Fee struct {
	Amount         int64 `json:"amount"`
	OriginalAmount int64 `json:"original_amount"`

	ApplicationFee        int64 `json:"application_fee"`
	ApplicationFeePercent int64 `json:"application_fee_percent"`

	PaymentFee        int64 `json:"payment_fee"`
	PaymentFeePercent int64 `json:"payment_fee_percent"`
	PaymentBaseFee    int64 `json:"payment_base_fee"`

	// ClientFee   int64 `json:"client_fee"`
	// BusinessFee int64 `json:"business_fee"`
}

type FeeConfig struct {
	// ApplicationFee        int64 `json:"application_fee"`
	ApplicationFeePercent int64 `json:"application_fee_percent"`

	// PaymentFee        int64 `json:"payment_fee"`
	PaymentFeePercent int64 `json:"payment_fee_percent"`
	PaymentBaseFee    int64 `json:"payment_base_fee"`

	FeePayer int8 `json:"fee_player"`
}

const (
	FeePayerCustomer int8 = 1
	FeePayerMerchant int8 = 2
)

func CalcFee(amount int64, c FeeConfig) Fee {
	applicationFee := (amount*c.ApplicationFeePercent + 50) / 10000
	var paymentFee int64
	if amount > 0 {
		paymentFee = (amount*c.PaymentFeePercent+50)/10000 + c.PaymentBaseFee
	}

	originAmount := amount
	var newAmount int64
	switch c.FeePayer {
	case FeePayerCustomer:
		newAmount = amount + paymentFee
	case FeePayerMerchant:
		newAmount = amount
	default:
		newAmount = amount + paymentFee
	}

	return Fee{
		Amount:         newAmount,
		OriginalAmount: originAmount,

		ApplicationFee:        applicationFee,
		ApplicationFeePercent: c.ApplicationFeePercent,

		PaymentFee:        paymentFee,
		PaymentFeePercent: c.PaymentFeePercent,
		PaymentBaseFee:    c.PaymentBaseFee,
	}
}
