package template

import (
	"fmt"
	"strings"
)

// TemplateVariable defines a variable that can be used in a template.
type TemplateVariable struct {
	Name        string `json:"name"`
	Description string `json:"description"`
}

// TemplateRenderer defines the interface for a template rendering engine.
type TemplateRenderer interface {
	// Ren<PERSON> renders the template content with the given variables.
	Render(content string, variables map[string]string) (string, error)
	// ValidateVariables validates if the input variables match the template's requirements.
	ValidateVariables(templateVars []TemplateVariable, inputVars map[string]string) error
}

// simpleTemplateRenderer is a simple implementation of TemplateRenderer.
// It uses strings.ReplaceAll for rendering.
type simpleTemplateRenderer struct{}

// NewSimpleTemplateRenderer creates a new instance of simpleTemplateRenderer.
func NewSimpleTemplateRenderer() TemplateRenderer {
	return &simpleTemplateRenderer{}
}

// Ren<PERSON> replaces placeholders in the format "{{variable_name}}" with their values.
func (r *simpleTemplateRenderer) Render(content string, variables map[string]string) (string, error) {
	for key, value := range variables {
		placeholder := fmt.Sprintf("{{%s}}", key)
		content = strings.ReplaceAll(content, placeholder, value)
	}
	return content, nil
}

// ValidateVariables checks if all variables defined in templateVars are present in inputVars.
func (r *simpleTemplateRenderer) ValidateVariables(templateVars []TemplateVariable, inputVars map[string]string) error {
	for _, templateVar := range templateVars {
		if _, ok := inputVars[templateVar.Name]; !ok {
			return fmt.Errorf("missing variable: %s", templateVar.Name)
		}
	}
	return nil
}
