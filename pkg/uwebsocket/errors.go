package uwebsocket

import "errors"

// 错误定义
var (
	ErrMaxConnectionsReached = errors.New("maximum connections reached")
	ErrBroadcastChannelFull  = errors.New("broadcast channel is full")
	ErrConnectionClosed      = errors.New("connection is closed")
	ErrInvalidToken          = errors.New("invalid token")
	ErrTokenExpired          = errors.New("token expired")
	ErrInvalidMessage        = errors.New("invalid message format")
	ErrAuthenticationFailed  = errors.New("authentication failed")
	ErrHubNotRunning         = errors.New("hub is not running")
)
