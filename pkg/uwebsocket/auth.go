package uwebsocket

// UserInfo 用户信息
type UserInfo struct {
	TenantId   string `json:"tenant_id"`
	LocationId string `json:"location_id"`
	AccountId  string `json:"account_id"`
}

// Authenticator 鉴权接口
type Authenticator interface {
	// Authenticate 验证token并返回用户信息
	Authenticate(token string) (*UserInfo, error)
}

// TokenExtractor token提取器接口
type TokenExtractor interface {
	// ExtractToken 从请求中提取token
	ExtractToken(r interface{}) (string, error)
}
