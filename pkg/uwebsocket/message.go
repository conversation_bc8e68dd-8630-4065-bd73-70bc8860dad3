package uwebsocket

import (
	"time"

	"github.com/segmentio/ksuid"
)

// Message WebSocket消息结构
type Message struct {
	// 基础字段
	ID        string      `json:"id"`        // 消息ID
	Type      string      `json:"type"`      // 消息类型
	Event     string      `json:"event"`     // 事件名称
	Data      interface{} `json:"data"`      // 消息数据
	Timestamp int64       `json:"timestamp"` // 时间戳(毫秒)

	// 扩展字段（可选）
	Meta    *MessageMeta      `json:"meta,omitempty"`    // 元信息
	Headers map[string]string `json:"headers,omitempty"` // 自定义头部
}

// MessageMeta 消息元信息
type MessageMeta struct {
	// 目标信息
	Target *TargetInfo `json:"target,omitempty"` // 推送目标

	// 消息属性
	Priority int   `json:"priority,omitempty"` // 优先级 1-5
	TTL      int64 `json:"ttl,omitempty"`      // 生存时间(秒)
	Reliable bool  `json:"reliable,omitempty"` // 是否可靠传输

	// 追踪信息
	TraceID string `json:"trace_id,omitempty"` // 链路追踪
}

// TargetInfo 目标信息
type TargetInfo struct {
	TenantId   string `json:"tenant_id"`
	LocationId string `json:"location_id,omitempty"`
	AccountId  string `json:"account_id,omitempty"`
}

// 消息类型常量
const (
	// 系统消息
	TypeSystem    = "system"    // 系统消息
	TypeHeartbeat = "heartbeat" // 心跳
	TypeError     = "error"     // 错误

	// 业务消息
	TypeNotify    = "notify"    // 通知
	TypeBroadcast = "broadcast" // 广播
	TypeEvent     = "event"     // 事件
)

// 心跳事件
const (
	EventPing = "ping"
	EventPong = "pong"
)

// MessageBuilder 消息构建器
type MessageBuilder struct {
	message *Message
}

// NewMessage 创建新消息构建器
func NewMessage(msgType, event string) *MessageBuilder {
	return &MessageBuilder{
		message: &Message{
			ID:        generateMessageID(),
			Type:      msgType,
			Event:     event,
			Timestamp: time.Now().UnixMilli(),
		},
	}
}

// Data 设置消息数据
func (b *MessageBuilder) Data(data interface{}) *MessageBuilder {
	b.message.Data = data
	return b
}

// ToTenant 设置租户级别目标
func (b *MessageBuilder) ToTenant(tenantId string) *MessageBuilder {
	b.ensureMeta()
	b.message.Meta.Target = &TargetInfo{TenantId: tenantId}
	return b
}

// ToLocation 设置位置级别目标
func (b *MessageBuilder) ToLocation(tenantId, locationId string) *MessageBuilder {
	b.ensureMeta()
	b.message.Meta.Target = &TargetInfo{
		TenantId:   tenantId,
		LocationId: locationId,
	}
	return b
}

// ToAccount 设置账户级别目标
func (b *MessageBuilder) ToAccount(tenantId, locationId, accountId string) *MessageBuilder {
	b.ensureMeta()
	b.message.Meta.Target = &TargetInfo{
		TenantId:   tenantId,
		LocationId: locationId,
		AccountId:  accountId,
	}
	return b
}

// Priority 设置优先级
func (b *MessageBuilder) Priority(priority int) *MessageBuilder {
	b.ensureMeta()
	b.message.Meta.Priority = priority
	return b
}

// Reliable 设置为可靠传输
func (b *MessageBuilder) Reliable() *MessageBuilder {
	b.ensureMeta()
	b.message.Meta.Reliable = true
	return b
}

// TTL 设置生存时间
func (b *MessageBuilder) TTL(seconds int64) *MessageBuilder {
	b.ensureMeta()
	b.message.Meta.TTL = seconds
	return b
}

// Header 添加自定义头部
func (b *MessageBuilder) Header(key, value string) *MessageBuilder {
	if b.message.Headers == nil {
		b.message.Headers = make(map[string]string)
	}
	b.message.Headers[key] = value
	return b
}

// TraceID 设置链路追踪ID
func (b *MessageBuilder) TraceID(traceID string) *MessageBuilder {
	b.ensureMeta()
	b.message.Meta.TraceID = traceID
	return b
}

// ensureMeta 确保Meta字段存在
func (b *MessageBuilder) ensureMeta() {
	if b.message.Meta == nil {
		b.message.Meta = &MessageMeta{}
	}
}

// Build 构建消息
func (b *MessageBuilder) Build() *Message {
	return b.message
}

// generateMessageID 生成消息ID
func generateMessageID() string {
	return ksuid.New().String()
}

// NewHeartbeatMessage 创建心跳消息
func NewHeartbeatMessage() *Message {
	return NewMessage(TypeHeartbeat, EventPing).
		Data(map[string]interface{}{"status": "alive"}).
		Build()
}

// NewPongMessage 创建pong响应消息
func NewPongMessage() *Message {
	return NewMessage(TypeHeartbeat, EventPong).
		Data(map[string]interface{}{"status": "alive"}).
		Build()
}

// NewErrorMessage 创建错误消息
func NewErrorMessage(code, message string, retry bool) *Message {
	return NewMessage(TypeError, "error").
		Data(map[string]interface{}{
			"code":    code,
			"message": message,
			"retry":   retry,
		}).
		Priority(3).
		Reliable().
		Build()
}
