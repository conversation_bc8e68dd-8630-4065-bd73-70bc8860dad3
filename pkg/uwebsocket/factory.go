package uwebsocket

import (
	"fmt"
	"os"

	"github.com/redis/go-redis/v9"
)

// HubFactory Hub工厂
type HubFactory struct{}

// NewHubFactory 创建Hub工厂
func NewHubFactory() *HubFactory {
	return &HubFactory{}
}

// CreateHub 创建Hub实例
func (f *HubFactory) CreateHub(config *Config, authenticator Authenticator) (DistributedHub, error) {
	// 生成实例ID
	instanceID := f.generateInstanceID(config)
	
	// 创建分布式Hub
	hub := NewRedisDistributedHub(config, authenticator, instanceID)
	
	// 如果启用分布式模式
	if config.Distributed != nil && config.Distributed.Enabled {
		redisClient, err := f.createRedisClient(config.Distributed)
		if err != nil {
			return nil, fmt.Errorf("create redis client error: %w", err)
		}
		
		if err := hub.EnableDistributed(redisClient); err != nil {
			return nil, fmt.Errorf("enable distributed mode error: %w", err)
		}
	}
	
	return hub, nil
}

// generateInstanceID 生成实例ID
func (f *HubFactory) generateInstanceID(config *Config) string {
	// 如果配置中指定了实例ID，使用配置的
	if config.Distributed != nil && config.Distributed.InstanceID != "" {
		return config.Distributed.InstanceID
	}
	
	// 否则使用主机名 + 进程ID
	hostname, _ := os.Hostname()
	pid := os.Getpid()
	return fmt.Sprintf("%s-%d", hostname, pid)
}

// createRedisClient 创建Redis客户端
func (f *HubFactory) createRedisClient(config *DistributedConfig) (*redis.Client, error) {
	client := redis.NewClient(&redis.Options{
		Addr:     config.RedisAddr,
		Password: config.RedisPassword,
		DB:       config.RedisDB,
	})
	
	// 测试连接
	if err := client.Ping(client.Context()).Err(); err != nil {
		return nil, fmt.Errorf("redis ping error: %w", err)
	}
	
	return client, nil
}

// CreateStandaloneHub 创建单机Hub（向后兼容）
func CreateStandaloneHub(config *Config, authenticator Authenticator) *Hub {
	return NewHub(config, authenticator)
}
