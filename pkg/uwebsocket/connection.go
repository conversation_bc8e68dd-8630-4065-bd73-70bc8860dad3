package uwebsocket

import (
	"context"
	"encoding/json"
	"sync"
	"time"

	"github.com/coder/websocket"
)

// Connection WebSocket连接
type Connection struct {
	// 连接信息
	ID   string          `json:"id"`   // 连接ID
	Conn *websocket.Conn `json:"-"`    // WebSocket连接
	Hub  *Hub            `json:"-"`    // Hub引用

	// 用户信息
	UserInfo *UserInfo `json:"user_info"` // 用户信息

	// 连接状态
	Send     chan []byte   `json:"-"`         // 发送通道
	LastPing time.Time     `json:"last_ping"` // 最后心跳时间
	mutex    sync.RWMutex  `json:"-"`         // 读写锁
	closed   bool          `json:"-"`         // 连接是否已关闭
}

// NewConnection 创建新连接
func NewConnection(conn *websocket.Conn, hub *Hub, userInfo *UserInfo) *Connection {
	return &Connection{
		ID:       generateConnectionID(),
		Conn:     conn,
		Hub:      hub,
		UserInfo: userInfo,
		Send:     make(chan []byte, hub.config.SendBufferSize),
		LastPing: time.Now(),
		closed:   false,
	}
}

// Start 启动连接处理
func (c *Connection) Start(ctx context.Context) {
	go c.writePump(ctx)
	go c.readPump(ctx)
}

// readPump 读取消息
func (c *Connection) readPump(ctx context.Context) {
	defer func() {
		c.Hub.unregister <- c
		c.Close()
	}()

	// 设置读取限制
	c.Conn.SetReadLimit(512)

	for {
		select {
		case <-ctx.Done():
			return
		default:
			// 读取消息
			_, messageData, err := c.Conn.Read(ctx)
			if err != nil {
				if websocket.CloseStatus(err) != websocket.StatusNormalClosure {
					// 记录非正常关闭的错误
					c.Hub.logError(ctx, "websocket read error", err)
				}
				return
			}

			// 处理消息
			c.handleMessage(ctx, messageData)
		}
	}
}

// writePump 写入消息
func (c *Connection) writePump(ctx context.Context) {
	ticker := time.NewTicker(c.Hub.config.PingInterval)
	defer func() {
		ticker.Stop()
		c.Close()
	}()

	for {
		select {
		case <-ctx.Done():
			return

		case message, ok := <-c.Send:
			if !ok {
				// 通道已关闭
				c.writeMessage(ctx, websocket.MessageText, []byte{})
				return
			}

			if err := c.writeMessage(ctx, websocket.MessageText, message); err != nil {
				c.Hub.logError(ctx, "websocket write error", err)
				return
			}

		case <-ticker.C:
			// 发送心跳
			c.sendHeartbeat(ctx)
		}
	}
}

// writeMessage 写入消息到WebSocket
func (c *Connection) writeMessage(ctx context.Context, messageType websocket.MessageType, data []byte) error {
	c.mutex.Lock()
	defer c.mutex.Unlock()

	if c.closed {
		return ErrConnectionClosed
	}

	// 设置写入超时
	writeCtx, cancel := context.WithTimeout(ctx, c.Hub.config.WriteTimeout)
	defer cancel()

	return c.Conn.Write(writeCtx, messageType, data)
}

// handleMessage 处理接收到的消息
func (c *Connection) handleMessage(ctx context.Context, data []byte) {
	var msg Message
	if err := json.Unmarshal(data, &msg); err != nil {
		c.Hub.logError(ctx, "unmarshal message error", err)
		c.sendError(ctx, "INVALID_MESSAGE", "Invalid message format", false)
		return
	}

	// 更新最后心跳时间
	if msg.Type == TypeHeartbeat {
		c.LastPing = time.Now()
		if msg.Event == EventPing {
			c.sendPong(ctx)
		}
		return
	}

	// 处理其他类型消息
	c.Hub.logInfo(ctx, "received message", map[string]interface{}{
		"connection_id": c.ID,
		"type":          msg.Type,
		"event":         msg.Event,
	})
}

// SendMessage 发送消息
func (c *Connection) SendMessage(msg *Message) error {
	c.mutex.RLock()
	defer c.mutex.RUnlock()

	if c.closed {
		return ErrConnectionClosed
	}

	data, err := json.Marshal(msg)
	if err != nil {
		return err
	}

	select {
	case c.Send <- data:
		return nil
	default:
		// 发送通道已满，关闭连接
		close(c.Send)
		return ErrBroadcastChannelFull
	}
}

// sendHeartbeat 发送心跳
func (c *Connection) sendHeartbeat(ctx context.Context) {
	msg := NewHeartbeatMessage()
	if err := c.SendMessage(msg); err != nil {
		c.Hub.logError(ctx, "send heartbeat error", err)
	}
}

// sendPong 发送pong响应
func (c *Connection) sendPong(ctx context.Context) {
	msg := NewPongMessage()
	if err := c.SendMessage(msg); err != nil {
		c.Hub.logError(ctx, "send pong error", err)
	}
}

// sendError 发送错误消息
func (c *Connection) sendError(ctx context.Context, code, message string, retry bool) {
	msg := NewErrorMessage(code, message, retry)
	if err := c.SendMessage(msg); err != nil {
		c.Hub.logError(ctx, "send error message error", err)
	}
}

// Close 关闭连接
func (c *Connection) Close() {
	c.mutex.Lock()
	defer c.mutex.Unlock()

	if c.closed {
		return
	}

	c.closed = true
	close(c.Send)
	c.Conn.Close(websocket.StatusNormalClosure, "connection closed")
}

// IsClosed 检查连接是否已关闭
func (c *Connection) IsClosed() bool {
	c.mutex.RLock()
	defer c.mutex.RUnlock()
	return c.closed
}

// IsExpired 检查连接是否过期
func (c *Connection) IsExpired(timeout time.Duration) bool {
	return time.Since(c.LastPing) > timeout
}

// GetTenantId 获取租户ID
func (c *Connection) GetTenantId() string {
	if c.UserInfo != nil {
		return c.UserInfo.TenantId
	}
	return ""
}

// GetLocationId 获取位置ID
func (c *Connection) GetLocationId() string {
	if c.UserInfo != nil {
		return c.UserInfo.LocationId
	}
	return ""
}

// GetAccountId 获取账户ID
func (c *Connection) GetAccountId() string {
	if c.UserInfo != nil {
		return c.UserInfo.AccountId
	}
	return ""
}

// generateConnectionID 生成连接ID
func generateConnectionID() string {
	return "conn_" + generateMessageID()
}
