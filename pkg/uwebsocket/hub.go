package uwebsocket

import (
	"context"
	"sync"
	"time"
)

// Hub WebSocket连接管理中心
type Hub struct {
	// 连接管理
	connections map[string]*Connection // connection_id -> Connection

	// 分组连接索引
	tenantConnections   map[string]map[string]*Connection                       // tenant_id -> connection_id -> Connection
	locationConnections map[string]map[string]*Connection                       // location_id -> connection_id -> Connection
	accountConnections  map[string]map[string]map[string]map[string]*Connection // tenant_id -> location_id -> account_id -> connection_id -> Connection

	// 通道
	register   chan *Connection // 注册连接
	unregister chan *Connection // 注销连接
	broadcast  chan *Message    // 广播消息

	// 配置和依赖
	config        *Config
	authenticator Authenticator
	logger        Logger

	// 状态
	mutex   sync.RWMutex
	running bool
}

// Logger 日志接口
type Logger interface {
	Info(ctx context.Context, msg string, fields map[string]interface{})
	Error(ctx context.Context, msg string, err error)
}

// NewHub 创建新的Hub
func NewHub(config *Config, authenticator Authenticator) *Hub {
	if config == nil {
		config = DefaultConfig()
	}

	return &Hub{
		connections:         make(map[string]*Connection),
		tenantConnections:   make(map[string]map[string]*Connection),
		locationConnections: make(map[string]map[string]*Connection),
		accountConnections:  make(map[string]map[string]map[string]map[string]*Connection),
		register:            make(chan *Connection),
		unregister:          make(chan *Connection),
		broadcast:           make(chan *Message, config.BroadcastBufferSize),
		config:              config,
		authenticator:       authenticator,
		running:             false,
	}
}

// SetLogger 设置日志器
func (h *Hub) SetLogger(logger Logger) {
	h.logger = logger
}

// Start 启动Hub
func (h *Hub) Start(ctx context.Context) {
	h.mutex.Lock()
	if h.running {
		h.mutex.Unlock()
		return
	}
	h.running = true
	h.mutex.Unlock()

	h.logInfo(ctx, "WebSocket Hub starting", nil)

	// 启动清理定时器
	cleanupTicker := time.NewTicker(h.config.CleanupInterval)
	defer cleanupTicker.Stop()

	for {
		select {
		case <-ctx.Done():
			h.shutdown()
			return

		case conn := <-h.register:
			h.registerConnection(conn)

		case conn := <-h.unregister:
			h.unregisterConnection(conn)

		case message := <-h.broadcast:
			h.broadcastMessage(ctx, message)

		case <-cleanupTicker.C:
			h.cleanupExpiredConnections(ctx)
		}
	}
}

// RegisterConnection 注册连接
func (h *Hub) RegisterConnection(conn *Connection) {
	if !h.running {
		h.logError(context.Background(), "hub not running", ErrHubNotRunning)
		conn.Close()
		return
	}

	select {
	case h.register <- conn:
	default:
		h.logError(context.Background(), "register channel full", nil)
		conn.Close()
	}
}

// registerConnection 内部注册连接
func (h *Hub) registerConnection(conn *Connection) {
	h.mutex.Lock()
	defer h.mutex.Unlock()

	// 检查连接数限制
	if len(h.connections) >= h.config.MaxConnections {
		h.logError(context.Background(), "max connections reached", ErrMaxConnectionsReached)
		conn.Close()
		return
	}

	// 添加到主连接映射
	h.connections[conn.ID] = conn

	// 添加到租户索引
	tenantId := conn.GetTenantId()
	if tenantId != "" {
		if h.tenantConnections[tenantId] == nil {
			h.tenantConnections[tenantId] = make(map[string]*Connection)
		}
		h.tenantConnections[tenantId][conn.ID] = conn
	}

	// 添加到位置索引
	locationId := conn.GetLocationId()
	if locationId != "" {
		if h.locationConnections[locationId] == nil {
			h.locationConnections[locationId] = make(map[string]*Connection)
		}
		h.locationConnections[locationId][conn.ID] = conn
	}

	// 添加到账户索引 (tenant_id -> location_id -> account_id -> connection_id -> Connection)
	accountId := conn.GetAccountId()
	if accountId != "" && tenantId != "" && locationId != "" {
		if h.accountConnections[tenantId] == nil {
			h.accountConnections[tenantId] = make(map[string]map[string]map[string]*Connection)
		}
		if h.accountConnections[tenantId][locationId] == nil {
			h.accountConnections[tenantId][locationId] = make(map[string]map[string]*Connection)
		}
		if h.accountConnections[tenantId][locationId][accountId] == nil {
			h.accountConnections[tenantId][locationId][accountId] = make(map[string]*Connection)
		}
		h.accountConnections[tenantId][locationId][accountId][conn.ID] = conn
	}

	h.logInfo(context.Background(), "connection registered", map[string]interface{}{
		"connection_id": conn.ID,
		"tenant_id":     tenantId,
		"location_id":   locationId,
		"account_id":    accountId,
	})
}

// unregisterConnection 内部注销连接
func (h *Hub) unregisterConnection(conn *Connection) {
	h.mutex.Lock()
	defer h.mutex.Unlock()

	// 从主连接映射中删除
	delete(h.connections, conn.ID)

	// 从租户索引中删除
	tenantId := conn.GetTenantId()
	if tenantId != "" {
		if tenantConns, exists := h.tenantConnections[tenantId]; exists {
			delete(tenantConns, conn.ID)
			if len(tenantConns) == 0 {
				delete(h.tenantConnections, tenantId)
			}
		}
	}

	// 从位置索引中删除
	locationId := conn.GetLocationId()
	if locationId != "" {
		if locationConns, exists := h.locationConnections[locationId]; exists {
			delete(locationConns, conn.ID)
			if len(locationConns) == 0 {
				delete(h.locationConnections, locationId)
			}
		}
	}

	// 从账户索引中删除
	accountId := conn.GetAccountId()
	if accountId != "" && tenantId != "" && locationId != "" {
		if tenantAccounts, exists := h.accountConnections[tenantId]; exists {
			if locationAccounts, exists := tenantAccounts[locationId]; exists {
				if accountConns, exists := locationAccounts[accountId]; exists {
					delete(accountConns, conn.ID)
					if len(accountConns) == 0 {
						delete(locationAccounts, accountId)
						if len(locationAccounts) == 0 {
							delete(tenantAccounts, locationId)
							if len(tenantAccounts) == 0 {
								delete(h.accountConnections, tenantId)
							}
						}
					}
				}
			}
		}
	}

	// 关闭连接
	conn.Close()

	h.logInfo(context.Background(), "connection unregistered", map[string]interface{}{
		"connection_id": conn.ID,
	})
}

// BroadcastToTenant 向租户广播消息
func (h *Hub) BroadcastToTenant(tenantId string, message *Message) error {
	// 设置目标信息
	if message.Meta == nil {
		message.Meta = &MessageMeta{}
	}
	message.Meta.Target = &TargetInfo{TenantId: tenantId}

	return h.broadcastMessage(context.Background(), message)
}

// BroadcastToLocation 向位置广播消息
func (h *Hub) BroadcastToLocation(tenantId, locationId string, message *Message) error {
	// 设置目标信息
	if message.Meta == nil {
		message.Meta = &MessageMeta{}
	}
	message.Meta.Target = &TargetInfo{
		TenantId:   tenantId,
		LocationId: locationId,
	}

	return h.broadcastMessage(context.Background(), message)
}

// BroadcastToAccount 向账户广播消息
func (h *Hub) BroadcastToAccount(tenantId, locationId, accountId string, message *Message) error {
	// 设置目标信息
	if message.Meta == nil {
		message.Meta = &MessageMeta{}
	}
	message.Meta.Target = &TargetInfo{
		TenantId:   tenantId,
		LocationId: locationId,
		AccountId:  accountId,
	}

	return h.broadcastMessage(context.Background(), message)
}

// BroadcastToAccountAllLocations 向账户在所有location的连接广播消息
func (h *Hub) BroadcastToAccountAllLocations(tenantId, accountId string, message *Message) error {
	h.mutex.RLock()
	defer h.mutex.RUnlock()

	if tenantAccounts, exists := h.accountConnections[tenantId]; exists {
		for locationId, locationAccounts := range tenantAccounts {
			if accountConns, exists := locationAccounts[accountId]; exists {
				// 为每个location创建单独的消息
				locationMessage := *message // 复制消息
				if locationMessage.Meta == nil {
					locationMessage.Meta = &MessageMeta{}
				}
				locationMessage.Meta.Target = &TargetInfo{
					TenantId:   tenantId,
					LocationId: locationId,
					AccountId:  accountId,
				}

				// 发送给该account在此location的所有连接
				for _, conn := range accountConns {
					if !conn.IsClosed() {
						if err := conn.SendMessage(&locationMessage); err != nil {
							h.logError(context.Background(), "send message to account connection error", err)
						}
					}
				}
			}
		}
	}

	return nil
}

// Broadcast 异步广播消息
func (h *Hub) Broadcast(message *Message) error {
	if !h.running {
		return ErrHubNotRunning
	}

	select {
	case h.broadcast <- message:
		return nil
	default:
		return ErrBroadcastChannelFull
	}
}

// broadcastMessage 内部广播消息
func (h *Hub) broadcastMessage(ctx context.Context, message *Message) error {
	if message.Meta == nil || message.Meta.Target == nil {
		h.logError(ctx, "broadcast message without target", nil)
		return nil
	}

	target := message.Meta.Target
	var targetConnections map[string]*Connection

	h.mutex.RLock()
	defer h.mutex.RUnlock()

	// 根据目标类型选择连接
	if target.AccountId != "" && target.TenantId != "" && target.LocationId != "" {
		// 账户级别 - 需要精确匹配tenant和location，支持多连接
		if tenantAccounts, exists := h.accountConnections[target.TenantId]; exists {
			if locationAccounts, exists := tenantAccounts[target.LocationId]; exists {
				if accountConns, exists := locationAccounts[target.AccountId]; exists {
					targetConnections = accountConns // 直接使用account的所有连接
				}
			}
		}
	} else if target.LocationId != "" {
		// 位置级别
		targetConnections = h.locationConnections[target.LocationId]
	} else if target.TenantId != "" {
		// 租户级别
		targetConnections = h.tenantConnections[target.TenantId]
	}

	if targetConnections == nil {
		h.logInfo(ctx, "no connections found for target", map[string]interface{}{
			"tenant_id":   target.TenantId,
			"location_id": target.LocationId,
			"account_id":  target.AccountId,
		})
		return nil
	}

	// 发送消息到目标连接
	sentCount := 0
	for _, conn := range targetConnections {
		if !conn.IsClosed() {
			if err := conn.SendMessage(message); err != nil {
				h.logError(ctx, "send message to connection error", err)
			} else {
				sentCount++
			}
		}
	}

	h.logInfo(ctx, "message broadcasted", map[string]interface{}{
		"sent_count":  sentCount,
		"tenant_id":   target.TenantId,
		"location_id": target.LocationId,
		"account_id":  target.AccountId,
	})

	return nil
}

// cleanupExpiredConnections 清理过期连接
func (h *Hub) cleanupExpiredConnections(ctx context.Context) {
	h.mutex.RLock()
	expiredConnections := make([]*Connection, 0)

	for _, conn := range h.connections {
		if conn.IsExpired(h.config.PongTimeout) {
			expiredConnections = append(expiredConnections, conn)
		}
	}
	h.mutex.RUnlock()

	// 清理过期连接
	for _, conn := range expiredConnections {
		h.logInfo(ctx, "cleaning up expired connection", map[string]interface{}{
			"connection_id": conn.ID,
		})
		h.unregister <- conn
	}

	if len(expiredConnections) > 0 {
		h.logInfo(ctx, "cleaned up expired connections", map[string]interface{}{
			"count": len(expiredConnections),
		})
	}
}

// GetStats 获取统计信息
func (h *Hub) GetStats() HubStats {
	h.mutex.RLock()
	defer h.mutex.RUnlock()

	// 计算账户连接总数
	totalAccountConnections := 0
	for _, tenantAccounts := range h.accountConnections {
		for _, locationAccounts := range tenantAccounts {
			for _, accountConns := range locationAccounts {
				totalAccountConnections += len(accountConns)
			}
		}
	}

	return HubStats{
		TotalConnections:    len(h.connections),
		TenantConnections:   len(h.tenantConnections),
		LocationConnections: len(h.locationConnections),
		AccountConnections:  totalAccountConnections,
		Running:             h.running,
	}
}

// shutdown 关闭Hub
func (h *Hub) shutdown() {
	h.mutex.Lock()
	defer h.mutex.Unlock()

	h.running = false

	// 关闭所有连接
	for _, conn := range h.connections {
		conn.Close()
	}

	h.logInfo(context.Background(), "WebSocket Hub shutdown completed", nil)
}

// logInfo 记录信息日志
func (h *Hub) logInfo(ctx context.Context, msg string, fields map[string]interface{}) {
	if h.logger != nil {
		h.logger.Info(ctx, msg, fields)
	}
}

// logError 记录错误日志
func (h *Hub) logError(ctx context.Context, msg string, err error) {
	if h.logger != nil {
		h.logger.Error(ctx, msg, err)
	}
}

// HubStats Hub统计信息
type HubStats struct {
	TotalConnections    int  `json:"total_connections"`
	TenantConnections   int  `json:"tenant_connections"`
	LocationConnections int  `json:"location_connections"`
	AccountConnections  int  `json:"account_connections"`
	Running             bool `json:"running"`
}
