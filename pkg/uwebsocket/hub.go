package uwebsocket

import (
	"context"
	"sync"
	"time"
)

// Hub WebSocket连接管理中心
type Hub struct {
	// 连接管理
	connections map[string]*Connection // connection_id -> Connection

	// 分组连接索引
	tenantConnections   map[string]map[string]*Connection                       // tenant_id -> connection_id -> Connection
	locationConnections map[string]map[string]*Connection                       // location_id -> connection_id -> Connection
	accountConnections  map[string]map[string]map[string]map[string]*Connection // tenant_id -> location_id -> account_id -> connection_id -> Connection

	// 通道
	register   chan *Connection // 注册连接
	unregister chan *Connection // 注销连接
	broadcast  chan *Message    // 广播消息

	// 配置和依赖
	config        *Config
	authenticator Authenticator
	logger        Logger

	// 状态
	mutex   sync.RWMutex
	running bool
}

// Logger 日志接口
type Logger interface {
	Info(ctx context.Context, msg string, fields map[string]interface{})
	Error(ctx context.Context, msg string, err error)
}

// NewHub 创建新的Hub
func NewHub(config *Config, authenticator Authenticator) *Hub {
	if config == nil {
		config = DefaultConfig()
	}

	return &Hub{
		connections:         make(map[string]*Connection),
		tenantConnections:   make(map[string]map[string]*Connection),
		locationConnections: make(map[string]map[string]*Connection),
		accountConnections:  make(map[string]map[string]map[string]map[string]*Connection),
		register:            make(chan *Connection, config.RegisterBufferSize),
		unregister:          make(chan *Connection, config.UnregisterBufferSize),
		broadcast:           make(chan *Message, config.BroadcastBufferSize),
		config:              config,
		authenticator:       authenticator,
		running:             false,
	}
}

// SetLogger 设置日志器
func (h *Hub) SetLogger(logger Logger) {
	h.logger = logger
}

// Start 启动Hub
func (h *Hub) Start(ctx context.Context) {
	h.mutex.Lock()
	if h.running {
		h.mutex.Unlock()
		return
	}
	h.running = true
	h.mutex.Unlock()

	h.logInfo(ctx, "WebSocket Hub starting", nil)

	// 启动清理定时器
	cleanupTicker := time.NewTicker(h.config.CleanupInterval)
	defer cleanupTicker.Stop()

	for {
		select {
		case <-ctx.Done():
			h.shutdown()
			return

		case conn := <-h.register:
			h.registerConnection(conn)

		case conn := <-h.unregister:
			h.unregisterConnection(conn)

		case message := <-h.broadcast:
			h.broadcastMessage(ctx, message)

		case <-cleanupTicker.C:
			h.cleanupExpiredConnections(ctx)
		}
	}
}

// RegisterConnection 注册连接
func (h *Hub) RegisterConnection(conn *Connection) {
	if !h.running {
		h.logError(context.Background(), "hub not running", ErrHubNotRunning)
		conn.Close()
		return
	}

	// 使用带超时的阻塞发送，避免立即丢弃连接
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	select {
	case h.register <- conn:
		return
	case <-ctx.Done():
		h.logError(context.Background(), "register connection timeout", nil)
		conn.Close()
	}
}

// registerConnection 内部注册连接
func (h *Hub) registerConnection(conn *Connection) {
	h.mutex.Lock()
	defer h.mutex.Unlock()

	// 检查连接数限制
	if len(h.connections) >= h.config.MaxConnections {
		h.logError(context.Background(), "max connections reached", ErrMaxConnectionsReached)
		conn.Close()
		return
	}

	// 添加到主连接映射
	h.connections[conn.ID] = conn

	// 添加到租户索引
	tenantId := conn.GetTenantId()
	if tenantId != "" {
		if h.tenantConnections[tenantId] == nil {
			h.tenantConnections[tenantId] = make(map[string]*Connection)
		}
		h.tenantConnections[tenantId][conn.ID] = conn
	}

	// 添加到位置索引
	locationId := conn.GetLocationId()
	if locationId != "" {
		if h.locationConnections[locationId] == nil {
			h.locationConnections[locationId] = make(map[string]*Connection)
		}
		h.locationConnections[locationId][conn.ID] = conn
	}

	// 添加到账户索引 (tenant_id -> location_id -> account_id -> connection_id -> Connection)
	accountId := conn.GetAccountId()
	if accountId != "" && tenantId != "" && locationId != "" {
		if h.accountConnections[tenantId] == nil {
			h.accountConnections[tenantId] = make(map[string]map[string]map[string]*Connection)
		}
		if h.accountConnections[tenantId][locationId] == nil {
			h.accountConnections[tenantId][locationId] = make(map[string]map[string]*Connection)
		}
		if h.accountConnections[tenantId][locationId][accountId] == nil {
			h.accountConnections[tenantId][locationId][accountId] = make(map[string]*Connection)
		}
		h.accountConnections[tenantId][locationId][accountId][conn.ID] = conn
	}

	h.logInfo(context.Background(), "connection registered", map[string]interface{}{
		"connection_id": conn.ID,
		"tenant_id":     tenantId,
		"location_id":   locationId,
		"account_id":    accountId,
	})
}

// unregisterConnection 内部注销连接
func (h *Hub) unregisterConnection(conn *Connection) {
	h.mutex.Lock()
	defer h.mutex.Unlock()

	// 从主连接映射中删除
	delete(h.connections, conn.ID)

	// 从租户索引中删除
	tenantId := conn.GetTenantId()
	if tenantId != "" {
		if tenantConns, exists := h.tenantConnections[tenantId]; exists {
			delete(tenantConns, conn.ID)
			if len(tenantConns) == 0 {
				delete(h.tenantConnections, tenantId)
			}
		}
	}

	// 从位置索引中删除
	locationId := conn.GetLocationId()
	if locationId != "" {
		if locationConns, exists := h.locationConnections[locationId]; exists {
			delete(locationConns, conn.ID)
			if len(locationConns) == 0 {
				delete(h.locationConnections, locationId)
			}
		}
	}

	// 从账户索引中删除
	accountId := conn.GetAccountId()
	if accountId != "" && tenantId != "" && locationId != "" {
		if tenantAccounts, exists := h.accountConnections[tenantId]; exists {
			if locationAccounts, exists := tenantAccounts[locationId]; exists {
				if accountConns, exists := locationAccounts[accountId]; exists {
					delete(accountConns, conn.ID)
					if len(accountConns) == 0 {
						delete(locationAccounts, accountId)
						if len(locationAccounts) == 0 {
							delete(tenantAccounts, locationId)
							if len(tenantAccounts) == 0 {
								delete(h.accountConnections, tenantId)
							}
						}
					}
				}
			}
		}
	}

	// 关闭连接
	conn.Close()

	h.logInfo(context.Background(), "connection unregistered", map[string]interface{}{
		"connection_id": conn.ID,
	})
}

// BroadcastToTenant 向租户广播消息
func (h *Hub) BroadcastToTenant(tenantId string, message *Message) error {
	// 设置目标信息
	if message.Meta == nil {
		message.Meta = &MessageMeta{}
	}
	message.Meta.Target = &TargetInfo{TenantId: tenantId}

	return h.broadcastMessage(context.Background(), message)
}

// BroadcastToLocation 向位置广播消息
func (h *Hub) BroadcastToLocation(tenantId, locationId string, message *Message) error {
	// 设置目标信息
	if message.Meta == nil {
		message.Meta = &MessageMeta{}
	}
	message.Meta.Target = &TargetInfo{
		TenantId:   tenantId,
		LocationId: locationId,
	}

	return h.broadcastMessage(context.Background(), message)
}

// BroadcastToAccount 向账户广播消息
func (h *Hub) BroadcastToAccount(tenantId, locationId, accountId string, message *Message) error {
	// 设置目标信息
	if message.Meta == nil {
		message.Meta = &MessageMeta{}
	}
	message.Meta.Target = &TargetInfo{
		TenantId:   tenantId,
		LocationId: locationId,
		AccountId:  accountId,
	}

	return h.broadcastMessage(context.Background(), message)
}

// BroadcastToAccountAllLocations 向账户在所有location的连接广播消息
func (h *Hub) BroadcastToAccountAllLocations(tenantId, accountId string, message *Message) error {
	h.mutex.RLock()
	locations := make([]string, 0)

	if tenantAccounts, exists := h.accountConnections[tenantId]; exists {
		for locationId, locationAccounts := range tenantAccounts {
			if _, exists := locationAccounts[accountId]; exists {
				locations = append(locations, locationId)
			}
		}
	}
	h.mutex.RUnlock()

	// 为每个location异步发送消息
	for _, locationId := range locations {
		// 为每个location创建单独的消息
		locationMessage := *message // 复制消息
		if locationMessage.Meta == nil {
			locationMessage.Meta = &MessageMeta{}
		}
		locationMessage.Meta.Target = &TargetInfo{
			TenantId:   tenantId,
			LocationId: locationId,
			AccountId:  accountId,
		}

		// 通过broadcast通道发送
		if err := h.Broadcast(&locationMessage); err != nil {
			h.logError(context.Background(), "broadcast to account location error", err)
		}
	}

	return nil
}

// Broadcast 异步广播消息
func (h *Hub) Broadcast(message *Message) error {
	if !h.running {
		return ErrHubNotRunning
	}

	select {
	case h.broadcast <- message:
		return nil
	default:
		return ErrBroadcastChannelFull
	}
}

// broadcastMessage 内部广播消息
func (h *Hub) broadcastMessage(ctx context.Context, message *Message) error {
	if message.Meta == nil || message.Meta.Target == nil {
		h.logError(ctx, "broadcast message without target", nil)
		return nil
	}

	target := message.Meta.Target
	var targetConnections map[string]*Connection

	h.mutex.RLock()
	defer h.mutex.RUnlock()

	// 根据目标类型选择连接
	if target.AccountId != "" && target.TenantId != "" && target.LocationId != "" {
		// 账户级别 - 需要精确匹配tenant和location，支持多连接
		if tenantAccounts, exists := h.accountConnections[target.TenantId]; exists {
			if locationAccounts, exists := tenantAccounts[target.LocationId]; exists {
				if accountConns, exists := locationAccounts[target.AccountId]; exists {
					targetConnections = accountConns // 直接使用account的所有连接
				}
			}
		}
	} else if target.LocationId != "" {
		// 位置级别
		targetConnections = h.locationConnections[target.LocationId]
	} else if target.TenantId != "" {
		// 租户级别
		targetConnections = h.tenantConnections[target.TenantId]
	}

	if targetConnections == nil {
		h.logInfo(ctx, "no connections found for target", map[string]interface{}{
			"tenant_id":   target.TenantId,
			"location_id": target.LocationId,
			"account_id":  target.AccountId,
		})
		return nil
	}

	// 发送消息到目标连接
	sentCount := 0
	totalConnections := len(targetConnections)

	// 如果连接数量较少，使用串行处理
	if totalConnections <= 10 {
		for _, conn := range targetConnections {
			if !conn.IsClosed() {
				if err := conn.SendMessage(message); err != nil {
					h.logError(ctx, "send message to connection error", err)
				} else {
					sentCount++
				}
			}
		}
	} else {
		// 连接数量较多，使用并行处理
		type sendResult struct {
			success bool
			err     error
		}

		results := make(chan sendResult, totalConnections)

		// 并行发送消息
		for _, conn := range targetConnections {
			go func(c *Connection) {
				if !c.IsClosed() {
					if err := c.SendMessage(message); err != nil {
						results <- sendResult{false, err}
					} else {
						results <- sendResult{true, nil}
					}
				} else {
					results <- sendResult{false, ErrConnectionClosed}
				}
			}(conn)
		}

		// 收集结果
		for i := 0; i < totalConnections; i++ {
			result := <-results
			if result.success {
				sentCount++
			} else if result.err != nil && result.err != ErrConnectionClosed {
				h.logError(ctx, "send message to connection error", result.err)
			}
		}
	}

	h.logInfo(ctx, "message broadcasted", map[string]interface{}{
		"sent_count":  sentCount,
		"tenant_id":   target.TenantId,
		"location_id": target.LocationId,
		"account_id":  target.AccountId,
	})

	return nil
}

// cleanupExpiredConnections 清理过期连接
func (h *Hub) cleanupExpiredConnections(ctx context.Context) {
	h.mutex.RLock()
	expiredConnections := make([]*Connection, 0)

	// 批量采样方式，避免遍历所有连接
	totalConnections := len(h.connections)
	if totalConnections == 0 {
		h.mutex.RUnlock()
		return
	}

	// 如果连接数量太多，采样检查
	var connectionsToCheck []*Connection
	if totalConnections > 1000 {
		// 采样检查：每次只检查最多500个连接
		sampleSize := 500
		i := 0
		for _, conn := range h.connections {
			if i >= sampleSize {
				break
			}
			connectionsToCheck = append(connectionsToCheck, conn)
			i++
		}
	} else {
		// 连接数量较少，检查所有连接
		connectionsToCheck = make([]*Connection, 0, totalConnections)
		for _, conn := range h.connections {
			connectionsToCheck = append(connectionsToCheck, conn)
		}
	}
	h.mutex.RUnlock()

	// 检查过期连接
	for _, conn := range connectionsToCheck {
		if conn.IsExpired(h.config.PongTimeout) {
			expiredConnections = append(expiredConnections, conn)
		}
	}

	// 清理过期连接
	for _, conn := range expiredConnections {
		h.logInfo(ctx, "cleaning up expired connection", map[string]interface{}{
			"connection_id": conn.ID,
		})

		// 使用非阻塞发送避免死锁
		select {
		case h.unregister <- conn:
		default:
			// 如果通道满了，直接强制关闭连接
			h.logError(ctx, "unregister channel full during cleanup, forcing close", nil)
			h.unregisterConnection(conn)
		}
	}

	if len(expiredConnections) > 0 {
		h.logInfo(ctx, "cleaned up expired connections", map[string]interface{}{
			"count": len(expiredConnections),
			"total": totalConnections,
		})
	}
}

// GetStats 获取统计信息
func (h *Hub) GetStats() HubStats {
	h.mutex.RLock()
	defer h.mutex.RUnlock()

	var onlineAccounts int
	var accountStats []AccountStats
	// 计算账户连接总数
	totalAccountConnections := 0
	for tenantId, tenantAccounts := range h.accountConnections {
		for locationId, locationAccounts := range tenantAccounts {
			onlineAccounts += len(locationAccounts)
			for accountId, accountConns := range locationAccounts {
				connCount := len(accountConns)
				totalAccountConnections += connCount
				accountStats = append(accountStats, AccountStats{
					Connections: connCount,
					UserInfo: UserInfo{
						TenantId:   tenantId,
						LocationId: locationId,
						AccountId:  accountId,
					},
				})
			}
		}
	}

	return HubStats{
		TotalConnections:      len(h.connections),
		TenantConnections:     len(h.tenantConnections),
		LocationConnections:   len(h.locationConnections),
		AccountConnections:    totalAccountConnections,
		OnlineAccounts:        onlineAccounts,
		Accounts:              accountStats,
		Running:               h.running,
		RegisterChannelSize:   len(h.register),
		UnregisterChannelSize: len(h.unregister),
		BroadcastChannelSize:  len(h.broadcast),
	}
}

// shutdown 关闭Hub
func (h *Hub) shutdown() {
	h.mutex.Lock()
	defer h.mutex.Unlock()

	h.running = false

	// 关闭所有连接
	for _, conn := range h.connections {
		conn.Close()
	}

	h.logInfo(context.Background(), "WebSocket Hub shutdown completed", nil)
}

// logInfo 记录信息日志
func (h *Hub) logInfo(ctx context.Context, msg string, fields map[string]interface{}) {
	if h.logger != nil {
		h.logger.Info(ctx, msg, fields)
	}
}

// logError 记录错误日志
func (h *Hub) logError(ctx context.Context, msg string, err error) {
	if h.logger != nil {
		h.logger.Error(ctx, msg, err)
	}
}

type AccountStats struct {
	UserInfo
	Connections int `json:"connections"`
}

// HubStats Hub统计信息
type HubStats struct {
	TotalConnections      int            `json:"total_connections"`
	TenantConnections     int            `json:"tenant_connections"`
	LocationConnections   int            `json:"location_connections"`
	AccountConnections    int            `json:"account_connections"`
	OnlineAccounts        int            `json:"online_accounts"`
	Accounts              []AccountStats `json:"accounts"`
	Running               bool           `json:"running"`
	RegisterChannelSize   int            `json:"register_channel_size"`
	UnregisterChannelSize int            `json:"unregister_channel_size"`
	BroadcastChannelSize  int            `json:"broadcast_channel_size"`
}
