package ujwt

import (
	"errors"
	"pebble/pkg/auth"
	"pebble/pkg/uerror"
	"time"

	"github.com/golang-jwt/jwt/v5"
)

type Claims struct {
	auth.Ext
	TokenType TokenType `json:"token_type"`
	jwt.RegisteredClaims
}

type jWTService struct {
	secret        []byte
	issuer        string
	accessExpire  time.Duration
	refreshExpire time.Duration
}

var jwtService *jWTService

func InitJWTService(config *JWTConfig) {
	jwtService = &jWTService{
		secret:        []byte(config.Secret),
		issuer:        config.Issuer,
		accessExpire:  time.Duration(config.AccessTokenExpire) * time.Second,
		refreshExpire: time.Duration(config.RefreshTokenExpire) * time.Second,
	}
}

func GenerateAccessToken(ext auth.Ext) (string, error) {
	claims := &Claims{
		Ext:       ext,
		TokenType: AccessToken,
		RegisteredClaims: jwt.RegisteredClaims{
			ExpiresAt: jwt.NewNumericDate(time.Now().Add(jwtService.accessExpire)),
			Issuer:    jwtService.issuer,
		},
	}
	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
	return token.SignedString(jwtService.secret)
}

func GenerateRefreshToken(ext auth.Ext) (string, error) {
	claims := &Claims{
		Ext:       ext,
		TokenType: RefreshToken,
		RegisteredClaims: jwt.RegisteredClaims{
			ExpiresAt: jwt.NewNumericDate(time.Now().Add(jwtService.refreshExpire)),
			Issuer:    jwtService.issuer,
		},
	}
	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
	return token.SignedString(jwtService.secret)
}

func ParseToken(tokenString string) (*Claims, error) {
	token, err := jwt.ParseWithClaims(tokenString, &Claims{}, func(token *jwt.Token) (interface{}, error) {
		if _, ok := token.Method.(*jwt.SigningMethodHMAC); !ok {
			return nil, errors.New("unexpected signing method")
		}
		return jwtService.secret, nil
	})
	if err != nil {
		return nil, err
	}

	if claims, ok := token.Claims.(*Claims); ok && token.Valid {
		return claims, nil
	}

	return nil, uerror.ErrInvalidToken
}
