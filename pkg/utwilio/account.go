package utwilio

import (
	openapi "github.com/twilio/twilio-go/rest/api/v2010"
)

type CreataSubAccountResp struct {
	SID          string `json:"sid"`
	Token        string `json:"token"`
	FriendlyName string `json:"friendly_name"`
}

func CreataSubAccount(friendName string) (*CreataSubAccountResp, error) {
	params := &openapi.CreateAccountParams{}
	params.SetFriendlyName(friendName)

	subAccount, err := masterClient.Api.CreateAccount(params)
	if err != nil {
		return nil, err
	}

	return &CreataSubAccountResp{
		SID:          *subAccount.Sid,
		Token:        *subAccount.AuthToken,
		FriendlyName: *subAccount.FriendlyName,
	}, nil
}

func GetSubAccount(sid string) (any, error) {
	resp, err := masterClient.Api.FetchAccount(sid)
	if err != nil {
		return nil, err
	}

	return resp, nil
}
