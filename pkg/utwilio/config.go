package utwilio

import "github.com/twilio/twilio-go"

type Config struct {
	AccountSid          string `mapstructure:"account_sid"`
	AuthToken           string `mapstructure:"auth_token"`
	MessagingServiceSID string `mapstructure:"messaging_service_sid"`
}

var masterClient *twilio.RestClient

func Init(c *Config) {
	masterClient = twilio.NewRestClientWithParams(twilio.ClientParams{
		Username: c.AccountSid,
		Password: c.AuthToken,
	})
}
