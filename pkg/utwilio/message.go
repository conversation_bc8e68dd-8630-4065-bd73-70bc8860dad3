package utwilio

import (
	"fmt"

	"github.com/twilio/twilio-go"
	openapi "github.com/twilio/twilio-go/rest/api/v2010"
	messaging "github.com/twilio/twilio-go/rest/messaging/v1"
)

type SendMessageReq struct {
	Sid   string
	Token string

	To      string
	Content string

	From                string
	MessagingServiceSid string

	StatusCallback string
}

type Message struct {
	MessageSid string
	Status     string

	MessagingServiceSid string
	From                string
	To                  string
	Content             string

	ErrorMessage string
	ErrorCode    int
}

func SendMessage(req SendMessageReq) (*Message, error) {
	client := twilio.NewRestClientWithParams(twilio.ClientParams{
		Username: req.Sid,
		Password: req.Token,
	})

	params := &openapi.CreateMessageParams{}
	params.SetTo(req.To)
	params.SetBody(req.Content)
	if req.From != "" {
		params.SetFrom(req.From)
	}
	if req.MessagingServiceSid != "" {
		params.SetMessagingServiceSid(req.MessagingServiceSid)
	}
	if req.StatusCallback != "" {
		params.SetStatusCallback(req.StatusCallback)
	}

	resp, err := client.Api.CreateMessage(params)
	if err != nil {
		return nil, fmt.Errorf("send message err: %w", err)
	}

	var result Message
	if resp.ErrorCode != nil {
		result.ErrorCode = *resp.ErrorCode
	}
	if resp.ErrorMessage != nil {
		result.ErrorMessage = *resp.ErrorMessage
	}
	if resp.Sid != nil {
		result.MessageSid = *resp.Sid
	}
	if resp.Status != nil {
		result.Status = *resp.Status
	}
	if resp.From != nil {
		result.From = *resp.From
	}
	if resp.To != nil {
		result.To = *resp.To
	}
	if resp.Body != nil {
		result.Content = *resp.Body
	}
	if resp.MessagingServiceSid != nil {
		result.MessagingServiceSid = *resp.MessagingServiceSid
	}

	return &result, nil
}

func QueryMessage(sid, token, messageSid string) (*Message, error) {
	client := twilio.NewRestClientWithParams(twilio.ClientParams{
		Username: sid,
		Password: token,
	})

	resp, err := client.Api.FetchMessage(messageSid, nil)
	if err != nil {
		return nil, fmt.Errorf("query message err: %w", err)
	}

	return &Message{
		MessageSid:   *resp.Sid,
		Status:       *resp.Status,
		From:         *resp.From,
		To:           *resp.To,
		Content:      *resp.Body,
		ErrorMessage: *resp.ErrorMessage,
		ErrorCode:    *resp.ErrorCode,
	}, nil
}

type CreateMessagingServiceReq struct {
	SID   string
	Token string

	FriendlyName      string
	StatusCallback    string
	InboundRequestUrl string
}

type CreateMessagingServiceResp struct {
	Sid string
}

func CreateMessagingService(req CreateMessagingServiceReq) (*CreateMessagingServiceResp, error) {
	client := twilio.NewRestClientWithParams(twilio.ClientParams{
		Username: req.SID,
		Password: req.Token,
	})

	params := &messaging.CreateServiceParams{}
	params.SetFriendlyName(req.FriendlyName)
	params.SetStatusCallback(req.StatusCallback)
	params.SetInboundRequestUrl(req.InboundRequestUrl)

	resp, err := client.MessagingV1.CreateService(params)
	if err != nil {
		return nil, fmt.Errorf("create messaging service err: %w", err)
	}

	return &CreateMessagingServiceResp{
		Sid: *resp.Sid,
	}, nil
}

func AddNumberToMessagingService(sid, token, serviceSid, numberSid string) error {
	client := twilio.NewRestClientWithParams(twilio.ClientParams{
		Username: sid,
		Password: token,
	})

	params := &messaging.CreatePhoneNumberParams{}
	params.SetPhoneNumberSid(numberSid)

	_, err := client.MessagingV1.CreatePhoneNumber(serviceSid, params)
	return err
}
