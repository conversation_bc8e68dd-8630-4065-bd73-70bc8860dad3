package email

type SendEmailReq struct {
	FromEmail string
	FromName  string
	HTMLBody  string
	Subject   string
	TextBody  string
	ToEmails  []string
}

type BatchEmailReq struct {
	FromEmail  string
	FromName   string
	Subject    string
	HTMLBody   string
	TextBody   string
	Recipients []Recipient
}

type EmailResult struct {
	Id      string `json:"id,omitempty"`
	Email   string `json:"email"`
	Message string `json:"message"`
	Status  string `json:"status"`
}

type Recipient struct {
	Email string `json:"email"`
	Vars  []Var  `json:"vars"`
}

type Var struct {
	Content string `json:"content"`
	Name    string `json:"name"`
}
