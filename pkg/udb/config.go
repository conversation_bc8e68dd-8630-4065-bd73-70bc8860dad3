package udb

type DatabaseConfig struct {
	Host          string `mapstructure:"host"`
	Port          int    `mapstructure:"port"`
	Name          string `mapstructure:"name"`
	Username      string `mapstructure:"username"`
	Password      string `mapstructure:"password"`
	MaxConn       int    `mapstructure:"max_conn"`
	MaxIdleConn   int    `mapstructure:"max_idle_conn"`
	SlowThreshold int    `mapstructure:"slow_threshold"`
	LogLevel      string `mapstructure:"log_level"`
}
