package udb

import (
	"context"
	"errors"
	"pebble/pkg/ulog"
	"time"

	"go.uber.org/zap"
	"gorm.io/gorm"

	gormlogger "gorm.io/gorm/logger"
)

type GormLogger struct {
	SlowThreshold time.Duration
	LogLevel      gormlogger.LogLevel
}

func NewGormLogger(slowThreshold int, level string) *GormLogger {
	var logLevel gormlogger.LogLevel
	switch level {
	case "silent":
		logLevel = gormlogger.Silent
	case "error":
		logLevel = gormlogger.Error
	case "warn":
		logLevel = gormlogger.Warn
	case "info":
		logLevel = gormlogger.Info
	default:
		logLevel = gormlogger.Info
	}

	return &GormLogger{
		SlowThreshold: time.Duration(slowThreshold) * time.Microsecond,
		LogLevel:      logLevel,
	}
}

func (l *GormLogger) LogMode(level gormlogger.LogLevel) gormlogger.Interface {
	newLogger := *l
	newLogger.LogLevel = level
	return &newLogger
}

func (l *GormLogger) Info(ctx context.Context, msg string, data ...interface{}) {
	if l.LogLevel >= gormlogger.Info {
		ulog.Infof(ctx, msg, data...)
	}
}

func (l *GormLogger) Warn(ctx context.Context, msg string, data ...interface{}) {
	if l.LogLevel >= gormlogger.Warn {
		ulog.Warnf(ctx, msg, data...)
	}
}
func (l *GormLogger) Error(ctx context.Context, msg string, data ...interface{}) {
	if l.LogLevel >= gormlogger.Error {
		ulog.Errorf(ctx, msg, data...)
	}
}

func (l *GormLogger) Trace(ctx context.Context, begin time.Time, fc func() (string, int64), err error) {
	if l.LogLevel <= gormlogger.Silent {
		return
	}

	duration := time.Since(begin)
	sql, rows := fc()
	fields := []zap.Field{
		zap.Duration("duration", duration),
		zap.String("sql", sql),
		zap.Int64("rows", rows),
	}

	switch {
	case err != nil && l.LogLevel >= gormlogger.Error && !errors.Is(err, gorm.ErrRecordNotFound):
		fields = append(fields, zap.Error(err))
		ulog.Error(ctx, "GORM", fields...)
	case duration > time.Millisecond*l.SlowThreshold && l.SlowThreshold != 0 && l.LogLevel >= gormlogger.Warn:
		ulog.Warn(ctx, "GORM", fields...)
	case l.LogLevel >= gormlogger.Info:
		ulog.Info(ctx, "GORM", fields...)
	}
}
