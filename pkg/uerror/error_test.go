package uerror

import (
	"errors"
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestParse(t *testing.T) {
	t.Run("Parse nil error", func(t *testing.T) {
		result := Parse(nil)
		assert.Nil(t, result)
	})

	t.Run("Parse uerror", func(t *testing.T) {
		err := &uerror{
			err:  errors.New("test error"),
			code: ErrCodeCommon,
		}
		result := Parse(err)
		assert.NotNil(t, result)
		assert.Equal(t, ErrCodeCommon, result.Code())
		assert.Equal(t, "test error", result.Error())
	})

	t.Run("Parse generic error", func(t *testing.T) {
		err := errors.New("generic error")
		result := Parse(err)
		assert.NotNil(t, result)
		assert.Equal(t, ErrCodeCommon, result.Code())
		assert.Equal(t, "generic error", result.Error())
	})
}

func TestNew(t *testing.T) {
	t.Run("Create new uerror", func(t *testing.T) {
		code := 1001
		message := "new error message"
		err := New(code, message)
		assert.NotNil(t, err)

		ue, ok := err.(*uerror)
		assert.True(t, ok)
		assert.Equal(t, code, ue.Code())
		assert.Equal(t, message, ue.Error())
	})
}
