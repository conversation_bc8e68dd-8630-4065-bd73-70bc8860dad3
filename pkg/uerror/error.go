package uerror

import (
	"errors"
	"fmt"
)

type uerror struct {
	err   error
	cause error
	code  int
}

type Iuerror interface {
	Code() int
	Error() string
}

func Parse(err error) Iuerror {
	if err == nil {
		return nil
	}
	e, ok := err.(*uerror)
	if ok {
		return e
	}

	return &uerror{
		err:   err,
		code:  <PERSON>rrCodeCommon, // default code
		cause: nil,
	}
}

func New(code int, message string) error {
	return &uerror{
		err:   errors.New(message),
		cause: nil,
		code:  code,
	}
}

func (e *uerror) Error() string {
	return fmt.Sprint(e.err)
}

func (e *uerror) Code() int {
	return e.code
}

func (w *uerror) Cause() error { return w.cause }

func (w *uerror) Unwrap() error { return w.cause }

func Wrap(err error, code int, format string, args ...interface{}) error {
	if err == nil {
		return nil
	}

	return &uerror{
		err:   fmt.<PERSON><PERSON><PERSON>(format, args...),
		code:  code,
		cause: err,
	}
}
