package uid

import (
	"github.com/segmentio/ksuid"
)

const (
	TenantPrefix             = "tent_"
	LocationPrefix           = "loc_"
	AccountPrefix            = "acct_"
	ClientPrefix             = "clie_"
	TracePrefix              = "trac_"
	SMSMsgPrefix             = "sms_"
	TaxPrefix                = "tax_"
	ServicePrefix            = "scv_"
	ServiceCategoryPrefix    = "scvc_"
	AppointmentPrefix        = "appt_"
	AppointmentServicePrefix = "apts_"
	PaymentMethodPrefix      = "pmth_"
	HolidayPrefix            = "hol_"
	RolePrefix               = "rol_"
	PermissionPrefix         = "perm_"
	TagPrefix                = "tag_"
	ApptNotePrefix           = "anote_"
	PaymentPrefix            = "pay_"
	PaymentItemPrefix        = "payi_"
	RefundPrefix             = "ref_"
	RefundItemPrefix         = "refi_"
	ApptTipPrefix            = "atip_"
	MsgTmplPrefix            = "mtpl_"
)

func GenerateTenantId() string {
	return TenantPrefix + GenerateUid()
}

func GenerateLocationId() string {
	return LocationPrefix + GenerateUid()
}

func GenerateAccountId() string {
	return AccountPrefix + GenerateUid()
}

func GenerateClientId() string {
	return ClientPrefix + GenerateUid()
}

func GenerateTraceId() string {
	return TracePrefix + GenerateUid()
}

func GenerateSMSMsgId() string {
	return GenerateUidWithPrefix(SMSMsgPrefix)
}

func GenerateTaxId() string {
	return GenerateUidWithPrefix(TaxPrefix)
}

func GenerateServiceCategoryId() string {
	return GenerateUidWithPrefix(ServiceCategoryPrefix)
}

func GenerateServiceId() string {
	return GenerateUidWithPrefix(ServicePrefix)
}

func GenerateAppointmentId() string {
	return GenerateUidWithPrefix(AppointmentPrefix)
}

func GenerateAppointmentServiceId() string {
	return GenerateUidWithPrefix(AppointmentServicePrefix)
}

func GeneratePaymentMethodId() string {
	return GenerateUidWithPrefix(PaymentMethodPrefix)
}

func GeneratePaymentId() string {
	return GenerateUidWithPrefix(PaymentPrefix)
}

func GeneratePaymentItemId() string {
	return GenerateUidWithPrefix(PaymentItemPrefix)
}

func GenerateRefundId() string {
	return GenerateUidWithPrefix(RefundPrefix)
}

func GenerateRefundItemId() string {
	return GenerateUidWithPrefix(RefundItemPrefix)
}

func GenerateHolidayId() string {
	return GenerateUidWithPrefix(HolidayPrefix)
}

func GenerateRoleId() string {
	return GenerateUidWithPrefix(RolePrefix)
}

func GeneratePermissionId() string {
	return GenerateUidWithPrefix(PermissionPrefix)
}

func GenerateTagId() string {
	return GenerateUidWithPrefix(TagPrefix)
}

func GenerateAppointmentNoteId() string {
	return GenerateUidWithPrefix(ApptNotePrefix)
}

func GenerateAppointmentTipId() string {
	return GenerateUidWithPrefix(ApptTipPrefix)
}

func GenerateMessageTemplateId() string {
	return GenerateUidWithPrefix(MsgTmplPrefix)
}

func GenerateUidWithPrefix(prefix string) string {
	return prefix + GenerateUid()
}

func GenerateUid() string {
	return ksuid.New().String()
}
