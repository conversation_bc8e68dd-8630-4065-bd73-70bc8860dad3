package auth

import (
	"context"
)

type extCtxKey struct{}

type Ext struct {
	TenantId   string `json:"tenant_id"`
	LocationId string `json:"location_id"`
	AccountId  string `json:"account_id"`
}

type Ctx struct {
	Ext
	ctx context.Context
}

func (c *Ctx) Context() context.Context {
	return c.ctx
}

func (c *Ctx) WithValue(key, value interface{}) *Ctx {
	return &Ctx{
		Ext: Ext{
			TenantId:   c.TenantId,
			LocationId: c.LocationId,
			AccountId:  c.AccountId,
		},
		ctx: context.WithValue(c.ctx, key, value),
	}
}

func AuthConText(ctx context.Context) *Ctx {
	authExt, ok := ctx.Value(extCtxKey{}).(Ext)
	if !ok {
		return &Ctx{ctx: ctx}
	}
	return &Ctx{Ext: authExt, ctx: ctx}
}

func ContextWithExt(ctx context.Context, ext Ext) context.Context {
	return context.WithValue(ctx, extCtxKey{}, ext)
}
