package util

import "encoding/json"

func Ptr[T any](i T) *T { return &i }

func PtrToValue[T any](p *T) T {
	if p == nil {
		return *new(T)
	}
	return *p
}

func NoNilSlice[T any](slice []T) []T {
	if slice == nil {
		return make([]T, 0)
	}

	return slice
}

func NonNegative[T int | int32 | int64 | float32 | float64](v T) T {
	if v < 0 {
		return 0
	}
	return v
}
func DeepCopy(src, dst any) error {
	if src == nil || dst == nil {
		return nil
	}
	data, err := json.Marshal(src)
	if err != nil {
		return err
	}
	return json.Unmarshal(data, dst)
}
