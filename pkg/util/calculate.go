package util

import (
	"fmt"
)

func CalculateTax(priceCents int64, taxRatePerThousand int64) int64 {
	if priceCents < 0 || taxRatePerThousand < 0 {
		return 0
	}

	// 带四舍五入的税额计算
	// tax = price * rate / 100000，并四舍五入
	tax := (priceCents*taxRatePerThousand + 500) / 100000
	return tax
}

func WithCurrencySymbol(symbol string, price int64) string {
	sign := ""
	uprice := uint64(price)
	if price < 0 {
		sign = "-"
		uprice = uint64(-price)
	}

	dollars := uprice / 100
	cents := uprice % 100

	return fmt.Sprintf("%s%s%d.%02d", sign, symbol, dollars, cents)
}
