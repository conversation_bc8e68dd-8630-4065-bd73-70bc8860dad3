package util

import (
	"strconv"
	"strings"
)

func SplitInt64(str string) []int64 {
	if str == "" {
		return []int64{}
	}
	str_arr := strings.Split(str, ",")
	var ids []int64
	for _, id_str := range str_arr {
		id, _ := strconv.Atoi(id_str)
		ids = append(ids, int64(id))
	}
	return ids
}

func Join(ids []int64, sep string) string {
	if len(ids) <= 0 {
		return ""
	}
	str_ids := []string{}
	for _, id := range ids {
		str_ids = append(str_ids, strconv.Itoa(int(id)))
	}
	return strings.Join(str_ids, sep)
}
