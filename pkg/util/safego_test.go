package util

import (
	"context"
	"sync"
	"testing"
	"time"
)

func TestGoSafe(t *testing.T) {
	tests := []struct {
		name        string
		fn          func(ctx context.Context)
		shouldPanic bool
	}{
		{
			name: "normal execution",
			fn: func(ctx context.Context) {
				time.Sleep(10 * time.Millisecond)
			},
			shouldPanic: false,
		},
		{
			name: "panic execution",
			fn: func(ctx context.Context) {
				panic("test panic")
			},
			shouldPanic: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctx := context.Background()
			wg := sync.WaitGroup{}
			wg.Add(1)

			// Wrap the test function to signal completion
			wrappedFn := func(ctx context.Context) {
				defer wg.Done()
				tt.fn(ctx)
			}

			// Execute the safe goroutine
			GoSafe(ctx, wrappedFn)

			// Wait for goroutine completion with timeout
			done := make(chan struct{})
			go func() {
				wg.Wait()
				close(done)
			}()

			select {
			case <-done:
				// Success case - goroutine completed
			case <-time.After(100 * time.Millisecond):
				t.<PERSON>("test timed out")
			}
		})
	}
}

func TestGoSafeWithContext(t *testing.T) {
	ctx, cancel := context.WithCancel(context.Background())
	wg := sync.WaitGroup{}
	wg.Add(1)

	// Create a channel to track if the goroutine received the context cancellation
	contextCanceled := make(chan struct{})

	GoSafe(ctx, func(ctx context.Context) {
		defer wg.Done()
		<-ctx.Done()
		close(contextCanceled)
	})

	// Cancel the context
	cancel()

	// Wait for the goroutine to process the cancellation
	select {
	case <-contextCanceled:
		// Success - goroutine received context cancellation
	case <-time.After(100 * time.Millisecond):
		t.Error("context cancellation was not propagated to the goroutine")
	}

	wg.Wait()
}
