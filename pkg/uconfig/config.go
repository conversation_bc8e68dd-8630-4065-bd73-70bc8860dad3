package uconfig

import (
	"flag"
	"fmt"
	"os"
	"path/filepath"
	"strings"

	"github.com/go-playground/validator/v10"
	"github.com/spf13/viper"
)

// Logger is an interface for logging, compatible with standard loggers.
type Logger interface {
	Printf(format string, v ...interface{})
}

// nopLogger is a logger that does nothing.
type nopLogger struct{}

func (n *nopLogger) Printf(_ string, _ ...interface{}) {}

// ConfigLoader represents a configuration loader with flexible options
type ConfigLoader struct {
	v         *viper.Viper
	envPrefix string
	logger    Logger
}

// Option is a configuration option for ConfigLoader
type Option func(*ConfigLoader)

// WithEnvPrefix sets the environment variable prefix
func WithEnvPrefix(prefix string) Option {
	return func(cl *ConfigLoader) {
		cl.envPrefix = prefix
	}
}

// WithLogger sets a custom logger for the loader
func WithLogger(logger Logger) Option {
	return func(cl *ConfigLoader) {
		cl.logger = logger
	}
}

// New creates a new ConfigLoader with the given options
func New(options ...Option) *ConfigLoader {
	cl := &ConfigLoader{
		v:      viper.New(),
		logger: &nopLogger{},
	}

	for _, option := range options {
		option(cl)
	}

	// Configure viper
	if cl.envPrefix != "" {
		cl.v.SetEnvPrefix(cl.envPrefix)
		cl.v.SetEnvKeyReplacer(strings.NewReplacer(".", "_"))
	}
	cl.v.AutomaticEnv()

	return cl
}

// Load loads configuration from a given path and environment into the provided config struct
func (cl *ConfigLoader) Load(configPath, env string, config interface{}) error {
	v := cl.v
	configName := "webapp" // Default config name
	configType := "yaml"   // Default config type

	// Track whether the provided path is a specific file
	isFile := false

	// 1. Configure Viper's search paths.
	if configPath != "" {
		fi, err := os.Stat(configPath)
		if err != nil {
			return fmt.Errorf("cannot access config path '%s': %w", configPath, err)
		}
		if fi.IsDir() {
			// If a directory is provided, add it to the search path.
			v.AddConfigPath(configPath)
		} else {
			// If a file is provided, tell Viper to use it directly.
			v.SetConfigFile(configPath)
			configName = strings.TrimSuffix(filepath.Base(configPath), filepath.Ext(configPath))
			isFile = true
		}
	} else {
		// Add default search paths if no explicit path is given.
		v.AddConfigPath("./configs")
		v.AddConfigPath("./config")
		v.AddConfigPath(".")
	}

	// Only set config name/type when we are NOT using an explicit file.
	if !isFile {
		v.SetConfigName(configName)
		v.SetConfigType(configType)
	}

	// 2. Read the base configuration file.
	if err := v.ReadInConfig(); err != nil {
		// It's a fatal error if the base config cannot be read.
		return fmt.Errorf("failed to read base config file: %w", err)
	}
	cl.logger.Printf("Loaded config from: %s", v.ConfigFileUsed())

	// 3. Merge environment-specific config if env is set.
	if env != "" {
		// Preserve the original app name, as it can be wiped out during merge.
		originalAppName := v.GetString("app.name")

		// Set config name for the environment-specific file (e.g., "webapp.dev").
		v.SetConfigName(fmt.Sprintf("%s.%s", configName, env))

		// Attempt to merge the env-specific file. It's okay if it doesn't exist.
		if err := v.MergeInConfig(); err != nil {
			cl.logger.Printf("No environment config file found for '%s', or failed to merge: %v", env, err)
		} else {
			cl.logger.Printf("Merged environment config from: %s", v.ConfigFileUsed())
		}

		// Restore app.name if it was blanked out by the merge and was not set in the env file.
		if v.GetString("app.name") == "" && originalAppName != "" {
			v.Set("app.name", originalAppName)
			cl.logger.Printf("Restored original app.name: %s", originalAppName)
		}
	}

	// 4. Unmarshal the final configuration into the struct.
	if err := v.Unmarshal(config); err != nil {
		return fmt.Errorf("failed to unmarshal config: %w", err)
	}

	// 5. Validate the struct.
	validate := validator.New()
	if err := validate.Struct(config); err != nil {
		return fmt.Errorf("config validation failed: %w", err)
	}

	return nil
}

// ParseFlags parses command-line flags for config path and environment.
// It is recommended to call this from your main function and pass the results to the loader.
func ParseFlags() (configPath, env string) {
	flag.StringVar(&configPath, "config", "", "Path to the configuration file or directory")
	flag.StringVar(&env, "env", "", "Environment (e.g., dev, test, prod)")
	flag.Parse()
	return
}

// GetViper returns the underlying viper instance
func (cl *ConfigLoader) GetViper() *viper.Viper {
	return cl.v
}
