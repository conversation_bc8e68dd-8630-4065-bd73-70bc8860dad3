package uconfig

// // AppConfigProvider defines the interface for application configurations
// type AppConfigProvider interface {
// 	// GetAppName returns the application name
// 	GetAppName() string
// 	// GetAppEnv returns the application environment
// 	GetAppEnv() string
// 	// GetAppMode returns the application mode (debug, release, etc.)
// 	GetAppMode() string
// }

// // ServerConfigProvider defines the interface for server configurations
// type ServerConfigProvider interface {
// 	// GetServerPort returns the server port
// 	GetServerPort() int
// 	// GetServerDomain returns the server domain
// 	GetServerDomain() string
// }

// // DatabaseConfigProvider defines the interface for database configurations
// type DatabaseConfigProvider interface {
// 	// GetDatabaseConfig returns the database configuration
// 	GetDatabaseConfig() interface{}
// }

// // LogConfigProvider defines the interface for logging configurations
// type LogConfigProvider interface {
// 	// GetLogConfig returns the logging configuration
// 	GetLogConfig() interface{}
// }

// // ConfigProvider combines all config providers
// type ConfigProvider interface {
// 	AppConfigProvider
// 	ServerConfigProvider
// 	// Additional provider interfaces can be added as needed
// }
