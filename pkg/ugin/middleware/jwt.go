package middleware

import (
	"errors"
	"pebble/pkg/auth"
	"pebble/pkg/uerror"
	"pebble/pkg/ugin/writer"
	"pebble/pkg/ujwt"
	"pebble/pkg/ulog"
	"strings"

	"github.com/gin-gonic/gin"
	"github.com/golang-jwt/jwt/v5"
)

func JWTAuth() gin.HandlerFunc {
	return func(c *gin.Context) {
		authHeader := c.<PERSON>eader("Authorization")
		if authHeader == "" {
			writer.ResponseErr(c, uerror.ErrInvalidToken, nil)
			return
		}

		parts := strings.SplitN(authHeader, " ", 2)
		if !(len(parts) == 2 && parts[0] == "Bearer") {
			writer.ResponseErr(c, uerror.ErrInvalidToken, nil)
			return
		}

		ctx := c.Request.Context()
		claims, err := ujwt.ParseToken(parts[1])
		if err != nil {
			ulog.Errorln(ctx, "parse token error", err)
			if errors.Is(err, jwt.ErrTokenExpired) {
				writer.ResponseErr(c, uerror.ErrExpiredToken, nil)
				return
			}
			writer.ResponseErr(c, uerror.ErrInvalidToken, nil)
			return
		}

		ctx = auth.ContextWithExt(ctx, claims.Ext)
		c.Request = c.Request.WithContext(ctx)

		c.Set("token", authHeader)
		c.Next()
	}
}
