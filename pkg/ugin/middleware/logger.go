package middleware

import (
	"bytes"
	"encoding/json"
	"io"
	"net/http"
	"pebble/pkg/uid"
	"pebble/pkg/ulog"
	"time"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

func TraceLogger() gin.HandlerFunc {
	return func(c *gin.Context) {
		traceID := c.GetHeader(ulog.TraceIDKey)

		if traceID == "" {
			traceID = uid.GenerateTraceId()
			c.Request.Header.Set(ulog.TraceIDKey, traceID)
		}

		ctx := ulog.ContextWithFields(c.Request.Context(), zap.String(ulog.TraceIDKey, traceID), zap.String("path", c.Request.URL.Path))
		c.Request = c.Request.WithContext(ctx)

		c.Writer.Header().Set(ulog.TraceIDKey, traceID)

		// Log basic request information
		ulog.Info(ctx, "---------- Request Started ----------",
			zap.String("method", c.Request.Method),
			zap.String("client_ip", c.Client<PERSON>()),
			zap.String("host", c.Request.Host),
			zap.String("user_agent", c.Request.UserAgent()),
			zap.String("referer", c.Request.Referer()),
			zap.String("protocol", c.Request.Proto),
		)

		// Log request headers
		// headers := make(map[string]string)
		// for k, v := range c.Request.Header {
		// 	if len(v) > 0 {
		// 		headers[k] = v[0]
		// 	}
		// }
		// ulog.Info(ctx, "Request Headers", zap.Any("headers", headers))

		start := time.Now()
		c.Next()
		duration := time.Since(start)

		// Log response information
		ulog.Info(ctx, "---------- Request Completed ----------",
			zap.Int("status", c.Writer.Status()),
			zap.Duration("duration", duration),
			zap.Int("response_size", c.Writer.Size()),
			zap.String("client_ip", c.ClientIP()),
		)
	}
}

func ParamLogger() gin.HandlerFunc {
	return func(c *gin.Context) {
		ctx := c.Request.Context()
		if c.Request.Method == http.MethodGet {
			queryParams := c.Request.URL.Query()
			ulog.Infoln(ctx, "Request Data (Params):", queryParams)
		}

		if (c.Request.Method == http.MethodPost || c.Request.Method == http.MethodPut || c.Request.Method == http.MethodDelete) &&
			!NotPrintBodyPaths[c.Request.URL.Path] {
			body, err := io.ReadAll(c.Request.Body)
			if err != nil {
				ulog.Error(ctx, "Failed to read request body", zap.Error(err))
			} else {
				// Restore the request body for further processing
				c.Request.Body = io.NopCloser(bytes.NewBuffer(body))

				// Try to parse the body as JSON
				var parsedBody interface{}
				if err := json.Unmarshal(body, &parsedBody); err == nil {
					ulog.Infoln(ctx, "Request Data (Body):", parsedBody)
				} else {
					ulog.Infoln(ctx, "Request Data (Body):", string(body))
				}
			}
		}

		c.Next()
	}

}

var NotPrintBodyPaths = map[string]bool{
	"/api/v1/auth/login":    true,
	"/api/v1/auth/register": true,
	"/health":               true,
}
