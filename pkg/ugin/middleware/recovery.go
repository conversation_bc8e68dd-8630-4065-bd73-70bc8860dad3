package middleware

import (
	"net/http"
	"pebble/pkg/ulog"
	"runtime/debug"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

func RecoveryLog() gin.HandlerFunc {
	return func(c *gin.Context) {
		defer func() {
			if err := recover(); err != nil {
				stack := string(debug.Stack())

				fields := []zap.Field{
					zap.Any("error", err),
					zap.String("method", c.Request.Method),
					zap.String("uri", c.Request.RequestURI),
					zap.String("ip", c.ClientIP()),
					zap.String("user-agent", c.Request.UserAgent()),
					zap.String("stack", stack),
				}

				ctx := c.Request.Context()
				ulog.Error(ctx, "[Recovery From Panic]", fields...)

				c.AbortWithStatus(http.StatusInternalServerError)
			}
		}()
		c.Next()
	}
}
