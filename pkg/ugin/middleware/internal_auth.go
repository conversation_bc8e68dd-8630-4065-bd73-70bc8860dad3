package middleware

import (
	"pebble/internal/webapp/config"
	"pebble/pkg/uerror"
	"pebble/pkg/ugin/writer"

	"github.com/gin-gonic/gin"
)

// InternalAuth checks for a secret header to authenticate internal API calls.
func InternalAuth() gin.HandlerFunc {
	return func(c *gin.Context) {
		secret := config.C.App.InternalApiSecret
		if secret == "" {
			// If no secret is configured, deny access as a security precaution.
			writer.ResponseErr(c, uerror.ErrInvalidToken, nil)
			c.Abort()
			return
		}

		headerSecret := c.GetHeader("X-Internal-Secret")
		if headerSecret != secret {
			writer.ResponseErr(c, uerror.ErrInvalidToken, nil)
			c.Abort()
			return
		}

		c.Next()
	}
}
