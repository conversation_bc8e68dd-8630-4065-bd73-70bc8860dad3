package validator

import (
	"pebble/pkg/localization"
	"time"

	"github.com/gin-gonic/gin/binding"
	"github.com/go-playground/validator/v10"
	"github.com/nyaruka/phonenumbers"
)

var validLanguages = map[string]bool{
	"en": true,
	"zh": true,
	"es": true,
	"pt": true,
	"fr": true,
	"de": true,
	"it": true,
	"ja": true,
	"ko": true,
	"ru": true,
	"ar": true,
	"hi": true,
	"bn": true,
	"tr": true,
	"ur": true,
	"fa": true,
	"vi": true,
	"id": true,
	"th": true,
	"ms": true,
	"tl": true,
	"pl": true,
}

func validLanguage(fl validator.FieldLevel) bool {
	lang := fl.Field().String()
	return localization.IsValidLanguageCode(lang)
}

func validPhoneNumber(fl validator.FieldLevel) bool {
	num := fl.Field().String()
	phone, err := phonenumbers.Parse(num, "")
	if err != nil {
		return false
	}
	if !phonenumbers.IsValidNumber(phone) {
		return false
	}
	return true
}

func validDate(fl validator.FieldLevel) bool {
	date := fl.Field().String()
	if date == "" {
		return false
	}

	_, err := time.Parse(time.DateOnly, date)
	return err == nil
}

func validDateTime(fl validator.FieldLevel) bool {
	date := fl.Field().String()
	if date == "" {
		return false
	}

	_, err := time.Parse(time.DateTime, date)
	return err == nil
}

func validCountry(fl validator.FieldLevel) bool {
	country := fl.Field().String()
	return localization.IsValidCountryCode(country)
}

func validCurrency(fl validator.FieldLevel) bool {
	currency := fl.Field().String()
	return localization.IsValidCurrencyCode(currency)
}

func Init() {
	if v, ok := binding.Validator.Engine().(*validator.Validate); ok {
		_ = v.RegisterValidation("phone_number", validPhoneNumber)
		_ = v.RegisterValidation("date", validDate)
		_ = v.RegisterValidation("datetime", validDateTime)
		_ = v.RegisterValidation("country", validCountry)
		_ = v.RegisterValidation("currency", validCurrency)
		_ = v.RegisterValidation("language", validLanguage)
	}
}
